# 🧪 Marketplace Authentication API Testing Guide

This guide provides comprehensive instructions for testing the marketplace authentication system through the API Gateway.

## 📋 Prerequisites

1. **Services Required**:
   - API Gateway (port 8000)
   - Workflow Service (port 8001)
   - PostgreSQL database (for workflow service)

2. **Dependencies**:
   - Python 3.8+
   - Poetry
   - curl
   - jq (optional, for JSON formatting)

## 🚀 Quick Start Testing

### Option 1: Automated Service Startup + Testing

```bash
# 1. Start all services
./start_services.sh start

# 2. Run curl-based tests
./test_marketplace_auth_curl.sh

# 3. Run Python-based tests
python test_marketplace_auth_api.py

# 4. Stop services when done
./start_services.sh stop
```

### Option 2: Manual Service Management

```bash
# Terminal 1: Start API Gateway
cd api-gateway
poetry run uvicorn app.main:app --port 8000

# Terminal 2: Start Workflow Service  
cd workflow-service
poetry run uvicorn app.main:app --port 8001

# Terminal 3: Run tests
./test_marketplace_auth_curl.sh
```

## 🔍 Test Scenarios

### 1. Authentication Summary Endpoint

**Endpoint**: `GET /marketplace/workflows/{workflow_id}/auth-summary`

**Purpose**: Get precomputed credential requirements for a workflow

**Test Command**:
```bash
curl -X GET \
  -H "Authorization: Bearer your-token-here" \
  "http://localhost:8000/marketplace/workflows/test-workflow-123/auth-summary"
```

**Expected Response**:
```json
{
  "workflow_id": "test-workflow-123",
  "env_credential_status": "pending_input",
  "credential_summary": {
    "total_requirements": 2,
    "estimated_setup_time": 6,
    "credential_requirements": [
      {
        "field_name": "openai_api_key",
        "credential_type": "api_key",
        "provider_name": "openai",
        "description": "OpenAI API key"
      },
      {
        "field_name": "github_token", 
        "credential_type": "api_key",
        "provider_name": "github",
        "description": "GitHub token"
      }
    ]
  },
  "requires_authentication": true
}
```

### 2. Import with Authentication - Analyze Action

**Endpoint**: `POST /marketplace/workflows/{workflow_id}/import-with-auth`

**Purpose**: Analyze workflow credential requirements and user coverage

**Test Command**:
```bash
curl -X POST \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{"action": "analyze"}' \
  "http://localhost:8000/marketplace/workflows/test-workflow-123/import-with-auth"
```

**Expected Response**:
```json
{
  "action": "analyzed",
  "credential_summary": {
    "total_requirements": 2,
    "estimated_setup_time": 6,
    "credential_requirements": [...]
  },
  "user_coverage": {
    "has_all_required": false,
    "missing_count": 2,
    "missing_credentials": ["openai_api_key", "github_token"]
  }
}
```

### 3. Import with Authentication - Import Action

**Endpoint**: `POST /marketplace/workflows/{workflow_id}/import-with-auth`

**Purpose**: Import workflow with credential mapping

**Test Command**:
```bash
curl -X POST \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "import",
    "credential_mapping": {
      "openai_api_key": {
        "credential_id": "cred-openai-123",
        "credential_name": "My OpenAI Key"
      },
      "github_token": {
        "credential_id": "cred-github-456", 
        "credential_name": "My GitHub Token"
      }
    }
  }' \
  "http://localhost:8000/marketplace/workflows/test-workflow-123/import-with-auth"
```

**Expected Response**:
```json
{
  "action": "imported",
  "workflow_id": "imported-workflow-456",
  "credential_summary": {...},
  "user_coverage": {...}
}
```

## 🧪 Test Scripts

### 1. Curl-based Testing (`test_marketplace_auth_curl.sh`)

**Features**:
- ✅ Service health checks
- ✅ Complete workflow testing
- ✅ Colored output
- ✅ Error handling
- ✅ Performance timing

**Usage**:
```bash
./test_marketplace_auth_curl.sh
```

### 2. Python-based Testing (`test_marketplace_auth_api.py`)

**Features**:
- ✅ Detailed response analysis
- ✅ JSON parsing and validation
- ✅ Comprehensive error reporting
- ✅ Performance metrics

**Usage**:
```bash
python test_marketplace_auth_api.py
```

## 🔧 Service Management

### Start Services
```bash
./start_services.sh start
```

### Check Service Status
```bash
./start_services.sh status
```

### Stop Services
```bash
./start_services.sh stop
```

### Restart Services
```bash
./start_services.sh restart
```

## 📊 Expected Test Results

### ✅ Success Indicators

1. **Service Health**: Both services respond to health checks
2. **Auth Summary**: Returns credential requirements with proper structure
3. **Analyze Action**: Returns analysis with user coverage information
4. **Import Action**: Successfully imports workflow with credential mapping
5. **Performance**: Response times under 100ms for precomputed data

### ⚠️ Common Issues

1. **Service Not Running**: 
   - Check if ports 8000/8001 are available
   - Verify poetry environments are set up
   - Check service logs for errors

2. **Authentication Errors**:
   - Update `AUTH_TOKEN` in test scripts
   - Verify token format and permissions

3. **Database Connection**:
   - Ensure PostgreSQL is running
   - Check database connection strings
   - Verify migrations are applied

## 🐛 Debugging

### Check Service Logs
```bash
# API Gateway logs
tail -f api-gateway/api_gateway.log

# Workflow Service logs  
tail -f workflow-service/workflow_service.log
```

### Manual Health Checks
```bash
# API Gateway health
curl http://localhost:8000/health

# Workflow Service health
curl http://localhost:8001/health
```

### Database Connection Test
```bash
cd workflow-service
poetry run python -c "
from app.database.database import get_db
db = next(get_db())
print('Database connection successful!')
"
```

## 📈 Performance Testing

### Load Testing with curl
```bash
# Test response times
for i in {1..10}; do
  time curl -s -H "Authorization: Bearer test-token" \
    "http://localhost:8000/marketplace/workflows/test-123/auth-summary" > /dev/null
done
```

### Concurrent Testing
```bash
# Test concurrent requests
seq 1 10 | xargs -n1 -P10 -I{} curl -s \
  -H "Authorization: Bearer test-token" \
  "http://localhost:8000/marketplace/workflows/test-123/auth-summary"
```

## 🎯 Success Criteria

The marketplace authentication system is working correctly if:

1. ✅ **All endpoints return 200 status codes**
2. ✅ **Response times are under 100ms for precomputed data**
3. ✅ **Credential requirements are accurately detected**
4. ✅ **Provider matching works for major services (OpenAI, GitHub, etc.)**
5. ✅ **Import workflow creates new workflow with credential mapping**
6. ✅ **Error handling works for invalid requests**

## 🔄 Continuous Testing

### Automated Testing Pipeline
```bash
#!/bin/bash
# Add to CI/CD pipeline

./start_services.sh start
sleep 10

# Run tests
./test_marketplace_auth_curl.sh
python test_marketplace_auth_api.py

# Cleanup
./start_services.sh stop
```

This comprehensive testing approach ensures the marketplace authentication system works correctly across all scenarios and edge cases.
