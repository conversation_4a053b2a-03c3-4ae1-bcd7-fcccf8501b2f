Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
clientCookies.ts:24 Client-side access token check: true
authApi.ts:349 Client-side isAuthenticated check: true
auth-provider.tsx:36 Authentication check result: true
authApi.ts:301 Making request to /users/me
axios.ts:43 [DEBUG] Added Authorization header with token (length: 349)
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 108ms
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 27ms
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 33ms
authApi.ts:304 Successfully retrieved user data
auth-provider.tsx:42 User data retrieved successfully: {id: 'c1454e90-09ac-40f2-bde2-833387d7b645', email: '<EMAIL>', fullName: '<PERSON><PERSON><PERSON>', company: 'student', department: 'Marketing', …}
credentialEnhancer.ts:80 Cache invalid or empty, fetching fresh credential data...
api.ts:2223 Using real credential API
SentryProvider.tsx:11 Sentry client-side initialized via SentryProvider.
SentryProvider.tsx:11 Sentry client-side initialized via SentryProvider.
3axios.ts:43 [DEBUG] Added Authorization header with token (length: 349)
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 33ms
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 27ms
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 33ms
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
hot-reloader-client.tsx:116 [Fast Refresh] done in 32ms
%5Broot%20of%20the%20server%5D__8c478a37._.css:1  GET http://localhost:3000/fonts/satoshi/Satoshi-Regular.ttf net::ERR_ABORTED 404 (Not Found)Understand this error
%5Broot%20of%20the%20server%5D__8c478a37._.css:1  GET http://localhost:3000/fonts/satoshi/Satoshi-Bold.ttf net::ERR_ABORTED 404 (Not Found)Understand this error
credentialEnhancer.ts:169 Credentials preloaded successfully
variableService.ts:90 Raw API response: {success: true, message: 'Variables retrieved successfully', credentials: Array(1)}
variableService.ts:91 Response structure: {success: true, message: 'Variables retrieved successfully', credentials: Array(1), credentialsType: 'object', credentialsLength: 1}
page.tsx:41 Fetched global variables: [{…}]
page.tsx:42 Variables type: object
page.tsx:43 Variables length: 1
variableService.ts:90 Raw API response: {success: true, message: 'Variables retrieved successfully', credentials: Array(1)}
variableService.ts:91 Response structure: {success: true, message: 'Variables retrieved successfully', credentials: Array(1), credentialsType: 'object', credentialsLength: 1}
page.tsx:41 Fetched global variables: [{…}]
page.tsx:42 Variables type: object
page.tsx:43 Variables length: 1
global-variable:1 The resource http://localhost:3000/_next/static/chunks/%5Broot%20of%20the%20server%5D__8c478a37._.css was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.Understand this warning