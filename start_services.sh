#!/bin/bash

# Service Startup Script for Marketplace Authentication Testing
# Starts API Gateway and Workflow Service in the background

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
API_GATEWAY_PORT=8000
WORKFLOW_SERVICE_PORT=8001
BASE_DIR="/Users/<USER>/Desktop/ruh_ai/backend"

echo -e "${BLUE}🚀 Starting Services for Marketplace Authentication Testing${NC}"
echo "=========================================================="

# Function to check if port is in use
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use (may be $service already running)${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Port $port is available${NC}"
        return 0
    fi
}

# Function to start API Gateway
start_api_gateway() {
    echo -e "${YELLOW}🔧 Starting API Gateway on port $API_GATEWAY_PORT...${NC}"
    
    cd "$BASE_DIR/api-gateway"
    
    if check_port $API_GATEWAY_PORT "API Gateway"; then
        # Start API Gateway in background
        nohup poetry run uvicorn app.main:app --host 0.0.0.0 --port $API_GATEWAY_PORT > api_gateway.log 2>&1 &
        API_GATEWAY_PID=$!
        echo $API_GATEWAY_PID > api_gateway.pid
        
        # Wait a moment for startup
        sleep 3
        
        # Check if it started successfully
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            echo -e "${GREEN}   ✅ API Gateway started (PID: $API_GATEWAY_PID)${NC}"
            echo -e "   📡 URL: http://localhost:$API_GATEWAY_PORT"
            echo -e "   📄 Logs: $BASE_DIR/api-gateway/api_gateway.log"
            return 0
        else
            echo -e "${RED}   ❌ API Gateway failed to start${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}   ⚠️  Skipping API Gateway startup${NC}"
        return 0
    fi
}

# Function to start Workflow Service
start_workflow_service() {
    echo -e "${YELLOW}🔧 Starting Workflow Service on port $WORKFLOW_SERVICE_PORT...${NC}"
    
    cd "$BASE_DIR/workflow-service"
    
    if check_port $WORKFLOW_SERVICE_PORT "Workflow Service"; then
        # Start Workflow Service in background
        nohup poetry run uvicorn app.main:app --host 0.0.0.0 --port $WORKFLOW_SERVICE_PORT > workflow_service.log 2>&1 &
        WORKFLOW_SERVICE_PID=$!
        echo $WORKFLOW_SERVICE_PID > workflow_service.pid
        
        # Wait a moment for startup
        sleep 3
        
        # Check if it started successfully
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            echo -e "${GREEN}   ✅ Workflow Service started (PID: $WORKFLOW_SERVICE_PID)${NC}"
            echo -e "   📡 URL: http://localhost:$WORKFLOW_SERVICE_PORT"
            echo -e "   📄 Logs: $BASE_DIR/workflow-service/workflow_service.log"
            return 0
        else
            echo -e "${RED}   ❌ Workflow Service failed to start${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}   ⚠️  Skipping Workflow Service startup${NC}"
        return 0
    fi
}

# Function to check service health
check_service_health() {
    local url=$1
    local name=$2
    
    echo -e "${YELLOW}🔍 Checking $name health...${NC}"
    
    # Wait up to 30 seconds for service to be ready
    for i in {1..10}; do
        if curl -s --connect-timeout 3 "$url/health" > /dev/null 2>&1; then
            echo -e "${GREEN}   ✅ $name is healthy${NC}"
            return 0
        else
            echo -e "   ⏳ Waiting for $name... (attempt $i/10)"
            sleep 3
        fi
    done
    
    echo -e "${RED}   ❌ $name health check failed${NC}"
    return 1
}

# Function to stop services
stop_services() {
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    
    # Stop API Gateway
    if [ -f "$BASE_DIR/api-gateway/api_gateway.pid" ]; then
        API_GATEWAY_PID=$(cat "$BASE_DIR/api-gateway/api_gateway.pid")
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            kill $API_GATEWAY_PID
            echo -e "${GREEN}   ✅ API Gateway stopped${NC}"
        fi
        rm -f "$BASE_DIR/api-gateway/api_gateway.pid"
    fi
    
    # Stop Workflow Service
    if [ -f "$BASE_DIR/workflow-service/workflow_service.pid" ]; then
        WORKFLOW_SERVICE_PID=$(cat "$BASE_DIR/workflow-service/workflow_service.pid")
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            kill $WORKFLOW_SERVICE_PID
            echo -e "${GREEN}   ✅ Workflow Service stopped${NC}"
        fi
        rm -f "$BASE_DIR/workflow-service/workflow_service.pid"
    fi
}

# Function to show service status
show_status() {
    echo -e "${BLUE}📊 Service Status${NC}"
    echo "=================="
    
    # Check API Gateway
    if [ -f "$BASE_DIR/api-gateway/api_gateway.pid" ]; then
        API_GATEWAY_PID=$(cat "$BASE_DIR/api-gateway/api_gateway.pid")
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            echo -e "API Gateway: ${GREEN}✅ Running${NC} (PID: $API_GATEWAY_PID, Port: $API_GATEWAY_PORT)"
        else
            echo -e "API Gateway: ${RED}❌ Not Running${NC}"
        fi
    else
        echo -e "API Gateway: ${RED}❌ Not Running${NC}"
    fi
    
    # Check Workflow Service
    if [ -f "$BASE_DIR/workflow-service/workflow_service.pid" ]; then
        WORKFLOW_SERVICE_PID=$(cat "$BASE_DIR/workflow-service/workflow_service.pid")
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            echo -e "Workflow Service: ${GREEN}✅ Running${NC} (PID: $WORKFLOW_SERVICE_PID, Port: $WORKFLOW_SERVICE_PORT)"
        else
            echo -e "Workflow Service: ${RED}❌ Not Running${NC}"
        fi
    else
        echo -e "Workflow Service: ${RED}❌ Not Running${NC}"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        "start")
            echo -e "${BLUE}🚀 Starting services...${NC}"
            echo ""
            
            # Start services
            start_api_gateway
            echo ""
            start_workflow_service
            echo ""
            
            # Health checks
            echo -e "${BLUE}🔍 Health checks...${NC}"
            check_service_health "http://localhost:$API_GATEWAY_PORT" "API Gateway"
            check_service_health "http://localhost:$WORKFLOW_SERVICE_PORT" "Workflow Service"
            echo ""
            
            # Show final status
            show_status
            echo ""
            
            echo -e "${GREEN}🎉 Services are ready for testing!${NC}"
            echo ""
            echo -e "${BLUE}💡 Next steps:${NC}"
            echo "   1. Run API tests: ./test_marketplace_auth_curl.sh"
            echo "   2. Run Python tests: python test_marketplace_auth_api.py"
            echo "   3. Check logs: tail -f api-gateway/api_gateway.log"
            echo "   4. Stop services: ./start_services.sh stop"
            ;;
        "stop")
            stop_services
            ;;
        "status")
            show_status
            ;;
        "restart")
            stop_services
            sleep 2
            main start
            ;;
        *)
            echo "Usage: $0 {start|stop|status|restart}"
            echo ""
            echo "Commands:"
            echo "  start   - Start API Gateway and Workflow Service"
            echo "  stop    - Stop all services"
            echo "  status  - Show service status"
            echo "  restart - Restart all services"
            exit 1
            ;;
    esac
}

# Handle Ctrl+C
trap 'echo -e "\n${YELLOW}🛑 Interrupted. Stopping services...${NC}"; stop_services; exit 1' INT

# Run main function
main "$@"
