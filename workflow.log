e': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_2', 'display_name': 'Input 2', 'info': 'Text for input 2. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '2', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_3', 'display_name': 'Input 3', 'info': 'Text for input 3. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_4', 'display_name': 'Input 4', 'info': 'Text for input 4. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_5', 'display_name': 'Input 5', 'info': 'Text for input 5. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_6', 'display_name': 'Input 6', 'info': 'Text for input 6. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_7', 'display_name': 'Input 7', 'info': 'Text for input 7. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_8', 'display_name': 'Input 8', 'info': 'Text for input 8. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_9', 'display_name': 'Input 9', 'info': 'Text for input 9. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_10', 'display_name': 'Input 10', 'info': 'Text for input 10. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'result', 'display_name': 'Combined Text', 'output_type': 'string', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.processing.combinetextcomponent', 'interface_issues': []}, 'config': {'main_input': '', 'num_additional_inputs': 0, 'separator': '', 'input_1': '', 'input_2': '', 'input_3': '', 'input_4': '', 'input_5': '', 'input_6': '', 'input_7': '', 'input_8': '', 'input_9': '', 'input_10': ''}}, 'width': 208, 'height': 184, 'selected': False, 'dragging': False, 'style': {'opacity': 1}}], 'edges': [{'id': 'reactflow__edge-LoopNode-1753852849426current_item-CombineTextComponent-1753852886677input_1', 'source': 'LoopNode-1753852849426', 'sourceHandle': 'current_item', 'target': 'CombineTextComponent-1753852886677', 'targetHandle': 'input_1', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}, {'id': 'reactflow__edge-start-nodeflow-LoopNode-1753852849426end', 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'LoopNode-1753852849426', 'targetHandle': 'end', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}, {'id': 'reactflow__edge-start-nodeflow-CombineTextComponent-1753875300927main_input', 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'CombineTextComponent-1753875300927', 'targetHandle': 'main_input', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}, {'id': 'reactflow__edge-CombineTextComponent-1753875300927result-LoopNode-1753852849426start', 'source': 'CombineTextComponent-1753875300927', 'sourceHandle': 'result', 'target': 'LoopNode-1753852849426', 'targetHandle': 'start', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}], 'unconnected_nodes': [{'id': 'SelectDataComponent-1753852841462', 'type': 'WorkflowNode', 'position': {'x': 520, 'y': 380}, 'data': {'label': 'Select Data', 'type': 'component', 'originalType': 'SelectDataComponent', 'definition': {'name': 'SelectDataComponent', 'display_name': 'Select Data', 'description': 'Extracts elements from lists or dictionaries.', 'category': 'Processing', 'icon': 'Filter', 'beta': False, 'requires_approval': False, 'inputs': [{'name': 'input_data', 'display_name': 'Input Data', 'info': 'The data to select from. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['list', 'dict', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'data_type', 'display_name': 'Data Type', 'info': 'The type of data structure to select from.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'Auto-Detect', 'options': ['Auto-Detect', 'List', 'Dictionary'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'search_mode', 'display_name': 'Search Mode', 'info': "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'Exact Path', 'options': ['Exact Path', 'Smart Search'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'field_matching_mode', 'display_name': 'Field Matching Mode', 'info': "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'Auto-detect', 'options': ['Auto-detect', 'Key-based Only', 'Property-based Only'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'selector', 'display_name': 'Selector', 'info': "Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'output_data', 'display_name': 'Selected Data', 'output_type': 'Any', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.processing.selectdatacomponent', 'interface_issues': []}, 'config': {'input_data': {}, 'data_type': 'Auto-Detect', 'search_mode': 'Exact Path', 'field_matching_mode': 'Auto-detect', 'selector': ''}}, 'width': 208, 'height': 194, 'selected': False, 'dragging': False, 'style': {'opacity': 0.5}}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/64521189-cfad-45a8-8993-49ac950e4716.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/64521189-cfad-45a8-8993-49ac950e4716.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges', 'unconnected_nodes']
[DEBUG] Number of nodes: 4
[DEBUG] Number of edges: 4
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=LoopNode-1753852849426, type=loop, originalType=LoopNode
[DEBUG] Node 2: id=CombineTextComponent-1753852886677, type=component, originalType=CombineTextComponent
[DEBUG] Node 3: id=CombineTextComponent-1753875300927, type=component, originalType=CombineTextComponent
[DEBUG] Edge 0: id=reactflow__edge-LoopNode-1753852849426current_item-CombineTextComponent-1753852886677input_1, source=LoopNode-1753852849426, target=CombineTextComponent-1753852886677, sourceHandle=current_item
[DEBUG] Edge 1: id=reactflow__edge-start-nodeflow-LoopNode-1753852849426end, source=start-node, target=LoopNode-1753852849426, sourceHandle=flow
[DEBUG] Edge 2: id=reactflow__edge-start-nodeflow-CombineTextComponent-1753875300927main_input, source=start-node, target=CombineTextComponent-1753875300927, sourceHandle=flow
[DEBUG] Edge 3: id=reactflow__edge-CombineTextComponent-1753875300927result-LoopNode-1753852849426start, source=CombineTextComponent-1753875300927, target=LoopNode-1753852849426, sourceHandle=result
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - LoopNode-1753852849426
   - CombineTextComponent-1753875300927
✅ Found 2 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels
   - Level 0: ['CombineTextComponent-1753875300927']
   - Level 1: ['LoopNode-1753852849426']
   - Level 2: ['CombineTextComponent-1753852886677']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['CombineTextComponent-1753875300927']

   📦 Processing level-0 node: CombineTextComponent-1753875300927
      Type: CombineTextComponent (component)
      ✅ PROCESSING: Available node (transition_id: transition-CombineTextComponent-1753875300927)
      📄 COMPONENT: CombineTextComponent
      ✅ ADDED: To available_nodes array (position #1)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['LoopNode-1753852849426']

   📦 Processing level-1 node: LoopNode-1753852849426
      Type: LoopNode (loop)
      ⏭️  SKIPPED: Invalid node type (loop)

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['CombineTextComponent-1753852886677']

   📦 Processing level-2 node: CombineTextComponent-1753852886677
      Type: CombineTextComponent (component)
      ✅ PROCESSING: Available node (transition_id: transition-CombineTextComponent-1753852886677)
      📄 COMPONENT: CombineTextComponent
      ✅ ADDED: To available_nodes array (position #2)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 3
   - Available nodes extracted: 2
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. CombineTextComponent (component) -> transition-CombineTextComponent-1753875300927
     2. CombineTextComponent (component) -> transition-CombineTextComponent-1753852886677
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 0 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 4
   - Edges: 4
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(LoopNode-1753852849426): node_type='loop', original_type='LoopNode', result=False
[DEBUG] is_conditional_node(CombineTextComponent-1753852886677): node_type='component', original_type='CombineTextComponent', result=False
[DEBUG] is_conditional_node(CombineTextComponent-1753875300927): node_type='component', original_type='CombineTextComponent', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - LoopNode: 1
   - CombineTextComponent: 2
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - LoopNode-1753852849426
   - CombineTextComponent-1753875300927
✅ Found 2 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - Edge mappings: 4
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/4: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/4: LoopNode-1753852849426
   Type: LoopNode (loop)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(LoopNode-1753852849426): node_type='loop', original_type='LoopNode', result=False
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/4: CombineTextComponent-1753852886677
   Type: CombineTextComponent (component)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(CombineTextComponent-1753852886677): node_type='component', original_type='CombineTextComponent', result=False
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 4/4: CombineTextComponent-1753875300927
   Type: CombineTextComponent (component)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(CombineTextComponent-1753875300927): node_type='component', original_type='CombineTextComponent', result=False
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   Combined 3 → 2 nodes (removed 1 duplicates)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 3 ['LoopNode-1753852849426', 'CombineTextComponent-1753852886677', 'CombineTextComponent-1753875300927']
   - Final transition_nodes count: 2
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['CombineTextComponent-1753875300927']

   📦 Processing node: CombineTextComponent-1753875300927
      Type: CombineTextComponent (component)
[DEBUG] is_conditional_node(CombineTextComponent-1753875300927): node_type='component', original_type='CombineTextComponent', result=False
      Is conditional: False
[DEBUG] is_output_node(CombineTextComponent-1753875300927): node_type='component', result=False
🔍 Analyzing loop body chains for loop LoopNode-1753852849426:
   Entry transitions: ['transition-CombineTextComponent-1753852886677']
   Loop body nodes: ['CombineTextComponent-1753852886677']
   Following chain from entry: transition-CombineTextComponent-1753852886677
     🔍 Following chain from transition-CombineTextComponent-1753852886677
     Starting with node: CombineTextComponent-1753852886677
     Visiting node: CombineTextComponent-1753852886677
     Outgoing edges: []
     Chain edges (excluding loop): []
     ✅ Found exit transition: transition-CombineTextComponent-1753852886677
   Chain exit(s) found: transition-CombineTextComponent-1753852886677
   Final exit transitions: ['transition-CombineTextComponent-1753852886677']
   Final exit transitions (outside loop body): []
[DEBUG] is_conditional_node(CombineTextComponent-1753875300927): node_type='component', original_type='CombineTextComponent', result=False
[DEBUG] is_conditional_node(LoopNode-1753852849426): node_type='loop', original_type='LoopNode', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-CombineTextComponent-1753875300927
         - Sequence: 1
         - Execution Type: Components
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['LoopNode-1753852849426']

   📦 Processing node: LoopNode-1753852849426
      Type: LoopNode (loop)
[DEBUG] is_conditional_node(LoopNode-1753852849426): node_type='loop', original_type='LoopNode', result=False
      Is conditional: False
[DEBUG] is_output_node(LoopNode-1753852849426): node_type='loop', result=False
🔍 Analyzing loop body chains for loop LoopNode-1753852849426:
   Entry transitions: ['transition-CombineTextComponent-1753852886677']
   Loop body nodes: ['CombineTextComponent-1753852886677']
   Following chain from entry: transition-CombineTextComponent-1753852886677
     🔍 Following chain from transition-CombineTextComponent-1753852886677
     Starting with node: CombineTextComponent-1753852886677
     Visiting node: CombineTextComponent-1753852886677
     Outgoing edges: []
     Chain edges (excluding loop): []
     ✅ Found exit transition: transition-CombineTextComponent-1753852886677
   Chain exit(s) found: transition-CombineTextComponent-1753852886677
   Final exit transitions: ['transition-CombineTextComponent-1753852886677']
   Final exit transitions (outside loop body): []
[DEBUG] is_conditional_node(LoopNode-1753852849426): node_type='loop', original_type='LoopNode', result=False
Transition LoopNode-1753852849426 marked as 'initial' but has 1 input dependencies. Changing transition_type to 'standard' to prevent execution conflicts.
🔍 Analyzing loop body chains for loop LoopNode-1753852849426:
   Entry transitions: ['transition-CombineTextComponent-1753852886677']
   Loop body nodes: ['CombineTextComponent-1753852886677']
   Following chain from entry: transition-CombineTextComponent-1753852886677
     🔍 Following chain from transition-CombineTextComponent-1753852886677
     Starting with node: CombineTextComponent-1753852886677
     Visiting node: CombineTextComponent-1753852886677
     Outgoing edges: []
     Chain edges (excluding loop): []
     ✅ Found exit transition: transition-CombineTextComponent-1753852886677
   Chain exit(s) found: transition-CombineTextComponent-1753852886677
   Final exit transitions: ['transition-CombineTextComponent-1753852886677']
   Final exit transitions (outside loop body): []

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['CombineTextComponent-1753852886677']

   📦 Processing node: CombineTextComponent-1753852886677
      Type: CombineTextComponent (component)
[DEBUG] is_conditional_node(CombineTextComponent-1753852886677): node_type='component', original_type='CombineTextComponent', result=False
      Is conditional: False
[DEBUG] is_output_node(CombineTextComponent-1753852886677): node_type='component', result=False
      🔧 Creating transition for node without outgoing edges...
[DEBUG] is_conditional_node(CombineTextComponent-1753852886677): node_type='component', original_type='CombineTextComponent', result=False
      ✅ TRANSITION CREATED FOR NODE WITHOUT OUTGOING EDGES:
         - ID: transition-CombineTextComponent-1753852886677
         - Sequence: 3
         - Execution Type: Components
         - Tools: 1
         - Is End: False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 2
   - Total transitions created: 3
   - Conditional transitions: 0
   - Regular transitions: 2

⚙️  REGULAR TRANSITIONS CREATED:
   - CombineTextComponent-1753875300927: 1 tools
   - CombineTextComponent-1753852886677: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'LoopNode', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'LoopNode', 'input_schema': {'predefined_fields': [{'field_name': 'source_type', 'data_type': {'type': 'string', 'description': 'Choose whether to iterate over a list of items or a number range.'}, 'required': False}, {'field_name': 'iteration_list', 'data_type': {'type': 'array', 'description': 'The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.'}, 'required': False}, {'field_name': 'batch_size', 'data_type': {'type': 'string', 'description': 'Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.'}, 'required': False}, {'field_name': 'start', 'data_type': {'type': 'string', 'description': 'Starting number for the range. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'end', 'data_type': {'type': 'string', 'description': 'Ending number for the range (inclusive). Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'step', 'data_type': {'type': 'string', 'description': 'Step size for the range (default: 1). Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'parallel_execution', 'data_type': {'type': 'boolean', 'description': 'Execute loop iterations in parallel for better performance.'}, 'required': False}, {'field_name': 'max_concurrent', 'data_type': {'type': 'number', 'description': 'Maximum number of iterations to run concurrently (1-20).'}, 'required': False}, {'field_name': 'preserve_order', 'data_type': {'type': 'boolean', 'description': 'Maintain the original order of items in the results.'}, 'required': False}, {'field_name': 'iteration_timeout', 'data_type': {'type': 'number', 'description': 'Maximum time to wait for each iteration to complete (1-3600 seconds).'}, 'required': False}, {'field_name': 'aggregation_type', 'data_type': {'type': 'string', 'description': 'How to aggregate results from all iterations.'}, 'required': False}, {'field_name': 'include_metadata', 'data_type': {'type': 'boolean', 'description': 'Include metadata (timing, iteration index, etc.) in results.'}, 'required': False}, {'field_name': 'on_iteration_error', 'data_type': {'type': 'string', 'description': 'How to handle errors in individual iterations.'}, 'required': False}, {'field_name': 'include_errors', 'data_type': {'type': 'boolean', 'description': 'Include error information in the final results.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'current_item', 'data_type': {'type': 'object', 'description': '', 'format': 'string'}}, {'field_name': 'final_results', 'data_type': {'type': 'array', 'description': '', 'format': 'string'}}]}}]}, {'id': 'CombineTextComponent', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'CombineTextComponent', 'input_schema': {'predefined_fields': [{'field_name': 'main_input', 'data_type': {'type': 'string', 'description': 'The main text or list to combine. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'num_additional_inputs', 'data_type': {'type': 'number', 'description': 'Set the number of additional text inputs to show (1-10).'}, 'required': False}, {'field_name': 'separator', 'data_type': {'type': 'string', 'description': 'The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.'}, 'required': False}, {'field_name': 'input_1', 'data_type': {'type': 'string', 'description': 'Text for input 1. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_2', 'data_type': {'type': 'string', 'description': 'Text for input 2. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_3', 'data_type': {'type': 'string', 'description': 'Text for input 3. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_4', 'data_type': {'type': 'string', 'description': 'Text for input 4. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_5', 'data_type': {'type': 'string', 'description': 'Text for input 5. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_6', 'data_type': {'type': 'string', 'description': 'Text for input 6. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_7', 'data_type': {'type': 'string', 'description': 'Text for input 7. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_8', 'data_type': {'type': 'string', 'description': 'Text for input 8. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_9', 'data_type': {'type': 'string', 'description': 'Text for input 9. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_10', 'data_type': {'type': 'string', 'description': 'Text for input 10. Can be connected from another node or entered directly.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'result', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-CombineTextComponent-1753875300927', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'Components', 'node_label': 'Combine Text', 'node_info': {'node_id': 'CombineTextComponent', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'CombineTextComponent', 'tool_params': {'items': [{'field_name': 'main_input', 'data_type': 'string', 'field_value': ''}, {'field_name': 'num_additional_inputs', 'data_type': 'number', 'field_value': 0}, {'field_name': 'separator', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_1', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_2', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_3', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_4', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_5', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_6', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_7', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_8', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_9', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_10', 'data_type': 'string', 'field_value': ''}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-LoopNode-1753852849426', 'target_node_id': 'For Each Loop', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'result', 'result_path': 'result', 'edge_id': 'reactflow__edge-CombineTextComponent-1753875300927result-LoopNode-1753852849426start'}]}}]}, 'result_resolution': {'node_type': 'component', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'main_input', 'handle_name': 'Main Input', 'data_type': 'string', 'required': False, 'description': 'The main text or list to combine. Can be connected from another node or entered directly.'}, {'handle_id': 'input_1', 'handle_name': 'Input 1', 'data_type': 'string', 'required': False, 'description': 'Text for input 1. Can be connected from another node or entered directly.'}, {'handle_id': 'input_2', 'handle_name': 'Input 2', 'data_type': 'string', 'required': False, 'description': 'Text for input 2. Can be connected from another node or entered directly.'}, {'handle_id': 'input_3', 'handle_name': 'Input 3', 'data_type': 'string', 'required': False, 'description': 'Text for input 3. Can be connected from another node or entered directly.'}, {'handle_id': 'input_4', 'handle_name': 'Input 4', 'data_type': 'string', 'required': False, 'description': 'Text for input 4. Can be connected from another node or entered directly.'}, {'handle_id': 'input_5', 'handle_name': 'Input 5', 'data_type': 'string', 'required': False, 'description': 'Text for input 5. Can be connected from another node or entered directly.'}, {'handle_id': 'input_6', 'handle_name': 'Input 6', 'data_type': 'string', 'required': False, 'description': 'Text for input 6. Can be connected from another node or entered directly.'}, {'handle_id': 'input_7', 'handle_name': 'Input 7', 'data_type': 'string', 'required': False, 'description': 'Text for input 7. Can be connected from another node or entered directly.'}, {'handle_id': 'input_8', 'handle_name': 'Input 8', 'data_type': 'string', 'required': False, 'description': 'Text for input 8. Can be connected from another node or entered directly.'}, {'handle_id': 'input_9', 'handle_name': 'Input 9', 'data_type': 'string', 'required': False, 'description': 'Text for input 9. Can be connected from another node or entered directly.'}, {'handle_id': 'input_10', 'handle_name': 'Input 10', 'data_type': 'string', 'required': False, 'description': 'Text for input 10. Can be connected from another node or entered directly.'}], 'output_handles': [{'handle_id': 'result', 'handle_name': 'Combined Text', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'result': 'result', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.result', 'output_data.result', 'response.result', 'data.result', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'result'}}, 'approval_required': False, 'end': False}, {'id': 'transition-LoopNode-1753852849426', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'loop', 'node_label': 'For Each Loop', 'node_info': {'node_id': 'LoopNode', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'LoopNode', 'tool_params': {'items': [{'field_name': 'source_type', 'data_type': 'string', 'field_value': 'number_range'}, {'field_name': 'iteration_list', 'data_type': 'array', 'field_value': []}, {'field_name': 'batch_size', 'data_type': 'string', 'field_value': '1'}, {'field_name': 'start', 'data_type': 'string', 'field_value': None}, {'field_name': 'end', 'data_type': 'string', 'field_value': '7'}, {'field_name': 'step', 'data_type': 'string', 'field_value': '1'}, {'field_name': 'parallel_execution', 'data_type': 'boolean', 'field_value': True}, {'field_name': 'max_concurrent', 'data_type': 'number', 'field_value': 3}, {'field_name': 'preserve_order', 'data_type': 'boolean', 'field_value': True}, {'field_name': 'iteration_timeout', 'data_type': 'number', 'field_value': 60}, {'field_name': 'aggregation_type', 'data_type': 'string', 'field_value': 'collect_all'}, {'field_name': 'include_metadata', 'data_type': 'boolean', 'field_value': True}, {'field_name': 'on_iteration_error', 'data_type': 'string', 'field_value': 'continue'}, {'field_name': 'include_errors', 'data_type': 'boolean', 'field_value': True}]}}], 'input_data': [{'from_transition_id': 'transition-CombineTextComponent-1753875300927', 'source_node_id': 'Combine Text', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-CombineTextComponent-1753875300927', 'source_handle_id': 'result', 'target_handle_id': 'start', 'edge_id': 'reactflow__edge-CombineTextComponent-1753875300927result-LoopNode-1753852849426start'}]}], 'output_data': [{'to_transition_id': 'transition-CombineTextComponent-1753852886677', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-1753852849426current_item-CombineTextComponent-1753852886677input_1'}]}}]}, 'result_resolution': {'node_type': 'loop', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'iteration_list', 'handle_name': 'Iteration List', 'data_type': 'array', 'required': False, 'description': 'The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.'}, {'handle_id': 'batch_size', 'handle_name': 'Batch Size', 'data_type': 'string', 'required': False, 'description': 'Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.'}, {'handle_id': 'start', 'handle_name': 'Start Number', 'data_type': 'string', 'required': False, 'description': 'Starting number for the range. Can be connected from another node or entered directly.'}, {'handle_id': 'end', 'handle_name': 'End Number', 'data_type': 'string', 'required': False, 'description': 'Ending number for the range (inclusive). Can be connected from another node or entered directly.'}, {'handle_id': 'step', 'handle_name': 'Step Size', 'data_type': 'string', 'required': False, 'description': 'Step size for the range (default: 1). Can be connected from another node or entered directly.'}], 'output_handles': [{'handle_id': 'current_item', 'handle_name': 'Current Item (Iteration Output)', 'data_type': 'object', 'description': 'The current item being processed in the loop iteration'}, {'handle_id': 'final_results', 'handle_name': 'All Results (Exit Output)', 'data_type': 'array', 'description': 'Aggregated results from all loop iterations'}]}, 'result_path_hints': {'current_item': 'current_item', 'final_results': 'final_results'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.current_item', 'output_data.current_item', 'response.current_item', 'data.current_item', 'result.final_results', 'output_data.final_results', 'response.final_results', 'data.final_results', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'current_item'}}, 'approval_required': False, 'end': True, 'loop_config': {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': None, 'end': 7}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753852886677'], 'exit_transitions': ['transition-CombineTextComponent-1753852886677'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}}, {'id': 'transition-CombineTextComponent-1753852886677', 'sequence': 3, 'transition_type': 'standard', 'execution_type': 'Components', 'node_label': 'Combine Text', 'node_info': {'node_id': 'CombineTextComponent', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'CombineTextComponent', 'tool_params': {'items': [{'field_name': 'main_input', 'data_type': 'string', 'field_value': 'ran'}, {'field_name': 'num_additional_inputs', 'data_type': 'number', 'field_value': 1}, {'field_name': 'separator', 'data_type': 'string', 'field_value': ' '}, {'field_name': 'input_1', 'data_type': 'string', 'field_value': None}, {'field_name': 'input_2', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_3', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_4', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_5', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_6', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_7', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_8', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_9', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_10', 'data_type': 'string', 'field_value': ''}]}}], 'input_data': [{'from_transition_id': 'transition-LoopNode-1753852849426', 'source_node_id': 'For Each Loop', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-LoopNode-1753852849426', 'source_handle_id': 'current_item', 'target_handle_id': 'input_1', 'edge_id': 'reactflow__edge-LoopNode-1753852849426current_item-CombineTextComponent-1753852886677input_1'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'component', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'main_input', 'handle_name': 'Main Input', 'data_type': 'string', 'required': False, 'description': 'The main text or list to combine. Can be connected from another node or entered directly.'}, {'handle_id': 'input_1', 'handle_name': 'Input 1', 'data_type': 'string', 'required': False, 'description': 'Text for input 1. Can be connected from another node or entered directly.'}, {'handle_id': 'input_2', 'handle_name': 'Input 2', 'data_type': 'string', 'required': False, 'description': 'Text for input 2. Can be connected from another node or entered directly.'}, {'handle_id': 'input_3', 'handle_name': 'Input 3', 'data_type': 'string', 'required': False, 'description': 'Text for input 3. Can be connected from another node or entered directly.'}, {'handle_id': 'input_4', 'handle_name': 'Input 4', 'data_type': 'string', 'required': False, 'description': 'Text for input 4. Can be connected from another node or entered directly.'}, {'handle_id': 'input_5', 'handle_name': 'Input 5', 'data_type': 'string', 'required': False, 'description': 'Text for input 5. Can be connected from another node or entered directly.'}, {'handle_id': 'input_6', 'handle_name': 'Input 6', 'data_type': 'string', 'required': False, 'description': 'Text for input 6. Can be connected from another node or entered directly.'}, {'handle_id': 'input_7', 'handle_name': 'Input 7', 'data_type': 'string', 'required': False, 'description': 'Text for input 7. Can be connected from another node or entered directly.'}, {'handle_id': 'input_8', 'handle_name': 'Input 8', 'data_type': 'string', 'required': False, 'description': 'Text for input 8. Can be connected from another node or entered directly.'}, {'handle_id': 'input_9', 'handle_name': 'Input 9', 'data_type': 'string', 'required': False, 'description': 'Text for input 9. Can be connected from another node or entered directly.'}, {'handle_id': 'input_10', 'handle_name': 'Input 10', 'data_type': 'string', 'required': False, 'description': 'Text for input 10. Can be connected from another node or entered directly.'}], 'output_handles': [{'handle_id': 'result', 'handle_name': 'Combined Text', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'result': 'result', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.result', 'output_data.result', 'response.result', 'data.result', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'result'}}, 'approval_required': False, 'end': False}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/00ddc37c-b24b-44db-8c65-926ca2fdb803.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/00ddc37c-b24b-44db-8c65-926ca2fdb803.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - LoopNode-1753852849426
   - CombineTextComponent-1753875300927
✅ Found 2 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels
   - Level 0: ['CombineTextComponent-1753875300927']
   - Level 1: ['LoopNode-1753852849426']
   - Level 2: ['CombineTextComponent-1753852886677']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['CombineTextComponent-1753875300927']

   📦 Processing level-0 node: CombineTextComponent-1753875300927
      Type: CombineTextComponent (component)
      ✅ PROCESSING: Available node (transition_id: transition-CombineTextComponent-1753875300927)
      📄 COMPONENT: CombineTextComponent
      ✅ ADDED: To available_nodes array (position #1)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['LoopNode-1753852849426']

   📦 Processing level-1 node: LoopNode-1753852849426
      Type: LoopNode (loop)
      ⏭️  SKIPPED: Invalid node type (loop)

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['CombineTextComponent-1753852886677']

   📦 Processing level-2 node: CombineTextComponent-1753852886677
      Type: CombineTextComponent (component)
      ✅ PROCESSING: Available node (transition_id: transition-CombineTextComponent-1753852886677)
      📄 COMPONENT: CombineTextComponent
      ✅ ADDED: To available_nodes array (position #2)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 3
   - Available nodes extracted: 2
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. CombineTextComponent (component) -> transition-CombineTextComponent-1753875300927
     2. CombineTextComponent (component) -> transition-CombineTextComponent-1753852886677
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-07-30 17:11:57 [info     ] Set is_updated=True for workflow 4cf0e8e9-620f-4961-95b5-38b22b6ff58e due to version-relevant changes
2025-07-30 17:11:58 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
