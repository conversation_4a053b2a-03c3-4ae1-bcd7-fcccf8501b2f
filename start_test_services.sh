#!/bin/bash

# Test Services Startup Script
# Starts API Gateway and Workflow Service with test database configuration

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
API_GATEWAY_PORT=8000
WORKFLOW_SERVICE_PORT=8001
BASE_DIR="/Users/<USER>/Desktop/ruh_ai/backend"
TEST_DB_NAME="workflow_service_test"

echo -e "${BLUE}🧪 Starting Test Services for Marketplace Authentication${NC}"
echo "========================================================"

# Function to check if port is in use
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use (may be $service already running)${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Port $port is available${NC}"
        return 0
    fi
}

# Function to check test database
check_test_database() {
    echo -e "${YELLOW}🔍 Checking test database...${NC}"
    
    if psql -d $TEST_DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Test database $TEST_DB_NAME is accessible${NC}"
        
        # Check if workflows table exists
        TABLE_EXISTS=$(psql -d $TEST_DB_NAME -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'workflows');" | xargs)
        
        if [ "$TABLE_EXISTS" = "t" ]; then
            echo -e "${GREEN}   ✅ Workflows table exists${NC}"
            
            # Check workflow count
            WORKFLOW_COUNT=$(psql -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM workflows;" | xargs)
            echo -e "   📊 Test workflows in database: $WORKFLOW_COUNT"
            
            return 0
        else
            echo -e "${RED}   ❌ Workflows table not found${NC}"
            echo -e "   💡 Run: ./setup_test_database.sh"
            return 1
        fi
    else
        echo -e "${RED}   ❌ Test database $TEST_DB_NAME not accessible${NC}"
        echo -e "   💡 Run: ./setup_test_database.sh"
        return 1
    fi
}

# Function to start API Gateway with test environment
start_test_api_gateway() {
    echo -e "${YELLOW}🔧 Starting API Gateway (Test Mode) on port $API_GATEWAY_PORT...${NC}"
    
    cd "$BASE_DIR/api-gateway"
    
    if check_port $API_GATEWAY_PORT "API Gateway"; then
        # Check if test environment file exists
        if [ ! -f ".env.test" ]; then
            echo -e "${RED}   ❌ Test environment file not found${NC}"
            echo -e "   💡 Run: ./setup_test_database.sh"
            return 1
        fi
        
        # Start API Gateway with test environment
        echo -e "   🔧 Using test environment configuration"
        nohup poetry run uvicorn app.main:app --host 0.0.0.0 --port $API_GATEWAY_PORT --env-file .env.test > api_gateway_test.log 2>&1 &
        API_GATEWAY_PID=$!
        echo $API_GATEWAY_PID > api_gateway_test.pid
        
        # Wait for startup
        sleep 3
        
        # Check if it started successfully
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            echo -e "${GREEN}   ✅ API Gateway (Test) started (PID: $API_GATEWAY_PID)${NC}"
            echo -e "   📡 URL: http://localhost:$API_GATEWAY_PORT"
            echo -e "   📄 Logs: $BASE_DIR/api-gateway/api_gateway_test.log"
            echo -e "   🧪 Environment: TEST"
            return 0
        else
            echo -e "${RED}   ❌ API Gateway failed to start${NC}"
            echo -e "   📄 Check logs: cat $BASE_DIR/api-gateway/api_gateway_test.log"
            return 1
        fi
    else
        echo -e "${YELLOW}   ⚠️  Skipping API Gateway startup${NC}"
        return 0
    fi
}

# Function to start Workflow Service with test database
start_test_workflow_service() {
    echo -e "${YELLOW}🔧 Starting Workflow Service (Test DB) on port $WORKFLOW_SERVICE_PORT...${NC}"
    
    cd "$BASE_DIR/workflow-service"
    
    if check_port $WORKFLOW_SERVICE_PORT "Workflow Service"; then
        # Check if test environment file exists
        if [ ! -f ".env.test" ]; then
            echo -e "${RED}   ❌ Test environment file not found${NC}"
            echo -e "   💡 Run: ./setup_test_database.sh"
            return 1
        fi
        
        # Start Workflow Service with test database
        echo -e "   🗄️  Using test database: $TEST_DB_NAME"
        nohup poetry run uvicorn app.main:app --host 0.0.0.0 --port $WORKFLOW_SERVICE_PORT --env-file .env.test > workflow_service_test.log 2>&1 &
        WORKFLOW_SERVICE_PID=$!
        echo $WORKFLOW_SERVICE_PID > workflow_service_test.pid
        
        # Wait for startup
        sleep 3
        
        # Check if it started successfully
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            echo -e "${GREEN}   ✅ Workflow Service (Test DB) started (PID: $WORKFLOW_SERVICE_PID)${NC}"
            echo -e "   📡 URL: http://localhost:$WORKFLOW_SERVICE_PORT"
            echo -e "   📄 Logs: $BASE_DIR/workflow-service/workflow_service_test.log"
            echo -e "   🗄️  Database: $TEST_DB_NAME"
            return 0
        else
            echo -e "${RED}   ❌ Workflow Service failed to start${NC}"
            echo -e "   📄 Check logs: cat $BASE_DIR/workflow-service/workflow_service_test.log"
            return 1
        fi
    else
        echo -e "${YELLOW}   ⚠️  Skipping Workflow Service startup${NC}"
        return 0
    fi
}

# Function to check service health
check_test_service_health() {
    local url=$1
    local name=$2
    
    echo -e "${YELLOW}🔍 Checking $name health...${NC}"
    
    # Wait up to 30 seconds for service to be ready
    for i in {1..10}; do
        if curl -s --connect-timeout 3 "$url/health" > /dev/null 2>&1; then
            echo -e "${GREEN}   ✅ $name is healthy${NC}"
            return 0
        else
            echo -e "   ⏳ Waiting for $name... (attempt $i/10)"
            sleep 3
        fi
    done
    
    echo -e "${RED}   ❌ $name health check failed${NC}"
    return 1
}

# Function to stop test services
stop_test_services() {
    echo -e "${YELLOW}🛑 Stopping test services...${NC}"
    
    # Stop API Gateway
    if [ -f "$BASE_DIR/api-gateway/api_gateway_test.pid" ]; then
        API_GATEWAY_PID=$(cat "$BASE_DIR/api-gateway/api_gateway_test.pid")
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            kill $API_GATEWAY_PID
            echo -e "${GREEN}   ✅ API Gateway (Test) stopped${NC}"
        fi
        rm -f "$BASE_DIR/api-gateway/api_gateway_test.pid"
    fi
    
    # Stop Workflow Service
    if [ -f "$BASE_DIR/workflow-service/workflow_service_test.pid" ]; then
        WORKFLOW_SERVICE_PID=$(cat "$BASE_DIR/workflow-service/workflow_service_test.pid")
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            kill $WORKFLOW_SERVICE_PID
            echo -e "${GREEN}   ✅ Workflow Service (Test DB) stopped${NC}"
        fi
        rm -f "$BASE_DIR/workflow-service/workflow_service_test.pid"
    fi
}

# Function to show test service status
show_test_status() {
    echo -e "${BLUE}📊 Test Service Status${NC}"
    echo "======================"
    
    # Check API Gateway
    if [ -f "$BASE_DIR/api-gateway/api_gateway_test.pid" ]; then
        API_GATEWAY_PID=$(cat "$BASE_DIR/api-gateway/api_gateway_test.pid")
        if kill -0 $API_GATEWAY_PID 2>/dev/null; then
            echo -e "API Gateway (Test): ${GREEN}✅ Running${NC} (PID: $API_GATEWAY_PID, Port: $API_GATEWAY_PORT)"
        else
            echo -e "API Gateway (Test): ${RED}❌ Not Running${NC}"
        fi
    else
        echo -e "API Gateway (Test): ${RED}❌ Not Running${NC}"
    fi
    
    # Check Workflow Service
    if [ -f "$BASE_DIR/workflow-service/workflow_service_test.pid" ]; then
        WORKFLOW_SERVICE_PID=$(cat "$BASE_DIR/workflow-service/workflow_service_test.pid")
        if kill -0 $WORKFLOW_SERVICE_PID 2>/dev/null; then
            echo -e "Workflow Service (Test DB): ${GREEN}✅ Running${NC} (PID: $WORKFLOW_SERVICE_PID, Port: $WORKFLOW_SERVICE_PORT)"
        else
            echo -e "Workflow Service (Test DB): ${RED}❌ Not Running${NC}"
        fi
    else
        echo -e "Workflow Service (Test DB): ${RED}❌ Not Running${NC}"
    fi
    
    # Show database info
    echo ""
    echo -e "${BLUE}🗄️  Test Database Info${NC}"
    echo "======================"
    echo -e "Database: $TEST_DB_NAME"
    
    if psql -d $TEST_DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        WORKFLOW_COUNT=$(psql -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM workflows;" 2>/dev/null | xargs || echo "0")
        echo -e "Status: ${GREEN}✅ Connected${NC}"
        echo -e "Test Workflows: $WORKFLOW_COUNT"
    else
        echo -e "Status: ${RED}❌ Not Connected${NC}"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        "start")
            echo -e "${BLUE}🚀 Starting test services...${NC}"
            echo ""
            
            # Check test database first
            if ! check_test_database; then
                echo -e "${RED}❌ Test database not ready${NC}"
                echo -e "${YELLOW}💡 Run: ./setup_test_database.sh${NC}"
                exit 1
            fi
            echo ""
            
            # Start services
            start_test_api_gateway
            echo ""
            start_test_workflow_service
            echo ""
            
            # Health checks
            echo -e "${BLUE}🔍 Health checks...${NC}"
            check_test_service_health "http://localhost:$API_GATEWAY_PORT" "API Gateway (Test)"
            check_test_service_health "http://localhost:$WORKFLOW_SERVICE_PORT" "Workflow Service (Test DB)"
            echo ""
            
            # Show final status
            show_test_status
            echo ""
            
            echo -e "${GREEN}🎉 Test services are ready!${NC}"
            echo ""
            echo -e "${BLUE}💡 Next steps:${NC}"
            echo "   1. Run API tests: ./test_marketplace_auth_curl.sh"
            echo "   2. Run Python tests: python test_marketplace_auth_api.py"
            echo "   3. Check test data: psql -d $TEST_DB_NAME -c 'SELECT id, name FROM workflows;'"
            echo "   4. Stop services: ./start_test_services.sh stop"
            ;;
        "stop")
            stop_test_services
            ;;
        "status")
            show_test_status
            ;;
        "restart")
            stop_test_services
            sleep 2
            main start
            ;;
        *)
            echo "Usage: $0 {start|stop|status|restart}"
            echo ""
            echo "Commands:"
            echo "  start   - Start API Gateway and Workflow Service with test database"
            echo "  stop    - Stop all test services"
            echo "  status  - Show test service status"
            echo "  restart - Restart all test services"
            exit 1
            ;;
    esac
}

# Handle Ctrl+C
trap 'echo -e "\n${YELLOW}🛑 Interrupted. Stopping test services...${NC}"; stop_test_services; exit 1' INT

# Run main function
main "$@"
