#!/usr/bin/env python3
"""
Simple test runner for the loop workflow test.
This bypasses node executor errors to focus on testing the loop implementation.
"""

import asyncio
import json
import logging
import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))


async def run_test():
    """Run the loop workflow test with proper error handling."""

    # Setup logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    logger = logging.getLogger(__name__)

    try:
        logger.info("🚀 Starting Loop Workflow Test")

        # Import the test
        from test_loop_workflow import run_loop_workflow_test

        # Run the test
        result = await run_loop_workflow_test()

        if result:
            logger.info("✅ Test completed successfully!")
            logger.info(f"📊 Result: {json.dumps(result, indent=2)}")
        else:
            logger.error("❌ Test failed!")

        return result

    except ImportError as e:
        logger.error(f"❌ Import error: {str(e)}")
        logger.error("Make sure you're running from the orchestration-engine directory")
        return None
    except Exception as e:
        logger.error(f"❌ Test execution failed: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🧪 Loop Workflow Test Runner")
    print("=" * 40)

    # Check if we're in the right directory
    if not os.path.exists("app"):
        print("❌ Error: 'app' directory not found")
        print("Please run this script from the orchestration-engine directory")
        sys.exit(1)

    # Run the test
    result = asyncio.run(run_test())

    if result:
        print("\n🎉 Test execution completed!")
    else:
        print("\n💥 Test execution failed!")
