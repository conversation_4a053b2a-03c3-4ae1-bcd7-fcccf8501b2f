import pytest

from app.services.loop_executor.loop_validator import LoopValidator


class TestLoopValidator:
    """
    Test suite for LoopValidator class.
    Tests loop configuration validation and schema checking.
    """

    @pytest.fixture
    def validator(self):
        """Create LoopValidator instance for testing."""
        return LoopValidator()

    @pytest.fixture
    def valid_config(self):
        """Valid loop configuration for testing."""
        return {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["item1", "item2", "item3"]},
            "loop_body_transitions": ["transition_1", "transition_2"],
            "aggregation_config": {"type": "list"},
            "concurrency": {"enabled": False, "max_concurrent": 5},
        }

    @pytest.fixture
    def sample_transitions(self):
        """Sample transitions for validation testing."""
        return {
            "transition_1": {"id": "transition_1", "type": "tool"},
            "transition_2": {"id": "transition_2", "type": "tool"},
            "transition_3": {"id": "transition_3", "type": "tool"},
        }

    def test_validate_valid_config(self, validator, valid_config, sample_transitions):
        """Test validation of a completely valid configuration."""
        result = validator.validate_loop_config(valid_config, sample_transitions)

        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_missing_required_fields(self, validator):
        """Test validation with missing required fields."""
        invalid_config = {
            "loop_type": "context_independent"
            # Missing other required fields
        }

        result = validator.validate_loop_config(invalid_config)

        assert result["valid"] is False
        assert len(result["errors"]) > 0
        assert any("Required field missing" in error for error in result["errors"])

    def test_validate_invalid_loop_type(self, validator, valid_config):
        """Test validation with invalid loop type."""
        valid_config["loop_type"] = "invalid_type"

        result = validator.validate_loop_config(valid_config)

        assert result["valid"] is False
        assert any("Invalid loop_type" in error for error in result["errors"])

    def test_validate_list_iteration_source(self, validator, valid_config):
        """Test validation of list iteration source."""
        # Valid list source
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is True

        # Empty list
        valid_config["iteration_source"]["data"] = []
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("cannot be empty" in error for error in result["errors"])

        # Non-list data
        valid_config["iteration_source"]["data"] = "not_a_list"
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("must be an array" in error for error in result["errors"])

    def test_validate_range_iteration_source(self, validator, valid_config):
        """Test validation of range iteration source."""
        valid_config["iteration_source"] = {
            "type": "range",
            "data": {"start": 0, "stop": 10, "step": 1},
        }

        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is True

        # Missing stop
        valid_config["iteration_source"]["data"] = {"start": 0, "step": 1}
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("stop is required" in error for error in result["errors"])

        # Invalid range (start >= stop with positive step)
        valid_config["iteration_source"]["data"] = {"start": 10, "stop": 5, "step": 1}
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("Invalid range" in error for error in result["errors"])

        # Zero step
        valid_config["iteration_source"]["data"] = {"start": 0, "stop": 10, "step": 0}
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("step cannot be zero" in error for error in result["errors"])

    def test_validate_condition_iteration_source(self, validator, valid_config):
        """Test validation of condition iteration source."""
        valid_config["iteration_source"] = {"type": "condition", "condition": "x < 10"}

        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is True

        # Missing condition
        valid_config["iteration_source"] = {"type": "condition"}
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("condition is required" in error for error in result["errors"])

    def test_validate_aggregation_config(self, validator, valid_config):
        """Test validation of aggregation configuration."""
        # Valid list aggregation
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is True

        # Invalid aggregation type
        valid_config["aggregation_config"]["type"] = "invalid_type"
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any(
            "Invalid aggregation_config.type" in error for error in result["errors"]
        )

        # Custom aggregation without function_name
        valid_config["aggregation_config"] = {"type": "custom"}
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("function_name is required" in error for error in result["errors"])

        # Merge aggregation with invalid strategy
        valid_config["aggregation_config"] = {
            "type": "merge",
            "merge_strategy": "invalid_strategy",
        }
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any(
            "Invalid aggregation_config.merge_strategy" in error
            for error in result["errors"]
        )

    def test_validate_loop_body_transitions(
        self, validator, valid_config, sample_transitions
    ):
        """Test validation of loop body transitions."""
        # Valid transitions
        result = validator.validate_loop_config(valid_config, sample_transitions)
        assert result["valid"] is True

        # Empty transitions list
        valid_config["loop_body_transitions"] = []
        result = validator.validate_loop_config(valid_config, sample_transitions)
        assert result["valid"] is False
        assert any("cannot be empty" in error for error in result["errors"])

        # Non-existent transition
        valid_config["loop_body_transitions"] = ["nonexistent_transition"]
        result = validator.validate_loop_config(valid_config, sample_transitions)
        assert result["valid"] is False
        assert any("transition not found" in error for error in result["errors"])

        # Non-string transition ID
        valid_config["loop_body_transitions"] = [123]
        result = validator.validate_loop_config(valid_config, sample_transitions)
        assert result["valid"] is False
        assert any("must be a string" in error for error in result["errors"])

    def test_validate_concurrency_config(self, validator, valid_config):
        """Test validation of concurrency configuration."""
        # Valid concurrency config
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is True

        # Invalid enabled type
        valid_config["concurrency"]["enabled"] = "not_boolean"
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("must be a boolean" in error for error in result["errors"])

        # Invalid max_concurrent
        valid_config["concurrency"]["enabled"] = True
        valid_config["concurrency"]["max_concurrent"] = 0
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("must be greater than 0" in error for error in result["errors"])

        # Non-integer max_concurrent
        valid_config["concurrency"]["max_concurrent"] = "not_integer"
        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any("must be an integer" in error for error in result["errors"])

    def test_validate_cross_field_consistency(self, validator, valid_config):
        """Test cross-field consistency validation."""
        # Context-preserving with concurrency should fail
        valid_config["loop_type"] = "context_preserving"
        valid_config["concurrency"]["enabled"] = True

        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any(
            "not compatible with concurrent execution" in error
            for error in result["errors"]
        )

        # Invalid exit_transition type
        valid_config["loop_type"] = "context_independent"
        valid_config["concurrency"]["enabled"] = False
        valid_config["exit_transition"] = 123

        result = validator.validate_loop_config(valid_config)
        assert result["valid"] is False
        assert any(
            "exit_transition must be a string" in error for error in result["errors"]
        )

    def test_validate_iteration_data(self, validator):
        """Test validation of iteration data."""
        # Valid iteration data
        iteration_data = ["item1", "item2", "item3"]
        result = validator.validate_iteration_data(iteration_data)
        assert result["valid"] is True

        # Empty iteration data (should warn but be valid)
        result = validator.validate_iteration_data([])
        assert result["valid"] is True
        assert len(result["warnings"]) > 0

        # Non-list iteration data
        result = validator.validate_iteration_data("not_a_list")
        assert result["valid"] is False
        assert any("must be a list" in error for error in result["errors"])

        # Too many iterations
        large_data = list(range(1000))
        result = validator.validate_iteration_data(large_data, max_iterations=100)
        assert result["valid"] is False
        assert any("Too many iterations" in error for error in result["errors"])

    def test_get_validation_schema(self, validator):
        """Test getting validation schema."""
        schema = validator.get_validation_schema()

        assert isinstance(schema, dict)
        assert schema["type"] == "object"
        assert "required" in schema
        assert "properties" in schema

        # Check required fields
        required_fields = [
            "loop_type",
            "iteration_source",
            "loop_body_transitions",
            "aggregation_config",
        ]
        for field in required_fields:
            assert field in schema["required"]
            assert field in schema["properties"]

    def test_validation_exception_handling(self, validator):
        """Test validation exception handling."""
        # Pass invalid input that might cause exceptions
        result = validator.validate_loop_config("not_a_dict")

        assert result["valid"] is False
        assert len(result["errors"]) > 0

    def test_basic_structure_validation(self, validator):
        """Test basic structure validation."""
        # Non-dict config
        errors = validator._validate_basic_structure("not_a_dict")
        assert len(errors) > 0
        assert "must be a dictionary" in errors[0]

        # Missing required fields
        errors = validator._validate_basic_structure({})
        assert len(errors) > 0
        assert any("Required field missing" in error for error in errors)

        # Null required fields
        config = {
            "loop_type": None,
            "iteration_source": {},
            "loop_body_transitions": [],
            "aggregation_config": {},
        }
        errors = validator._validate_basic_structure(config)
        assert len(errors) > 0
        assert any("cannot be null" in error for error in errors)

    def test_iteration_source_validation_edge_cases(self, validator):
        """Test edge cases in iteration source validation."""
        # Non-dict iteration_source
        config = {"iteration_source": "not_a_dict"}
        errors = validator._validate_iteration_source(config)
        assert len(errors) > 0
        assert "must be a dictionary" in errors[0]

        # Invalid source type
        config = {"iteration_source": {"type": "invalid_type"}}
        errors = validator._validate_iteration_source(config)
        assert len(errors) > 0
        assert any("Invalid iteration_source.type" in error for error in errors)

        # Range with non-integer values
        config = {
            "iteration_source": {
                "type": "range",
                "data": {"start": "not_int", "stop": "not_int", "step": "not_int"},
            }
        }
        errors = validator._validate_iteration_source(config)
        assert len(errors) >= 3  # Should have errors for start, stop, and step

    def test_aggregation_config_validation_edge_cases(self, validator):
        """Test edge cases in aggregation config validation."""
        # Non-dict aggregation_config
        config = {"aggregation_config": "not_a_dict"}
        errors = validator._validate_aggregation_config(config)
        assert len(errors) > 0
        assert "must be a dictionary" in errors[0]

        # Concatenate with non-string separator
        config = {"aggregation_config": {"type": "concatenate", "separator": 123}}
        errors = validator._validate_aggregation_config(config)
        assert len(errors) > 0
        assert any("must be a string" in error for error in errors)

    def test_enhanced_concurrency_validation(self, validator):
        """Test enhanced concurrency configuration validation."""
        # Valid enhanced concurrency config
        config = {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["a", "b"]},
            "loop_body_transitions": ["t1"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "max_concurrent": 5,
                "preserve_order": True,
                "early_exit": True,
                "progress_tracking": True,
                "iteration_timeout": 30.0,
                "early_exit_config": {
                    "on_first_success": True,
                    "failure_threshold": 3,
                    "custom_condition": "result.value > 100",
                },
                "resource_limits": {
                    "memory_limit_mb": 1024,
                    "cpu_limit_percent": 80.0,
                    "max_execution_time": 300.0,
                },
            },
        }

        result = validator.validate_loop_config(config)
        assert result["valid"] is True

    def test_early_exit_config_validation(self, validator):
        """Test early exit configuration validation."""
        config = {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["a", "b"]},
            "loop_body_transitions": ["t1"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "early_exit": True,
                "early_exit_config": {
                    "on_first_success": "not_boolean",  # Invalid
                    "failure_threshold": 0,  # Invalid
                    "custom_condition": "",  # Invalid
                },
            },
        }

        result = validator.validate_loop_config(config)
        assert result["valid"] is False
        assert any(
            "on_first_success must be a boolean" in error for error in result["errors"]
        )
        assert any(
            "failure_threshold must be greater than 0" in error
            for error in result["errors"]
        )
        assert any(
            "custom_condition cannot be empty" in error for error in result["errors"]
        )

    def test_resource_limits_validation(self, validator):
        """Test resource limits validation."""
        config = {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["a", "b"]},
            "loop_body_transitions": ["t1"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "resource_limits": {
                    "memory_limit_mb": -100,  # Invalid
                    "cpu_limit_percent": 150,  # Invalid
                    "max_execution_time": 0,  # Invalid
                },
            },
        }

        result = validator.validate_loop_config(config)
        assert result["valid"] is False
        assert any(
            "memory_limit_mb must be greater than 0" in error
            for error in result["errors"]
        )
        assert any(
            "cpu_limit_percent must be between 0 and 100" in error
            for error in result["errors"]
        )
        assert any(
            "max_execution_time must be greater than 0" in error
            for error in result["errors"]
        )

    def test_concurrency_safety_limits(self, validator):
        """Test concurrency safety limits."""
        config = {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["a", "b"]},
            "loop_body_transitions": ["t1"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "max_concurrent": 150,  # Exceeds safety limit
            },
        }

        result = validator.validate_loop_config(config)
        assert result["valid"] is False
        assert any(
            "should not exceed 100 for safety" in error for error in result["errors"]
        )
