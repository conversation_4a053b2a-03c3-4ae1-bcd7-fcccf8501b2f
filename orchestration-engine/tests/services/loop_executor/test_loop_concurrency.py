import asyncio
from unittest.mock import Async<PERSON><PERSON>, Mock, patch

import pytest

from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopConcurrency:
    """Test suite for concurrent loop execution features."""

    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for LoopExecutor."""
        return {
            "state_manager": <PERSON><PERSON>(),
            "workflow_utils": <PERSON><PERSON>(),
            "result_callback": AsyncMock(),
            "transitions_by_id": {},
            "nodes": {},
            "transition_handler": <PERSON><PERSON>(),
            "user_id": "test_user",
        }

    @pytest.fixture
    def loop_executor(self, mock_dependencies):
        """Create LoopExecutor instance with mocked dependencies."""
        return LoopExecutor(**mock_dependencies)

    @pytest.fixture
    def concurrent_loop_config(self):
        """Sample concurrent loop configuration."""
        return {
            "loop_type": "context_independent",
            "iteration_source": {
                "type": "list",
                "data": ["item1", "item2", "item3", "item4", "item5"],
            },
            "loop_body_transitions": ["process_item"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "max_concurrent": 3,
                "preserve_order": True,
                "early_exit": False,
                "progress_tracking": True,
                "iteration_timeout": 10.0,
            },
        }

    @pytest.fixture
    def early_exit_config(self):
        """Sample early exit configuration."""
        return {
            "loop_type": "context_independent",
            "iteration_source": {
                "type": "list",
                "data": ["item1", "item2", "item3", "item4", "item5"],
            },
            "loop_body_transitions": ["process_item"],
            "aggregation_config": {"type": "list"},
            "concurrency": {
                "enabled": True,
                "max_concurrent": 3,
                "early_exit": True,
                "early_exit_config": {
                    "on_first_success": True,
                    "failure_threshold": 2,
                    "custom_condition": "result.get('value', 0) > 100",
                },
            },
        }

    @pytest.mark.asyncio
    async def test_concurrent_execution_basic(
        self, loop_executor, concurrent_loop_config
    ):
        """Test basic concurrent execution functionality."""
        # Setup
        loop_executor.current_loop_config = concurrent_loop_config
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(5)]
        loop_executor.iteration_results = {}
        loop_executor.loop_context = {"completed_iterations": 0, "failed_iterations": 0}

        # Mock execute_single_iteration to return success
        async def mock_execute_iteration(index, item):
            await asyncio.sleep(0.1)  # Simulate work
            return {
                "iteration_index": index,
                "iteration_item": item,
                "status": "completed",
            }

        loop_executor.execute_single_iteration = mock_execute_iteration

        # Execute
        await loop_executor.execute_concurrent_loop()

        # Verify
        assert len(loop_executor.iteration_results) == 5
        assert all(
            result["status"] == "completed"
            for result in loop_executor.iteration_results.values()
        )
        assert hasattr(loop_executor, "concurrent_state")

    @pytest.mark.asyncio
    async def test_early_exit_on_first_success(self, loop_executor, early_exit_config):
        """Test early exit on first success."""
        # Setup
        loop_executor.current_loop_config = early_exit_config
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(5)]
        loop_executor.iteration_results = {}
        loop_executor.loop_context = {"completed_iterations": 0, "failed_iterations": 0}

        # Mock execute_single_iteration to return success on first item
        async def mock_execute_iteration(index, item):
            await asyncio.sleep(0.1)
            return {
                "iteration_index": index,
                "iteration_item": item,
                "status": "completed",
                "value": 150,  # Triggers early exit condition
            }

        loop_executor.execute_single_iteration = mock_execute_iteration

        # Execute
        await loop_executor.execute_concurrent_loop()

        # Verify early exit was triggered
        assert hasattr(loop_executor, "concurrent_state")
        # Note: Due to concurrent execution, we can't guarantee exact count
        # but we should have fewer than 5 completed iterations

    @pytest.mark.asyncio
    async def test_timeout_handling(self, loop_executor, concurrent_loop_config):
        """Test iteration timeout handling."""
        # Setup with short timeout
        concurrent_loop_config["concurrency"]["iteration_timeout"] = 0.05
        loop_executor.current_loop_config = concurrent_loop_config
        loop_executor.current_iteration_data = [(0, "slow_item")]
        loop_executor.iteration_results = {}
        loop_executor.loop_context = {"completed_iterations": 0, "failed_iterations": 0}

        # Mock slow execution
        async def slow_execute_iteration(index, item):
            await asyncio.sleep(0.2)  # Longer than timeout
            return {"status": "completed"}

        loop_executor.execute_single_iteration = slow_execute_iteration
        loop_executor.handle_iteration_error = AsyncMock()

        # Execute
        await loop_executor.execute_concurrent_loop()

        # Verify timeout was handled
        loop_executor.handle_iteration_error.assert_called()

    @pytest.mark.asyncio
    async def test_progress_tracking(self, loop_executor, concurrent_loop_config):
        """Test progress tracking functionality."""
        # Setup
        loop_executor.current_loop_config = concurrent_loop_config
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(10)]
        loop_executor.iteration_results = {}
        loop_executor.loop_context = {"completed_iterations": 0, "failed_iterations": 0}
        loop_executor.concurrent_state = {
            "completed_count": 0,
            "failed_count": 0,
            "cancelled_count": 0,
        }

        # Test progress update
        await loop_executor._update_concurrent_progress()
        # Should not raise any exceptions

    @pytest.mark.asyncio
    async def test_result_ordering_validation(
        self, loop_executor, concurrent_loop_config
    ):
        """Test result ordering validation."""
        # Setup
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(5)]
        loop_executor.iteration_results = {
            0: "result0",
            2: "result2",
            4: "result4",
        }  # Missing 1, 3

        # Test ordering validation
        await loop_executor._ensure_result_ordering([])
        # Should log warnings about missing results

    def test_early_exit_condition_evaluation(self, loop_executor):
        """Test custom early exit condition evaluation."""
        # Setup
        loop_executor.concurrent_state = {"completed_count": 5, "failed_count": 1}

        # Test safe condition
        result = {"value": 150}
        condition = "result.get('value', 0) > 100"
        assert loop_executor._evaluate_custom_condition(condition, result, 0) == True

        # Test unsafe condition (should be rejected)
        unsafe_condition = "import os; os.system('rm -rf /')"
        assert (
            loop_executor._evaluate_custom_condition(unsafe_condition, result, 0)
            == False
        )

    @pytest.mark.asyncio
    async def test_resource_monitoring(self, loop_executor):
        """Test resource usage monitoring."""
        config = {"resource_limits": {"cpu_limit_percent": 80, "memory_limit_mb": 1024}}

        # Test monitoring (should not raise exceptions)
        await loop_executor._monitor_resource_usage(config)

    @pytest.mark.asyncio
    async def test_dynamic_concurrency_adjustment(self, loop_executor):
        """Test dynamic concurrency adjustment."""
        semaphore = asyncio.Semaphore(5)
        config = {
            "dynamic_adjustment": True,
            "max_concurrent": 5,
            "resource_limits": {"cpu_limit_percent": 80, "memory_limit_percent": 80},
        }

        # Test adjustment (should not raise exceptions)
        await loop_executor._adjust_concurrency_dynamically(semaphore, config)

    @pytest.mark.asyncio
    async def test_concurrent_execution_stats_logging(self, loop_executor):
        """Test concurrent execution statistics logging."""
        # Setup concurrent state
        loop_executor.concurrent_state = {
            "start_time": asyncio.get_event_loop().time() - 1.0,
            "completed_count": 8,
            "failed_count": 1,
            "cancelled_count": 1,
            "early_exit_triggered": True,
        }
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(10)]

        # Test stats logging
        await loop_executor._log_concurrent_execution_stats()
        # Should not raise any exceptions

    @pytest.mark.asyncio
    async def test_early_exit_monitoring(self, loop_executor):
        """Test early exit monitoring during task execution."""
        # Create mock tasks
        tasks = []
        for i in range(3):
            task = asyncio.create_task(asyncio.sleep(0.1))
            task.set_name(f"test_task_{i}")
            tasks.append(task)

        cancellation_event = asyncio.Event()

        # Test without early exit
        results = await loop_executor._execute_with_early_exit_monitoring(
            tasks, cancellation_event
        )
        assert len(results) == 3

    @pytest.mark.asyncio
    async def test_failure_isolation(self, loop_executor, concurrent_loop_config):
        """Test that failures in one iteration don't affect others."""
        # Setup
        loop_executor.current_loop_config = concurrent_loop_config
        loop_executor.current_iteration_data = [(i, f"item{i+1}") for i in range(3)]
        loop_executor.iteration_results = {}
        loop_executor.loop_context = {"completed_iterations": 0, "failed_iterations": 0}

        # Mock execution with one failure
        async def mixed_execute_iteration(index, item):
            if index == 1:
                raise Exception("Simulated failure")
            return {"iteration_index": index, "status": "completed"}

        loop_executor.execute_single_iteration = mixed_execute_iteration
        loop_executor.handle_iteration_error = AsyncMock()

        # Execute
        await loop_executor.execute_concurrent_loop()

        # Verify failure isolation
        assert len(loop_executor.iteration_results) >= 2  # At least 2 should succeed
        loop_executor.handle_iteration_error.assert_called_once()
