"""
Test Loop Node Chain Execution according to PRD.

This module tests loop body chain execution patterns:
1. Simple single-transition loops: loop -> process -> loop -> exit
2. Complex multi-transition chains: loop -> step1 -> step2 -> step3 -> step4 -> loop -> exit
3. Chain detection and monitoring
4. Result collection from chain endpoints
5. Chain state management and timeouts
"""

import asyncio
from unittest.mock import AsyncMock, Mock, patch

import pytest

from app.services.loop_executor.loop_body_chain_executor import \
    LoopBodyChainExecutor
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopNodeChainExecution:
    """Test loop body chain execution patterns according to PRD."""

    @pytest.fixture
    def mock_state_manager(self):
        """Create a mock state manager."""
        mock = Mock()
        mock.store_result = Mock()
        mock.get_result = Mock(return_value={"test": "data"})
        return mock

    @pytest.fixture
    def mock_transition_handler(self):
        """Create a mock transition handler."""
        mock = Mock()
        mock.execute_transition = AsyncMock()
        return mock

    @pytest.fixture
    def mock_workflow_utils(self):
        """Create a mock workflow utils."""
        return Mock()

    @pytest.fixture
    def mock_transitions_by_id(self):
        """Create mock transitions for testing."""
        return {
            "loop_transition": {
                "id": "loop_transition",
                "execution_type": "loop",
                "node_info": {
                    "output_data": [
                        {
                            "to_transition_id": "process_step",
                            "output_handle_registry": {
                                "handle_mappings": [
                                    {
                                        "handle_type": "loop_body_entry",
                                        "result_path": "current_item",
                                    }
                                ]
                            },
                        }
                    ]
                },
            },
            "process_step": {"id": "process_step", "execution_type": "MCP"},
            "validate_step": {"id": "validate_step", "execution_type": "MCP"},
            "transform_step": {"id": "transform_step", "execution_type": "MCP"},
            "save_step": {"id": "save_step", "execution_type": "MCP"},
        }

    @pytest.fixture
    def loop_executor(
        self,
        mock_state_manager,
        mock_transition_handler,
        mock_workflow_utils,
        mock_transitions_by_id,
    ):
        """Create a loop executor instance for testing."""
        executor = LoopExecutor(
            state_manager=mock_state_manager,
            transition_handler=mock_transition_handler,
            workflow_utils=mock_workflow_utils,
            transitions_by_id=mock_transitions_by_id,
            user_id="test_user",
        )
        return executor

    @pytest.fixture
    def chain_executor(
        self,
        mock_state_manager,
        mock_transition_handler,
        mock_workflow_utils,
        mock_transitions_by_id,
    ):
        """Create a loop body chain executor instance for testing."""
        executor = LoopBodyChainExecutor(
            state_manager=mock_state_manager,
            transition_handler=mock_transition_handler,
            workflow_utils=mock_workflow_utils,
            transitions_by_id=mock_transitions_by_id,
            user_id="test_user",
        )
        return executor

    # ========================================
    # SIMPLE LOOP BODY TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_simple_single_transition_loop(self, chain_executor):
        """Test simple single-transition loop: loop -> process -> loop -> exit."""
        loop_body_config = {
            "entry_transitions": ["process_step"],
            "exit_transitions": ["process_step"],
            "chain_completion_detection": "explicit_exit_transitions",
        }

        # Execute loop body chain for one iteration
        result = await chain_executor.execute_loop_body_chain(
            loop_transition_id="loop_transition",
            iteration_index=0,
            iteration_item="test_item",
            iteration_context={"index": 0},
            loop_body_config=loop_body_config,
        )

        # Verify chain state was initialized correctly
        assert len(chain_executor.active_chains) == 0  # Should be cleaned up
        assert len(chain_executor.completion_callbacks) == 0  # Should be cleaned up

    @pytest.mark.asyncio
    async def test_auto_detect_entry_transitions(self, chain_executor):
        """Test auto-detection of entry transitions from loop output connections."""
        entry_transitions = await chain_executor._auto_detect_entry_transitions(
            "loop_transition"
        )

        # Should detect process_step as entry transition based on handle_type
        assert "process_step" in entry_transitions

    @pytest.mark.asyncio
    async def test_auto_detect_exit_transitions(self, chain_executor):
        """Test auto-detection of exit transitions from chain analysis."""
        entry_transitions = ["process_step"]
        exit_transitions = await chain_executor._auto_detect_exit_transitions(
            entry_transitions
        )

        # For simple case, should use entry transition as exit transition
        assert "process_step" in exit_transitions

    # ========================================
    # COMPLEX CHAIN TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_complex_multi_transition_chain(self, chain_executor):
        """Test complex multi-transition chain: loop -> step1 -> step2 -> step3 -> step4 -> loop -> exit."""
        loop_body_config = {
            "entry_transitions": ["validate_step"],
            "exit_transitions": ["save_step"],
            "chain_completion_detection": "explicit_exit_transitions",
        }

        # Mock the chain execution to simulate multi-step flow
        with patch.object(
            chain_executor, "_wait_for_chain_completion", new_callable=AsyncMock
        ) as mock_wait:
            mock_wait.return_value = {"processed_data": "result"}

            result = await chain_executor.execute_loop_body_chain(
                loop_transition_id="loop_transition",
                iteration_index=0,
                iteration_item={"data": "test"},
                iteration_context={"index": 0},
                loop_body_config=loop_body_config,
            )

            assert result == {"processed_data": "result"}

    @pytest.mark.asyncio
    async def test_chain_state_initialization(self, chain_executor):
        """Test proper initialization of chain state."""
        chain_id = "test_chain_1"
        loop_body_config = {
            "entry_transitions": ["validate_step"],
            "exit_transitions": ["save_step"],
        }

        await chain_executor._initialize_chain_state(
            chain_id=chain_id,
            loop_transition_id="loop_transition",
            iteration_index=0,
            iteration_item={"test": "data"},
            iteration_context={"index": 0},
            loop_body_config=loop_body_config,
        )

        # Verify chain state was initialized correctly
        assert chain_id in chain_executor.active_chains
        chain_state = chain_executor.active_chains[chain_id]

        assert chain_state["loop_transition_id"] == "loop_transition"
        assert chain_state["iteration_index"] == 0
        assert chain_state["iteration_item"] == {"test": "data"}
        assert chain_state["entry_transitions"] == ["validate_step"]
        assert chain_state["exit_transitions"] == ["save_step"]
        assert chain_state["chain_state"] == "initializing"
        assert isinstance(chain_state["completed_transitions"], set)
        assert isinstance(chain_state["pending_transitions"], set)

    @pytest.mark.asyncio
    async def test_iteration_data_injection(self, chain_executor):
        """Test injection of iteration data for chain execution."""
        chain_id = "test_chain_1"
        iteration_item = {"id": 123, "name": "test"}
        iteration_context = {"index": 0, "total": 5}

        await chain_executor._inject_iteration_data_for_chain(
            chain_id=chain_id,
            iteration_item=iteration_item,
            iteration_context=iteration_context,
        )

        # Verify data was stored in state manager
        expected_key = f"chain_{chain_id}_iteration_data"
        chain_executor.state_manager.store_result.assert_called_once()
        call_args = chain_executor.state_manager.store_result.call_args

        assert call_args[0][0] == expected_key
        stored_data = call_args[0][1]
        assert stored_data["current_item"] == iteration_item
        assert stored_data["iteration_context"] == iteration_context
        assert stored_data["chain_id"] == chain_id

    # ========================================
    # CHAIN COMPLETION TESTS
    # ========================================

    def test_transition_completion_notification(self, chain_executor):
        """Test notification of transition completion."""
        # Set up active chain
        chain_id = "test_chain_1"
        chain_executor.active_chains[chain_id] = {
            "pending_transitions": {"transition_1", "transition_2"},
            "completed_transitions": set(),
            "exit_transitions": ["transition_2"],
        }

        # Notify completion of transition_1
        chain_executor.notify_transition_completion("transition_1", {"result": "data"})

        # Should trigger async handling (we can't easily test the async part in this sync test)
        # But we can verify the method doesn't crash and finds the right chain

    @pytest.mark.asyncio
    async def test_handle_transition_completion(self, chain_executor):
        """Test handling of transition completion."""
        chain_id = "test_chain_1"
        transition_id = "transition_1"
        result = {"processed": "data"}

        # Set up chain state
        chain_executor.active_chains[chain_id] = {
            "pending_transitions": {transition_id},
            "completed_transitions": set(),
            "exit_transitions": [transition_id],
            "result": None,
            "chain_state": "running",
        }

        # Set up completion event
        completion_event = asyncio.Event()
        chain_executor.completion_callbacks[chain_id] = completion_event

        # Handle completion
        await chain_executor._handle_transition_completion(
            chain_id, transition_id, result
        )

        # Verify state updates
        chain_state = chain_executor.active_chains[chain_id]
        assert transition_id in chain_state["completed_transitions"]
        assert transition_id not in chain_state["pending_transitions"]
        assert chain_state["result"] == result
        assert chain_state["chain_state"] == "completed"
        assert completion_event.is_set()

    @pytest.mark.asyncio
    async def test_chain_cleanup(self, chain_executor):
        """Test cleanup of chain state after completion."""
        chain_id = "test_chain_1"

        # Set up chain state
        chain_executor.active_chains[chain_id] = {"test": "data"}
        chain_executor.completion_callbacks[chain_id] = asyncio.Event()

        # Cleanup
        await chain_executor._cleanup_chain_state(chain_id)

        # Verify cleanup
        assert chain_id not in chain_executor.active_chains
        assert chain_id not in chain_executor.completion_callbacks

    # ========================================
    # TIMEOUT AND ERROR TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_chain_execution_timeout(self, chain_executor):
        """Test chain execution timeout handling."""
        chain_id = "test_chain_1"

        # Set up chain state without completion
        chain_executor.active_chains[chain_id] = {
            "result": None,
            "chain_state": "running",
        }

        # Set up completion event that never gets set
        completion_event = asyncio.Event()
        chain_executor.completion_callbacks[chain_id] = completion_event

        # Mock wait_for to simulate timeout
        with patch("asyncio.wait_for", side_effect=asyncio.TimeoutError):
            with pytest.raises(asyncio.TimeoutError):
                await chain_executor._wait_for_chain_completion(chain_id)
