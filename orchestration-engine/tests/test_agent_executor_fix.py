#!/usr/bin/env python3
"""
Test script to verify the AgentExecutor user_id parameter fix.
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, Mock, patch

from app.services.agent_executor import AgentExecutor


class TestAgentExecutorFix(unittest.TestCase):
    """Test AgentExecutor user_id parameter fix."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_producer = Mock()
        self.mock_producer._sender._running = True

    async def test_agent_executor_user_id_parameter_required(self):
        """Test that AgentExecutor requires user_id parameter."""
        executor = AgentExecutor(producer=self.mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False

        # Test that user_id is required
        with self.assertRaises(ValueError) as context:
            await executor.execute_tool(
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "response",
                    "query": "test query",
                    "agent_config": {"id": "test-agent", "name": "Test Agent"},
                },
                user_id=None,  # Missing user_id should raise error
            )

        self.assertIn("user_id is required", str(context.exception))
        print("✅ AgentExecutor correctly requires user_id parameter")

    @patch("uuid.uuid4")
    async def test_agent_executor_user_id_passed_to_helpers(self, mock_uuid):
        """Test that user_id is correctly passed to helper methods."""
        mock_uuid.return_value.__str__ = lambda x: "test-request-id"

        executor = AgentExecutor(producer=self.mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False

        # Mock the helper method to capture user_id
        captured_user_id = None

        async def mock_build_component_agent_request(*args, **kwargs):
            nonlocal captured_user_id
            # user_id should be the last argument
            captured_user_id = args[-1] if args else kwargs.get("user_id")
            return {
                "request_id": "test-request-id",
                "user_id": captured_user_id,
                "agent_type": "component",
                "execution_type": "response",
                "query": "test query",
                "variables": {},
                "agent_config": {"id": "test-agent", "name": "Test Agent"},
            }

        executor._build_component_agent_request = mock_build_component_agent_request

        # Mock the producer.send to avoid actual Kafka calls
        async def mock_send(*args, **kwargs):
            return None

        executor.producer.send = mock_send

        # Mock the future for the pending request
        mock_future = AsyncMock()
        mock_future.return_value = {"success": True, "content": "Test response"}

        with patch("asyncio.Future", return_value=mock_future):
            # Execute with a specific user_id
            test_user_id = "test_user_123"
            await executor.execute_tool(
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "response",
                    "query": "test query",
                    "agent_config": {"id": "test-agent", "name": "Test Agent"},
                },
                user_id=test_user_id,
            )

        # Verify that the user_id was correctly passed to the helper method
        self.assertEqual(captured_user_id, test_user_id)
        print(
            f"✅ AgentExecutor correctly passed user_id '{test_user_id}' to helper method"
        )


async def run_async_tests():
    """Run async test methods."""
    test_instance = TestAgentExecutorFix()
    test_instance.setUp()

    print("🔧 Testing AgentExecutor user_id parameter fix...")

    # Run async tests
    await test_instance.test_agent_executor_user_id_parameter_required()
    await test_instance.test_agent_executor_user_id_passed_to_helpers()

    print("✅ All AgentExecutor fix tests passed!")


if __name__ == "__main__":
    print("🔧 Running AgentExecutor user_id parameter fix tests...")
    asyncio.run(run_async_tests())
    print("\n🎉 AgentExecutor fix verification completed successfully!")
