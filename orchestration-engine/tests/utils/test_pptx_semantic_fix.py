"""
Test cases specifically for the PPTX semantic type detection fix.

This test module verifies that PPTX files are correctly detected as 'ppt' semantic type
even in edge cases where MIME type detection fails.
"""

import pytest

from app.utils.semantic_type_extractor import (
    _detect_semantic_type_from_url_pattern, detect_semantic_type_from_data)


class TestPPTXSemanticTypeFix:
    """Test cases for PPTX semantic type detection fix."""

    def test_pptx_normal_cases(self):
        """Test normal PPTX URL cases that should work with MIME detection."""
        test_cases = [
            "https://example.com/file.pptx",
            "https://example.com/file.PPTX",
            "https://example.com/file.pptx?download=true",
            "https://example.com/file.pptx#slide1",
            "https://example.com/path/to/file.pptx",
            "https://example.com/file%20name.pptx",
        ]

        for url in test_cases:
            result = detect_semantic_type_from_data(url)
            assert result == "ppt", f"Failed for URL: {url}"

    def test_pptx_edge_cases(self):
        """Test PPTX URL edge cases where MIME detection fails but pattern detection should work."""
        test_cases = [
            "https://example.com/path/file.pptx/download",
            "https://example.com/redirect?url=file.pptx",
            "https://example.com/file.pptx/",
            "https://example.com/file.pptx?format=presentation&version=2",
        ]

        for url in test_cases:
            result = detect_semantic_type_from_data(url)
            assert result == "ppt", f"Failed for edge case URL: {url}"

    def test_ppt_legacy_format(self):
        """Test legacy PPT format detection."""
        test_cases = [
            "https://example.com/file.ppt",
            "https://example.com/file.ppt?download=true",
            "https://example.com/path/file.ppt/download",
        ]

        for url in test_cases:
            result = detect_semantic_type_from_data(url)
            assert result == "ppt", f"Failed for PPT URL: {url}"

    def test_other_file_types_still_work(self):
        """Test that other file types are still detected correctly."""
        test_cases = [
            ("https://example.com/image.jpg", "image"),
            ("https://example.com/video.mp4", "video"),
            ("https://example.com/audio.mp3", "audio"),
            (
                "https://example.com/document.pdf",
                "application",
            ),  # PDF returns 'application' from MIME type
            ("https://example.com/image.webp", "image"),
        ]

        for url, expected in test_cases:
            result = detect_semantic_type_from_data(url)
            assert (
                result == expected
            ), f"Failed for URL: {url}, expected: {expected}, got: {result}"

    def test_non_file_urls_fallback_to_url(self):
        """Test that URLs without file extensions fall back to 'url' semantic type."""
        test_cases = [
            "https://drive.google.com/file/d/1234567890/view",
            "https://example.com/api/endpoint",
            "https://example.com/page",
            "https://example.com/",
        ]

        for url in test_cases:
            result = detect_semantic_type_from_data(url)
            assert result == "url", f"Failed for non-file URL: {url}"

    def test_pattern_detection_function_directly(self):
        """Test the pattern detection function directly."""
        # Test PPTX patterns
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/file.pptx")
            == "ppt"
        )
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/file.pptx/")
            == "ppt"
        )
        assert (
            _detect_semantic_type_from_url_pattern(
                "https://example.com/file.pptx?param=value"
            )
            == "ppt"
        )
        assert (
            _detect_semantic_type_from_url_pattern(
                "https://example.com/file.pptx#fragment"
            )
            == "ppt"
        )

        # Test other patterns
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/file.mp4")
            == "video"
        )
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/file.jpg")
            == "image"
        )

        # Test no match
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/page") is None
        )
        assert (
            _detect_semantic_type_from_url_pattern("https://example.com/file.unknown")
            is None
        )

    def test_case_insensitive_detection(self):
        """Test that pattern detection is case insensitive."""
        test_cases = [
            "https://example.com/file.PPTX",
            "https://example.com/file.Pptx",
            "https://example.com/file.PPT",
            "https://example.com/file.Mp4",
            "https://example.com/file.JPG",
        ]

        expected_types = ["ppt", "ppt", "ppt", "video", "image"]

        for url, expected in zip(test_cases, expected_types):
            result = detect_semantic_type_from_data(url)
            assert result == expected, f"Case insensitive test failed for URL: {url}"

    def test_non_url_strings(self):
        """Test that non-URL strings are handled correctly."""
        test_cases = [
            ("file.pptx", "ppt"),  # Local file
            (
                "document.pdf",
                "application",
            ),  # Local file - PDF returns 'application' from MIME type
            ("hello world", "string"),  # Regular string
            ("<EMAIL>", "email"),  # Email
        ]

        for text, expected in test_cases:
            result = detect_semantic_type_from_data(text)
            assert result == expected, f"Failed for non-URL text: {text}"
