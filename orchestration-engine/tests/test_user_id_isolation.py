#!/usr/bin/env python3
"""
Test script to verify user_id isolation between concurrent workflows.
This test ensures that user_id from one peer doesn't contaminate another peer's workflow.
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, Mock, patch

from app.services.agent_executor import AgentExecutor
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.workflow_executor import WorkflowExecutor


class TestUserIdIsolation(unittest.TestCase):
    """Test user_id isolation in executors."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_producer = Mock()
        self.mock_producer._sender._running = True

    def test_workflow_executor_no_shared_state(self):
        """Test that WorkflowExecutor doesn't store user_id as instance state."""
        executor = WorkflowExecutor(producer=self.mock_producer)

        # Verify no user_id instance variable exists
        self.assertFalse(hasattr(executor, "_current_user_id"))

        # Verify no set_user_id method exists
        self.assertFalse(hasattr(executor, "set_user_id"))

    def test_agent_executor_no_shared_state(self):
        """Test that AgentExecutor doesn't store user_id as instance state."""
        executor = AgentExecutor(producer=self.mock_producer)

        # Verify no user_id instance variable exists
        self.assertFalse(hasattr(executor, "_current_user_id"))

        # Verify no set_user_id method exists
        self.assertFalse(hasattr(executor, "set_user_id"))

    def test_kafka_tool_executor_no_shared_state(self):
        """Test that KafkaToolExecutor doesn't store user_id as instance state."""
        executor = KafkaToolExecutor(producer=self.mock_producer)

        # Verify no user_id instance variable exists
        self.assertFalse(hasattr(executor, "_current_user_id"))

        # Verify no set_user_id method exists
        self.assertFalse(hasattr(executor, "set_user_id"))

    @patch("asyncio.Future")
    @patch("uuid.uuid4")
    async def test_workflow_executor_user_id_parameter(self, mock_uuid, mock_future):
        """Test that WorkflowExecutor requires user_id as parameter."""
        mock_uuid.return_value = Mock()
        mock_uuid.return_value.__str__ = lambda x: "test-request-id"

        executor = WorkflowExecutor(producer=self.mock_producer)

        # Test that user_id is required
        with self.assertRaises(ValueError) as context:
            await executor.execute_tool(
                tool_name="test_workflow",
                tool_parameters={
                    "workflow_id": "test_workflow_id",
                    "field_mappings": {"field1": "transition1"},
                },
                user_id=None,  # Missing user_id should raise error
            )

        self.assertIn("user_id is required", str(context.exception))

    async def test_agent_executor_user_id_parameter(self):
        """Test that AgentExecutor requires user_id as parameter."""
        executor = AgentExecutor(producer=self.mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False

        # Test that user_id is required
        with self.assertRaises(ValueError) as context:
            await executor.execute_tool(
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "response",
                    "query": "test query",
                },
                user_id=None,  # Missing user_id should raise error
            )

        self.assertIn("user_id is required", str(context.exception))

    async def test_kafka_tool_executor_user_id_parameter(self):
        """Test that KafkaToolExecutor requires user_id as parameter."""
        executor = KafkaToolExecutor(producer=self.mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False

        # Test that user_id is required
        with self.assertRaises(ValueError) as context:
            await executor.execute_tool(
                tool_name="test_tool",
                tool_parameters={"param1": "value1"},
                mcp_id="test_mcp",
                user_id=None,  # Missing user_id should raise error
            )

        self.assertIn("user_id is required", str(context.exception))

    async def test_concurrent_workflow_isolation(self):
        """Test that concurrent workflows with different user_ids remain isolated."""
        executor = WorkflowExecutor(producer=self.mock_producer)

        # Mock the _build_unified_workflow_payload method to capture user_id
        captured_user_ids = []

        async def mock_build_payload(*args, **kwargs):
            # The last argument should be user_id
            user_id = args[-1] if args else kwargs.get("user_id")
            captured_user_ids.append(user_id)
            return {
                "task_id": 1752221010,
                "task_type": "workflow",
                "data": {
                    "user_id": user_id,
                    "workflow_id": "test_workflow",
                    "approval": False,
                    "payload": {
                        "user_dependent_fields": [],
                        "user_payload_template": {},
                    },
                },
                "approval": False,
            }

        executor._build_unified_workflow_payload = mock_build_payload

        # Mock the _execute_workflow_with_consumer method to avoid actual Kafka calls
        async def mock_execute_with_consumer(*args, **kwargs):
            return {"status": "completed", "result": "test_result"}

        executor._execute_workflow_with_consumer = mock_execute_with_consumer

        # Simulate concurrent workflows with different user_ids
        task1 = asyncio.create_task(
            executor.execute_tool(
                tool_name="workflow1",
                tool_parameters={
                    "workflow_id": "test_workflow_1",
                    "field_mappings": {"field1": "transition1"},
                },
                user_id="peer1_user_id",
            )
        )

        task2 = asyncio.create_task(
            executor.execute_tool(
                tool_name="workflow2",
                tool_parameters={
                    "workflow_id": "test_workflow_2",
                    "field_mappings": {"field2": "transition2"},
                },
                user_id="peer2_user_id",
            )
        )

        # Wait for both tasks to complete
        await asyncio.gather(task1, task2)

        # Verify that each workflow used its own user_id
        self.assertEqual(len(captured_user_ids), 2)
        self.assertIn("peer1_user_id", captured_user_ids)
        self.assertIn("peer2_user_id", captured_user_ids)

        print("✅ User ID isolation test passed!")
        print(f"   Captured user_ids: {captured_user_ids}")


async def run_async_tests():
    """Run async test methods."""
    test_instance = TestUserIdIsolation()
    test_instance.setUp()

    print("🔧 Testing user_id isolation...")

    # Run async tests
    await test_instance.test_workflow_executor_user_id_parameter()
    print("✅ WorkflowExecutor user_id parameter test passed")

    await test_instance.test_agent_executor_user_id_parameter()
    print("✅ AgentExecutor user_id parameter test passed")

    await test_instance.test_kafka_tool_executor_user_id_parameter()
    print("✅ KafkaToolExecutor user_id parameter test passed")

    await test_instance.test_concurrent_workflow_isolation()
    print("✅ Concurrent workflow isolation test passed")


if __name__ == "__main__":
    # Run sync tests
    unittest.main(argv=[""], exit=False, verbosity=2)

    # Run async tests
    print("\n🔧 Running async isolation tests...")
    asyncio.run(run_async_tests())

    print("\n🎉 All user_id isolation tests completed successfully!")
