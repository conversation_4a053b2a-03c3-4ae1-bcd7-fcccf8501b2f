#!/usr/bin/env python3
"""
Test script using the exact user payload format to verify the loop node fix.
"""

import json
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

from services.initialize_workflow import initialize_workflow_with_params


def test_real_user_payload():
    """Test with the exact payload format the user provided."""

    # Load the actual test.json file
    with open("testing/test.json", "r") as f:
        workflow = json.load(f)

    # Create the exact user payload from the conversation
    params = {
        "workflow_id": "b3b7f168-29ee-4e51-8717-16f4c8beb832",
        "approval": True,
        "payload": {
            "user_dependent_fields": ["iteration_list"],
            "user_payload_template": {
                "iteration_list": {
                    "value": '["HELLO","MNY","EH"]',
                    "transition_id": "transition-LoopNode-1750925271398",
                }
            },
        },
    }

    print("🧪 Testing with real user payload...")
    print(
        f"📥 Input: {params['payload']['user_payload_template']['iteration_list']['value']}"
    )

    # Find the loop transition before processing
    loop_transition = None
    for transition in workflow["transitions"]:
        if transition.get("id") == "transition-LoopNode-1750925271398":
            loop_transition = transition
            break

    if not loop_transition:
        print("❌ Could not find loop transition in test.json")
        return False

    print(
        f"📋 Before: loop_config.iteration_source.iteration_list = {loop_transition['loop_config']['iteration_source']['iteration_list']}"
    )

    # Test the fix
    try:
        updated_workflow = initialize_workflow_with_params(workflow, params)

        # Find the updated loop transition
        updated_loop_transition = None
        for transition in updated_workflow["transitions"]:
            if transition.get("id") == "transition-LoopNode-1750925271398":
                updated_loop_transition = transition
                break

        if not updated_loop_transition:
            print("❌ Could not find updated loop transition")
            return False

        # Check the results
        final_iteration_list = updated_loop_transition["loop_config"][
            "iteration_source"
        ]["iteration_list"]
        expected_array = ["HELLO", "MNY", "EH"]

        print(
            f"📋 After: loop_config.iteration_source.iteration_list = {final_iteration_list}"
        )

        if final_iteration_list == expected_array:
            print("✅ SUCCESS! Loop config correctly updated with user input!")
            print(f"✅ The iteration_list now contains: {final_iteration_list}")
            return True
        else:
            print(f"❌ FAILED: Expected {expected_array}, got {final_iteration_list}")
            return False

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_real_user_payload()
    sys.exit(0 if success else 1)
