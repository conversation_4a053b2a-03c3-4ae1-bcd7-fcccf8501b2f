#!/usr/bin/env python3
"""
Debug script to test the loop node with our test.json configuration
"""
import asyncio
import json
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

from aiokafka import AIOKafkaProducer

from app.config.config import settings
from app.core_.executor_core import EnhancedWorkflowEngine
from app.services.initialize_workflow import initialize_workflow_with_params
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.utils.enhanced_logger import get_logger, setup_logging

# Setup logging
setup_logging(
    allowed_loggers={
        "EnhancedWorkflowEngine",
        "TransitionHandler",
        "WorkflowUtils",
        "StateManager",
        "LoopExecutor",
        "TestEngine",
    },
    excluded_loggers={"httpx", "mcp.client.sse"},
)

logger = get_logger("TestEngine")


async def run_debug_test():
    """Run the debug test with our test.json configuration"""

    logger.info("🧪 Starting Loop Debug Test")

    try:
        # Load our test.json file
        with open("./testing/test.json", "r") as file:
            workflow = json.load(file)

        logger.info("✅ Test schema loaded from: testing/test.json")

        # Create the workflow initialization payload
        workflow_json_content = {
            "workflow_id": "test_loop_workflow",
            "payload": {
                "user_dependent_fields": ["iteration_list"],
                "user_payload_template": {
                    "iteration_list": '["q", "b", "c"]',  # JSON string as it would come from frontend
                    "transition_ids": ["transition-ForEachLoopComponent-1750925312663"],
                },
            },
        }

        # Initialize the workflow
        init_workflow = initialize_workflow_with_params(
            workflow,
            params=workflow_json_content,
        )

        logger.info("✅ Workflow initialized")

        # Setup Kafka producer (mock for testing)
        kafka_broker: str = settings.kafka_bootstrap_servers
        producer: AIOKafkaProducer = AIOKafkaProducer(
            bootstrap_servers=kafka_broker,
            max_request_size=524288000,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )

        mcp_tool_executor = KafkaToolExecutor(producer=producer)

        async def result_callback(result_info):
            logger.info(f"🎯 Result callback: {result_info}")

        await mcp_tool_executor.start()

        # Create the workflow engine
        engine = EnhancedWorkflowEngine(
            init_workflow=init_workflow,
            result_callback=result_callback,
            tool_executor=mcp_tool_executor,
            user_id="test_user_123",  # Add user_id for non-loop executions
        )

        logger.info("✅ Engine created, starting workflow execution")

        # Execute the workflow
        await engine.execute()

        logger.info("✅ Workflow execution completed")

    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")

    finally:
        logger.info("🧹 Cleaning up...")


if __name__ == "__main__":
    asyncio.run(run_debug_test())
