import json

import pytest
from jsonschema import ValidationError, validate

from app.utils.helper_functions import load_schema


class TestLoopSchemaValidation:
    """
    Test suite for loop execution schema validation.
    Tests that the updated transition schema correctly validates loop configurations.
    """

    @pytest.fixture
    def transition_schema(self):
        """Load the transition schema for validation."""
        return load_schema("app/shared/json_schemas/transition_schema.json")

    def test_loop_execution_type_validation(self, transition_schema):
        """Test that 'loop' is accepted as a valid execution_type."""
        valid_transition = {
            "id": "test-loop-transition",
            "sequence": 1,
            "transition_type": "standard",
            "execution_type": "loop",
            "node_info": {
                "node_id": "test-node",
                "tools_to_use": [],
                "input_data": [],
                "output_data": [],
            },
            "result_resolution": {
                "node_type": "loop",
                "expected_result_structure": "direct",
                "handle_registry": {"input_handles": [], "output_handles": []},
                "dynamic_discovery": {"enabled": False},
            },
            "loop_config": {
                "loop_type": "context_independent",
                "aggregation_type": "aggregating",
                "iteration_source": {
                    "type": "list",
                    "data": ["item1", "item2", "item3"],
                },
                "loop_body_transitions": ["body-transition-1"],
            },
            "end": False,
        }

        workflow = {"nodes": [], "transitions": [valid_transition]}

        # Should not raise ValidationError
        validate(instance=workflow, schema=transition_schema)

    def test_loop_config_required_fields(self, transition_schema):
        """Test that loop_config requires all mandatory fields."""
        invalid_transition = {
            "id": "test-loop-transition",
            "sequence": 1,
            "transition_type": "standard",
            "execution_type": "loop",
            "node_info": {
                "node_id": "test-node",
                "tools_to_use": [],
                "input_data": [],
                "output_data": [],
            },
            "result_resolution": {
                "node_type": "loop",
                "expected_result_structure": "direct",
                "handle_registry": {"input_handles": [], "output_handles": []},
                "dynamic_discovery": {"enabled": False},
            },
            "loop_config": {
                "loop_type": "context_independent"
                # Missing required fields: aggregation_type, iteration_source, loop_body_transitions
            },
            "end": False,
        }

        workflow = {"nodes": [], "transitions": [invalid_transition]}

        with pytest.raises(ValidationError):
            validate(instance=workflow, schema=transition_schema)

    def test_loop_type_enum_validation(self, transition_schema):
        """Test that loop_type only accepts valid enum values."""
        invalid_transition = {
            "id": "test-loop-transition",
            "sequence": 1,
            "transition_type": "standard",
            "execution_type": "loop",
            "node_info": {
                "node_id": "test-node",
                "tools_to_use": [],
                "input_data": [],
                "output_data": [],
            },
            "result_resolution": {
                "node_type": "loop",
                "expected_result_structure": "direct",
                "handle_registry": {"input_handles": [], "output_handles": []},
                "dynamic_discovery": {"enabled": False},
            },
            "loop_config": {
                "loop_type": "invalid_type",  # Invalid enum value
                "aggregation_type": "aggregating",
                "iteration_source": {"type": "list", "data": ["item1", "item2"]},
                "loop_body_transitions": ["body-transition-1"],
            },
            "end": False,
        }

        workflow = {"nodes": [], "transitions": [invalid_transition]}

        with pytest.raises(ValidationError):
            validate(instance=workflow, schema=transition_schema)

    def test_iteration_source_types(self, transition_schema):
        """Test different iteration source types."""
        # Test list type
        list_config = {"type": "list", "data": ["item1", "item2", "item3"]}

        # Test range type
        range_config = {"type": "range", "data": {"start": 0, "stop": 10, "step": 1}}

        # Test condition type
        condition_config = {"type": "condition", "condition": "counter < 5"}

        for iteration_source in [list_config, range_config, condition_config]:
            valid_transition = {
                "id": "test-loop-transition",
                "sequence": 1,
                "transition_type": "standard",
                "execution_type": "loop",
                "node_info": {
                    "node_id": "test-node",
                    "tools_to_use": [],
                    "input_data": [],
                    "output_data": [],
                },
                "result_resolution": {
                    "node_type": "loop",
                    "expected_result_structure": "direct",
                    "handle_registry": {"input_handles": [], "output_handles": []},
                    "dynamic_discovery": {"enabled": False},
                },
                "loop_config": {
                    "loop_type": "context_independent",
                    "aggregation_type": "aggregating",
                    "iteration_source": iteration_source,
                    "loop_body_transitions": ["body-transition-1"],
                },
                "end": False,
            }

            workflow = {"nodes": [], "transitions": [valid_transition]}

            # Should not raise ValidationError
            validate(instance=workflow, schema=transition_schema)

    def test_concurrency_config_validation(self, transition_schema):
        """Test concurrency configuration validation."""
        valid_transition = {
            "id": "test-loop-transition",
            "sequence": 1,
            "transition_type": "standard",
            "execution_type": "loop",
            "node_info": {
                "node_id": "test-node",
                "tools_to_use": [],
                "input_data": [],
                "output_data": [],
            },
            "result_resolution": {
                "node_type": "loop",
                "expected_result_structure": "direct",
                "handle_registry": {"input_handles": [], "output_handles": []},
                "dynamic_discovery": {"enabled": False},
            },
            "loop_config": {
                "loop_type": "context_independent",
                "aggregation_type": "aggregating",
                "iteration_source": {
                    "type": "list",
                    "data": ["item1", "item2", "item3"],
                },
                "loop_body_transitions": ["body-transition-1"],
                "concurrency": {
                    "enabled": True,
                    "max_concurrent": 3,
                    "preserve_order": True,
                },
            },
            "end": False,
        }

        workflow = {"nodes": [], "transitions": [valid_transition]}

        # Should not raise ValidationError
        validate(instance=workflow, schema=transition_schema)
