"""
Loop Aggregator - Result aggregation and formatting for loop execution.

This module provides various aggregation strategies for loop results,
handling the dual output system and different aggregation types.
"""

import json
from typing import Any, Dict, List, Optional, Union

from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopAggregator")


class LoopAggregator:
    """
    Handles aggregation of loop iteration results into final outputs.

    This class provides various aggregation strategies and handles the
    dual output system where loops have both iteration outputs (current_item)
    and final aggregated outputs (final_results).
    """

    def __init__(self, logger=None):
        """
        Initialize the loop aggregator.

        Args:
            logger: Optional logger instance
        """
        self.logger = logger or get_logger("LoopAggregator")

    async def aggregate_results(
        self,
        iteration_results: List[Dict[str, Any]],
        aggregation_config: Dict[str, Any],
        loop_metadata: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Aggregate iteration results according to configuration.

        Args:
            iteration_results: List of results from all iterations
            aggregation_config: Configuration for aggregation strategy
            loop_metadata: Optional metadata about the loop execution

        Returns:
            Aggregated results with dual output format
        """
        aggregation_type = aggregation_config.get("aggregation_type", "collect_all")
        include_metadata = aggregation_config.get("include_metadata", False)

        # Extract exit transitions from loop metadata if available
        self.exit_transitions = None
        if loop_metadata and "exit_transitions" in loop_metadata:
            self.exit_transitions = loop_metadata["exit_transitions"]

        self.logger.info(
            f"🔄 Aggregating {len(iteration_results)} results using strategy: {aggregation_type}"
        )
        self.logger.info(
            f"🔍 AGGREGATOR DEBUG: iteration_results received: {iteration_results}"
        )
        self.logger.info(
            f"🔍 AGGREGATOR DEBUG: aggregation_config: {aggregation_config}"
        )
        self.logger.info(f"🔍 AGGREGATOR DEBUG: loop_metadata: {loop_metadata}")
        self.logger.info(
            f"🔍 AGGREGATOR DEBUG: exit_transitions: {self.exit_transitions}"
        )

        # Apply aggregation strategy
        if aggregation_type == "collect_all":
            final_results = await self._collect_all(iteration_results)
        elif aggregation_type == "collect_successful":
            final_results = await self._collect_successful(iteration_results)
        elif aggregation_type == "collect_values":
            final_results = await self._collect_values(iteration_results)
        elif aggregation_type == "count_only":
            final_results = await self._count_only(iteration_results)
        elif aggregation_type == "first_successful":
            final_results = await self._first_successful(iteration_results)
        elif aggregation_type == "last_successful":
            final_results = await self._last_successful(iteration_results)
        elif aggregation_type == "merge_objects":
            final_results = await self._merge_objects(iteration_results)
        elif aggregation_type == "flatten_arrays":
            final_results = await self._flatten_arrays(iteration_results)
        elif aggregation_type == "combine_text":
            final_results = await self._combine_text(iteration_results)
        elif aggregation_type == "return_original":
            final_results = await self._return_original(
                iteration_results, loop_metadata
            )
        else:
            self.logger.warning(
                f"Unknown aggregation type: {aggregation_type}, using collect_all"
            )
            final_results = await self._collect_all(iteration_results)

        # Build final output with dual output system
        output = {
            "output_type": "loop_completion",
            "final_results": final_results,
            "current_item": None,  # No current item on completion
            "aggregation_metadata": {
                "aggregation_type": aggregation_type,
                "total_iterations": len(iteration_results),
                "successful_iterations": len(
                    [r for r in iteration_results if self._is_successful_result(r)]
                ),
                "failed_iterations": len(
                    [r for r in iteration_results if not self._is_successful_result(r)]
                ),
            },
        }

        # Add loop metadata if requested
        if include_metadata and loop_metadata:
            output["loop_metadata"] = loop_metadata

        return output

    async def _collect_all(self, results: List[Dict[str, Any]]) -> List[Any]:
        """
        Collect all results including failed ones.

        For collect_all aggregation:
        - Group results by exit transition across all iterations
        - If multiple exit transitions: return [[exit1_iter1, exit1_iter2], [exit2_iter1, exit2_iter2]]
        - If single exit transition: return flattened list [iter1_result, iter2_result]
        - Always return a list, no transition ID keys
        """
        self.logger.info(
            f"🔍 COLLECT_ALL DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return []

        # Extract exit transition results and group by transition ID
        exit_transition_groups = {}

        for iteration_idx, result in enumerate(results):
            self.logger.info(
                f"🔍 COLLECT_ALL DEBUG: Processing iteration {iteration_idx}: {result}"
            )

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only collect from specified exit transitions
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Group by transition ID
                            if transition_id not in exit_transition_groups:
                                exit_transition_groups[transition_id] = []
                            exit_transition_groups[transition_id].append(actual_value)

                            self.logger.info(
                                f"🔍 COLLECT_ALL DEBUG: Added {actual_value} to group {transition_id}"
                            )
                        elif (
                            transition_id.startswith("transition-")
                            and self.exit_transitions
                        ):
                            self.logger.info(
                                f"🔍 COLLECT_ALL DEBUG: Skipping non-exit transition {transition_id}"
                            )

        self.logger.info(
            f"🔍 COLLECT_ALL DEBUG: Exit transition groups: {exit_transition_groups}"
        )
        self.logger.info(
            f"🔍 COLLECT_ALL DEBUG: Number of exit transition groups: {len(exit_transition_groups)}"
        )
        self.logger.info(
            f"🔍 COLLECT_ALL DEBUG: Exit transition group keys: {list(exit_transition_groups.keys())}"
        )

        # Convert to the expected format
        if len(exit_transition_groups) == 0:
            self.logger.info(
                f"🔍 COLLECT_ALL DEBUG: No exit transition groups found, returning empty list"
            )
            return []
        elif len(exit_transition_groups) == 1:
            # Single exit transition - return flattened list
            result = list(exit_transition_groups.values())[0]
            self.logger.info(
                f"🔍 COLLECT_ALL DEBUG: Single exit transition, returning flattened: {result}"
            )
            return result
        else:
            # Multiple exit transitions - return list of lists
            result = list(exit_transition_groups.values())
            self.logger.info(
                f"🔍 COLLECT_ALL DEBUG: Multiple exit transitions, returning list of lists: {result}"
            )
            return result

    async def _combine_text(self, results: List[Dict[str, Any]]) -> str:
        """
        Combine all results into a single string.

        Uses the same collection logic as collect_all but returns a string instead of arrays.
        All results from all exit transitions across all iterations are flattened and joined.

        Args:
            results: List of results from each iteration

        Returns:
            String containing all results joined together
        """
        self.logger.info(
            f"🔍 COMBINE_TEXT DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return ""

        # Extract exit transition results and group by transition ID (same as collect_all)
        exit_transition_groups = {}

        for iteration_idx, result in enumerate(results):
            self.logger.info(
                f"🔍 COMBINE_TEXT DEBUG: Processing iteration {iteration_idx}: {result}"
            )

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only collect from specified exit transitions
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Group by transition ID
                            if transition_id not in exit_transition_groups:
                                exit_transition_groups[transition_id] = []
                            exit_transition_groups[transition_id].append(actual_value)

                            self.logger.info(
                                f"🔍 COMBINE_TEXT DEBUG: Added {actual_value} to group {transition_id}"
                            )

        self.logger.info(
            f"🔍 COMBINE_TEXT DEBUG: Exit transition groups: {exit_transition_groups}"
        )

        # Flatten all groups into a single list
        all_values = []
        for group_values in exit_transition_groups.values():
            all_values.extend(group_values)

        self.logger.info(f"🔍 COMBINE_TEXT DEBUG: Flattened values: {all_values}")

        # Convert all values to strings and join them
        string_values = []
        for value in all_values:
            if isinstance(value, str):
                string_values.append(value)
            elif value is None:
                string_values.append("")
            elif isinstance(value, (dict, list)):
                # JSON-stringify complex objects (dicts and lists)
                try:
                    string_values.append(json.dumps(value, ensure_ascii=False))
                except (TypeError, ValueError) as e:
                    self.logger.warning(
                        f"🔍 COMBINE_TEXT DEBUG: Failed to JSON-stringify {type(value)}: {e}, using str() instead"
                    )
                    string_values.append(str(value))
            else:
                # Convert primitive types (int, float, bool, etc.) to string representation
                string_values.append(str(value))

        # Join with comma and space separator
        combined_result = ", ".join(string_values)

        self.logger.info(
            f"🔍 COMBINE_TEXT DEBUG: Final combined result: {combined_result}"
        )

        return combined_result

    async def _collect_successful(self, results: List[Dict[str, Any]]) -> List[Any]:
        """
        Collect only successful iteration results, filtering out failed iterations.

        Uses the same collection logic as collect_all but excludes failed iterations.
        Groups results by exit transition across successful iterations only.

        Args:
            results: List of results from each iteration

        Returns:
            List[List[Any]] for multiple exit transitions, List[Any] for single exit transition
        """
        self.logger.info(
            f"🔍 COLLECT_SUCCESSFUL DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return []

        # Filter out failed iterations first
        successful_iterations = []
        for iteration_idx, result in enumerate(results):
            if self._is_successful_iteration(result):
                successful_iterations.append(result)
                self.logger.info(
                    f"🔍 COLLECT_SUCCESSFUL DEBUG: Iteration {iteration_idx} is successful"
                )
            else:
                self.logger.info(
                    f"🔍 COLLECT_SUCCESSFUL DEBUG: Iteration {iteration_idx} failed, excluding from results"
                )

        self.logger.info(
            f"🔍 COLLECT_SUCCESSFUL DEBUG: {len(successful_iterations)} successful iterations out of {len(results)} total"
        )

        if not successful_iterations:
            self.logger.info(
                f"🔍 COLLECT_SUCCESSFUL DEBUG: No successful iterations found, returning empty list"
            )
            return []

        # Extract exit transition results and group by transition ID (same logic as collect_all)
        exit_transition_groups = {}

        for iteration_idx, result in enumerate(successful_iterations):
            self.logger.info(
                f"🔍 COLLECT_SUCCESSFUL DEBUG: Processing successful iteration {iteration_idx}: {result}"
            )

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only collect from specified exit transitions
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Only include non-null successful results
                            if actual_value is not None:
                                # Group by transition ID
                                if transition_id not in exit_transition_groups:
                                    exit_transition_groups[transition_id] = []
                                exit_transition_groups[transition_id].append(
                                    actual_value
                                )

                                self.logger.info(
                                    f"🔍 COLLECT_SUCCESSFUL DEBUG: Added {actual_value} to group {transition_id}"
                                )

        self.logger.info(
            f"🔍 COLLECT_SUCCESSFUL DEBUG: Exit transition groups: {exit_transition_groups}"
        )

        # Convert to the expected format (same as collect_all)
        if len(exit_transition_groups) == 0:
            self.logger.info(
                f"🔍 COLLECT_SUCCESSFUL DEBUG: No exit transition groups found, returning empty list"
            )
            return []
        elif len(exit_transition_groups) == 1:
            # Single exit transition - return flattened list
            result = list(exit_transition_groups.values())[0]
            self.logger.info(
                f"🔍 COLLECT_SUCCESSFUL DEBUG: Single exit transition, returning flattened: {result}"
            )
            return result
        else:
            # Multiple exit transitions - return list of lists
            result = list(exit_transition_groups.values())
            self.logger.info(
                f"🔍 COLLECT_SUCCESSFUL DEBUG: Multiple exit transitions, returning list of lists: {result}"
            )
            return result

    async def _collect_values(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Collect only the core values, stripping metadata."""
        values = []
        for result in results:
            if self._is_successful_result(result):
                value = self._extract_core_value(result)
                if value is not None:
                    values.append(value)
        return values

    async def _count_only(self, results: List[Dict[str, Any]]):
        """
        Count successful iterations for each exit transition.

        Returns only the count of successful results per exit transition,
        without including the actual result data. Optimized for performance
        monitoring and analytics scenarios.

        Args:
            results: List of results from each iteration

        Returns:
            List[int] for multiple exit transitions, int for single exit transition
        """
        self.logger.info(
            f"🔍 COUNT_ONLY DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return (
                0 if self.exit_transitions and len(self.exit_transitions) == 1 else []
            )

        # Count successful results for each exit transition
        exit_transition_counts = {}

        for iteration_idx, result in enumerate(results):
            self.logger.info(
                f"🔍 COUNT_ONLY DEBUG: Processing iteration {iteration_idx}: {result}"
            )

            # Only process successful iterations
            if not self._is_successful_iteration(result):
                self.logger.info(
                    f"🔍 COUNT_ONLY DEBUG: Iteration {iteration_idx} failed, skipping"
                )
                continue

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only count specified exit transitions
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Only count non-null successful results
                            if actual_value is not None:
                                # Initialize count if first time seeing this transition
                                if transition_id not in exit_transition_counts:
                                    exit_transition_counts[transition_id] = 0
                                exit_transition_counts[transition_id] += 1

                                self.logger.info(
                                    f"🔍 COUNT_ONLY DEBUG: Incremented count for {transition_id} to {exit_transition_counts[transition_id]}"
                                )

        self.logger.info(
            f"🔍 COUNT_ONLY DEBUG: Exit transition counts: {exit_transition_counts}"
        )

        # Initialize counts for exit transitions that had no successful results
        if self.exit_transitions:
            for transition_id in self.exit_transitions:
                if transition_id not in exit_transition_counts:
                    exit_transition_counts[transition_id] = 0
                    self.logger.info(
                        f"🔍 COUNT_ONLY DEBUG: Initialized count for {transition_id} to 0"
                    )

        # Convert to the expected format
        if len(exit_transition_counts) == 0:
            self.logger.info(
                f"🔍 COUNT_ONLY DEBUG: No exit transitions found, returning 0"
            )
            return 0
        elif len(exit_transition_counts) == 1:
            # Single exit transition - return single integer
            count = list(exit_transition_counts.values())[0]
            self.logger.info(
                f"🔍 COUNT_ONLY DEBUG: Single exit transition, returning count: {count}"
            )
            return count
        else:
            # Multiple exit transitions - return list of integers
            counts = list(exit_transition_counts.values())
            self.logger.info(
                f"🔍 COUNT_ONLY DEBUG: Multiple exit transitions, returning counts: {counts}"
            )
            return counts

    async def _first_successful(self, results: List[Dict[str, Any]]):
        """
        Return the earliest successful result from each exit transition.

        Finds the first successful iteration for each exit transition and returns
        only the earliest successful result per exit transition. Important for
        early-exit scenarios where the first successful result is sufficient.

        Args:
            results: List of results from each iteration

        Returns:
            List[Any] for multiple exit transitions, Any for single exit transition
        """
        self.logger.info(
            f"🔍 FIRST_SUCCESSFUL DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return (
                None
                if self.exit_transitions and len(self.exit_transitions) == 1
                else []
            )

        # Track the first successful result per exit transition
        exit_transition_first_results = {}

        # Track which exit transitions we've found first results for (for early termination)
        found_transitions = set()
        target_transitions = (
            set(self.exit_transitions) if self.exit_transitions else set()
        )

        # Process iterations in forward order to find first successful results
        for iteration_idx, result in enumerate(results):
            self.logger.info(
                f"🔍 FIRST_SUCCESSFUL DEBUG: Processing iteration {iteration_idx}: {result}"
            )

            # Only process successful iterations
            if not self._is_successful_iteration(result):
                self.logger.info(
                    f"🔍 FIRST_SUCCESSFUL DEBUG: Iteration {iteration_idx} failed, skipping"
                )
                continue

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only collect from specified exit transitions that we haven't found yet
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                            and transition_id not in exit_transition_first_results
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Only include non-null successful results
                            if actual_value is not None:
                                # Store the first result for this transition
                                exit_transition_first_results[transition_id] = (
                                    actual_value
                                )
                                found_transitions.add(transition_id)

                                self.logger.info(
                                    f"🔍 FIRST_SUCCESSFUL DEBUG: Found first result for {transition_id}: {actual_value} (iteration {iteration_idx})"
                                )

            # Early termination optimization: if we've found first results for all target transitions, stop
            if self.exit_transitions and found_transitions >= target_transitions:
                self.logger.info(
                    f"🔍 FIRST_SUCCESSFUL DEBUG: Found first results for all {len(target_transitions)} exit transitions, early termination at iteration {iteration_idx}"
                )
                break

        self.logger.info(
            f"🔍 FIRST_SUCCESSFUL DEBUG: Exit transition first results: {exit_transition_first_results}"
        )

        # Initialize null results for exit transitions that had no successful results
        if self.exit_transitions:
            for transition_id in self.exit_transitions:
                if transition_id not in exit_transition_first_results:
                    exit_transition_first_results[transition_id] = None
                    self.logger.info(
                        f"🔍 FIRST_SUCCESSFUL DEBUG: No successful result found for {transition_id}, setting to null"
                    )

        # Convert to the expected format
        if len(exit_transition_first_results) == 0:
            self.logger.info(
                f"🔍 FIRST_SUCCESSFUL DEBUG: No exit transitions found, returning None"
            )
            return None
        elif len(exit_transition_first_results) == 1:
            # Single exit transition - return single value
            result = list(exit_transition_first_results.values())[0]
            self.logger.info(
                f"🔍 FIRST_SUCCESSFUL DEBUG: Single exit transition, returning value: {result}"
            )
            return result
        else:
            # Multiple exit transitions - return list of values
            results = list(exit_transition_first_results.values())
            self.logger.info(
                f"🔍 FIRST_SUCCESSFUL DEBUG: Multiple exit transitions, returning values: {results}"
            )
            return results

    async def _last_successful(self, results: List[Dict[str, Any]]):
        """
        Return the most recent successful result from each exit transition.

        Finds the last successful iteration for each exit transition and returns
        only the most recent successful result per exit transition. Valuable for
        workflows where only the latest successful state is relevant.

        Args:
            results: List of results from each iteration

        Returns:
            List[Any] for multiple exit transitions, Any for single exit transition
        """
        self.logger.info(
            f"🔍 LAST_SUCCESSFUL DEBUG: Processing {len(results)} iteration results"
        )

        if not results:
            return (
                None
                if self.exit_transitions and len(self.exit_transitions) == 1
                else []
            )

        # Track the most recent successful result per exit transition
        exit_transition_last_results = {}

        # Process iterations in forward order, so later iterations overwrite earlier ones
        for iteration_idx, result in enumerate(results):
            self.logger.info(
                f"🔍 LAST_SUCCESSFUL DEBUG: Processing iteration {iteration_idx}: {result}"
            )

            # Only process successful iterations
            if not self._is_successful_iteration(result):
                self.logger.info(
                    f"🔍 LAST_SUCCESSFUL DEBUG: Iteration {iteration_idx} failed, skipping"
                )
                continue

            # Extract exit transition results from this iteration
            if "all_results" in result:
                all_results = result["all_results"]
                if isinstance(all_results, dict):
                    for transition_id, transition_result in all_results.items():
                        # Only collect from specified exit transitions
                        if (
                            transition_id.startswith("transition-")
                            and transition_result is not None
                            and (
                                self.exit_transitions is None
                                or transition_id in self.exit_transitions
                            )
                        ):

                            # Extract the actual value
                            if (
                                isinstance(transition_result, dict)
                                and "result" in transition_result
                            ):
                                actual_value = transition_result["result"]
                            else:
                                actual_value = transition_result

                            # Only include non-null successful results
                            if actual_value is not None:
                                # Store/overwrite with the latest result for this transition
                                exit_transition_last_results[transition_id] = (
                                    actual_value
                                )

                                self.logger.info(
                                    f"🔍 LAST_SUCCESSFUL DEBUG: Updated last result for {transition_id} to {actual_value} (iteration {iteration_idx})"
                                )

        self.logger.info(
            f"🔍 LAST_SUCCESSFUL DEBUG: Exit transition last results: {exit_transition_last_results}"
        )

        # Initialize null results for exit transitions that had no successful results
        if self.exit_transitions:
            for transition_id in self.exit_transitions:
                if transition_id not in exit_transition_last_results:
                    exit_transition_last_results[transition_id] = None
                    self.logger.info(
                        f"🔍 LAST_SUCCESSFUL DEBUG: No successful result found for {transition_id}, setting to null"
                    )

        # Convert to the expected format
        if len(exit_transition_last_results) == 0:
            self.logger.info(
                f"🔍 LAST_SUCCESSFUL DEBUG: No exit transitions found, returning None"
            )
            return None
        elif len(exit_transition_last_results) == 1:
            # Single exit transition - return single value
            result = list(exit_transition_last_results.values())[0]
            self.logger.info(
                f"🔍 LAST_SUCCESSFUL DEBUG: Single exit transition, returning value: {result}"
            )
            return result
        else:
            # Multiple exit transitions - return list of values
            results = list(exit_transition_last_results.values())
            self.logger.info(
                f"🔍 LAST_SUCCESSFUL DEBUG: Multiple exit transitions, returning values: {results}"
            )
            return results

    async def _merge_objects(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge all successful results into a single object."""
        merged = {}

        for result in results:
            if self._is_successful_result(result):
                value = self._extract_result_value(result)
                if isinstance(value, dict):
                    merged.update(value)
                else:
                    # Use iteration index as key if value is not a dict
                    iteration_index = result.get("iteration_index", len(merged))
                    merged[f"iteration_{iteration_index}"] = value

        return merged

    async def _flatten_arrays(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Flatten all array results into a single array."""
        flattened = []

        for result in results:
            if self._is_successful_result(result):
                value = self._extract_result_value(result)
                if isinstance(value, list):
                    flattened.extend(value)
                else:
                    flattened.append(value)

        return flattened

    def _is_successful_result(self, result: Dict[str, Any]) -> bool:
        """Check if a result represents a successful iteration."""
        status = result.get("status", "completed")
        return status not in ["failed", "error", "timeout"]

    def _is_successful_iteration(self, iteration_result: Dict[str, Any]) -> bool:
        """
        Check if an entire iteration is considered successful.

        An iteration is successful if:
        1. It has a successful status
        2. It contains valid results in all_results
        3. At least one exit transition has a non-null result

        Args:
            iteration_result: Result from a single iteration

        Returns:
            True if the iteration is successful, False otherwise
        """
        # Check overall iteration status
        if not self._is_successful_result(iteration_result):
            return False

        # Check if we have all_results
        if "all_results" not in iteration_result:
            return False

        all_results = iteration_result["all_results"]
        if not isinstance(all_results, dict):
            return False

        # Check if at least one exit transition has a successful result
        has_successful_exit_result = False
        for transition_id, transition_result in all_results.items():
            # Only check exit transitions
            if transition_id.startswith("transition-") and (
                self.exit_transitions is None or transition_id in self.exit_transitions
            ):

                # Extract the actual value
                if (
                    isinstance(transition_result, dict)
                    and "result" in transition_result
                ):
                    actual_value = transition_result["result"]
                else:
                    actual_value = transition_result

                # If we find at least one non-null result from an exit transition, iteration is successful
                if actual_value is not None:
                    has_successful_exit_result = True
                    break

        return has_successful_exit_result

    def _extract_iteration_exit_results(self, iteration_result: Dict[str, Any]) -> Any:
        """
        Extract results from exit transitions in an iteration result.

        For collect_all, we need to extract the actual values from exit transitions
        and return them in the correct format for aggregation.

        Args:
            iteration_result: Result from a single iteration

        Returns:
            - Single value if only one exit transition with non-null result
            - List of values if multiple exit transitions with non-null results
            - None if no valid results found
        """
        self.logger.debug(
            f"🔍 DEBUG: _extract_iteration_exit_results input: {iteration_result}"
        )
        self.logger.debug(
            f"🔍 DEBUG: iteration_result keys: {list(iteration_result.keys()) if isinstance(iteration_result, dict) else 'not dict'}"
        )
        # Try to extract from all_results first (this contains the transition results)
        if "all_results" in iteration_result:
            all_results = iteration_result["all_results"]
            self.logger.debug(f"🔍 DEBUG: all_results: {all_results}")
            if isinstance(all_results, dict):
                # Find non-null results from exit transitions
                exit_results = []
                for key, value in all_results.items():
                    self.logger.debug(f"🔍 DEBUG: checking {key}: {value}")
                    if key.startswith("transition-") and value is not None:
                        # Extract the actual value from the transition result
                        # The value should be in format {"result": actual_data}
                        if isinstance(value, dict) and "result" in value:
                            actual_value = value["result"]
                            self.logger.debug(
                                f"🔍 DEBUG: extracted actual_value from {key}: {actual_value}"
                            )
                            exit_results.append(actual_value)
                        else:
                            self.logger.debug(
                                f"🔍 DEBUG: value for {key} is not in expected format: {value}"
                            )
                            exit_results.append(value)

                self.logger.debug(f"🔍 DEBUG: exit_results: {exit_results}")

                # Return based on number of exit results
                if len(exit_results) == 0:
                    return None
                elif len(exit_results) == 1:
                    return exit_results[0]
                else:
                    # Multiple exit transitions - return as list
                    return exit_results

        # Fallback: try the main result field
        if "result" in iteration_result:
            main_result = iteration_result["result"]
            if main_result is not None:
                return main_result

        # Last fallback
        return None

    def _extract_result_value(self, result: Dict[str, Any]) -> Any:
        """Extract the main result value from an iteration result."""
        # Try different possible result locations
        if "result" in result:
            return result["result"]
        elif "value" in result:
            return result["value"]
        elif "output" in result:
            return result["output"]
        elif "data" in result:
            return result["data"]
        else:
            # Return the whole result if no specific value field found
            return result

    def _extract_core_value(self, result: Dict[str, Any]) -> Any:
        """Extract the core value, stripping away metadata."""
        value = self._extract_result_value(result)

        # If the value is a dict with metadata, try to extract the core content
        if isinstance(value, dict):
            # Common patterns for core values
            for key in ["content", "value", "data", "result", "output"]:
                if key in value:
                    return value[key]

            # If no common pattern, check if it looks like metadata
            metadata_keys = {
                "status",
                "timestamp",
                "iteration_index",
                "metadata",
                "error",
            }
            value_keys = set(value.keys())

            # If most keys are metadata, try to find the actual content
            if len(metadata_keys.intersection(value_keys)) > len(value_keys) / 2:
                content_keys = value_keys - metadata_keys
                if len(content_keys) == 1:
                    return value[list(content_keys)[0]]

        return value

    async def format_current_item_output(
        self,
        iteration_result: Dict[str, Any],
        iteration_index: int,
        iteration_item: Any,
    ) -> Dict[str, Any]:
        """
        Format output for current_item handle during iteration.

        This is used for the dual output system where current_item
        provides the result of the current iteration.
        """
        return {
            "output_type": "iteration_output",
            "current_item": self._extract_result_value(iteration_result),
            "final_results": None,  # Not available during iteration
            "iteration_metadata": {
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
                "status": iteration_result.get("status", "completed"),
            },
        }

    async def format_error_result(
        self,
        error: Exception,
        iteration_index: int,
        iteration_item: Any,
        include_in_results: bool = True,
    ) -> Dict[str, Any]:
        """
        Format an error result for aggregation.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            iteration_item: Item that was being processed
            include_in_results: Whether to include this error in final results

        Returns:
            Formatted error result
        """
        error_result = {
            "iteration_index": iteration_index,
            "iteration_item": iteration_item,
            "error": str(error),
            "error_type": type(error).__name__,
            "status": "failed",
            "timestamp": self._get_current_timestamp(),
        }

        if not include_in_results:
            error_result["excluded_from_results"] = True

        return error_result

    async def _return_original(
        self, results: List[Dict[str, Any]], loop_metadata: Dict[str, Any] = None
    ):
        """
        Return the original input data that was provided to the loop node.

        Executes all loop iterations normally but ignores the iteration results
        and returns the original input data instead. This is valuable for workflows
        where the loop execution is needed for side effects but the original input
        should be preserved for downstream processing.

        Args:
            results: List of results from each iteration (ignored)
            loop_metadata: Loop metadata containing original input data

        Returns:
            The original input data that was provided to the loop node
        """
        self.logger.info(
            f"🔍 RETURN_ORIGINAL DEBUG: Processing {len(results)} iteration results"
        )
        self.logger.info(
            f"🔍 RETURN_ORIGINAL DEBUG: All iteration results will be ignored"
        )

        # Check if original input data is available in loop metadata
        if not loop_metadata:
            self.logger.warning(
                "🔍 RETURN_ORIGINAL DEBUG: No loop metadata provided, returning None"
            )
            return None

        original_input = loop_metadata.get("original_input_data")
        if original_input is None:
            self.logger.warning(
                "🔍 RETURN_ORIGINAL DEBUG: No original_input_data found in loop metadata, returning None"
            )
            return None

        self.logger.info(
            f"🔍 RETURN_ORIGINAL DEBUG: Returning original input data: {original_input}"
        )
        self.logger.info(
            f"🔍 RETURN_ORIGINAL DEBUG: Loop executed {len(results)} iterations for side effects"
        )

        return original_input

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime

        return datetime.now().isoformat()

    async def validate_aggregation_config(
        self, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate and normalize aggregation configuration.

        Args:
            config: Raw aggregation configuration

        Returns:
            Validated and normalized configuration
        """
        valid_types = {
            "collect_all",
            "collect_successful",
            "collect_values",
            "count_only",
            "first_successful",
            "last_successful",
            "merge_objects",
            "flatten_arrays",
        }

        aggregation_type = config.get("aggregation_type", "collect_all")
        if aggregation_type not in valid_types:
            self.logger.warning(
                f"Invalid aggregation type: {aggregation_type}, using collect_all"
            )
            aggregation_type = "collect_all"

        return {
            "aggregation_type": aggregation_type,
            "include_metadata": config.get("include_metadata", False),
            "include_errors": config.get("include_errors", True),
            "preserve_order": config.get("preserve_order", True),
        }
