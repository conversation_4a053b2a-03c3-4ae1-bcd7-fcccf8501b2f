"""
Loop State Manager - Manages loop execution state and integrates with main workflow state.

This module provides state management for loop execution, including iteration tracking,
dependency management, and integration with the main WorkflowStateManager.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopStateManager")


class LoopStateManager:
    """
    Manages state for loop execution and integrates with main workflow state management.

    This class handles:
    - Loop iteration state tracking
    - Integration with main state manager for dependency tracking
    - Loop body transition state management
    - Persistence and recovery of loop state
    """

    def __init__(
        self, loop_id: str, transition_id: str, workflow_id: str, main_state_manager
    ):
        """
        Initialize the loop state manager.

        Args:
            loop_id: Unique identifier for this loop instance
            transition_id: ID of the loop transition
            workflow_id: ID of the parent workflow
            main_state_manager: Reference to the main workflow state manager
        """
        self.loop_id = loop_id
        self.transition_id = transition_id
        self.workflow_id = workflow_id
        self.main_state_manager = main_state_manager
        self.logger = logger

        # Loop execution state
        self.loop_status = (
            "initializing"  # initializing, running, completed, failed, terminated
        )
        self.current_iteration_index = 0
        self.total_iterations = 0
        self.completed_iterations = 0
        self.failed_iterations = 0

        # Iteration tracking
        self.iteration_states: Dict[int, str] = {}  # {index: status}
        self.iteration_results: Dict[int, Any] = {}  # {index: result}
        self.iteration_errors: Dict[int, str] = {}  # {index: error_message}

        # Loop body transition states (managed independently from main state manager)
        self.pending_transitions: List[str] = []  # Ready to execute now
        self.waiting_transitions: List[str] = []  # Waiting for dependencies
        self.completed_transitions: List[str] = (
            []
        )  # Already executed in current iteration

        # Loop body configuration (will be set during initialization)
        self.entry_transitions: List[str] = []
        self.exit_transitions: List[str] = []
        self.loop_body_dependency_map: Dict[str, List[str]] = (
            {}
        )  # transition_id -> [dependencies]

        # Loop body transition tracking
        self.loop_body_transitions: List[str] = []
        self.entry_transitions: List[str] = []
        self.exit_transitions: List[str] = []

        # Note: loop_body_dependency_map is initialized via initialize_loop_body_configuration()
        self.iteration_pending_transitions: Dict[int, Set[str]] = {}
        self.iteration_completed_transitions: Dict[int, Set[str]] = {}
        self.iteration_waiting_transitions: Dict[int, Set[str]] = {}

        # Timing and metadata
        self.start_time = None
        self.end_time = None
        self.iteration_start_times: Dict[int, datetime] = {}
        self.iteration_end_times: Dict[int, datetime] = {}

    async def initialize_loop_state(self, loop_config: Dict[str, Any]) -> None:
        """
        Initialize loop state from configuration.

        Args:
            loop_config: Complete loop configuration
        """
        self.logger.info(f"🔧 Initializing loop state for loop {self.loop_id}")

        # Extract loop body configuration
        loop_body_config = loop_config.get("loop_body_configuration", {})
        self.entry_transitions = loop_body_config.get("entry_transitions", [])
        self.exit_transitions = loop_body_config.get("exit_transitions", [])

        # Note: Loop body dependency map will be set via initialize_loop_body_configuration()

        # Calculate total iterations
        iteration_source = loop_config.get("iteration_source", {})
        self.total_iterations = await self._calculate_total_iterations(iteration_source)

        # Initialize iteration tracking
        for i in range(self.total_iterations):
            self.iteration_states[i] = "pending"
            self.iteration_pending_transitions[i] = set()
            self.iteration_completed_transitions[i] = set()
            self.iteration_waiting_transitions[i] = set()

        # Update status and store state
        self.loop_status = "initialized"
        self.start_time = datetime.now()

        await self._store_loop_state()

        self.logger.info(
            f"✅ Loop state initialized: {self.total_iterations} iterations planned"
        )

    def initialize_loop_body_configuration(
        self, loop_config: Dict[str, Any], dependency_map: Dict[str, List[str]]
    ) -> None:
        """
        Initialize loop body configuration and dependency map.

        Args:
            loop_config: Loop configuration containing entry/exit transitions
            dependency_map: Full workflow dependency map
        """
        # Extract loop body configuration
        loop_body_config = loop_config.get("loop_body_configuration", {})
        self.entry_transitions = loop_body_config.get("entry_transitions", [])
        self.exit_transitions = loop_body_config.get("exit_transitions", [])

        # Check for loop_body_transitions as fallback when entry/exit are not defined
        loop_body_transitions = loop_config.get("loop_body_transitions", [])

        if (
            not self.entry_transitions
            and not self.exit_transitions
            and loop_body_transitions
        ):
            self.logger.info(
                f"🔍 Using loop_body_transitions as fallback: {loop_body_transitions}"
            )
            # For simple cases, treat all loop_body_transitions as both entry and exit
            self.entry_transitions = loop_body_transitions.copy()
            self.exit_transitions = loop_body_transitions.copy()

        # Build loop body dependency map (subset of main dependency map)
        self._build_loop_body_dependency_map(dependency_map)

        self.logger.info(
            f"🔧 Loop body configured - Entry: {self.entry_transitions}, Exit: {self.exit_transitions}"
        )
        self.logger.debug(f"🔗 Loop body dependencies: {self.loop_body_dependency_map}")

    def _build_loop_body_dependency_map(
        self, full_dependency_map: Dict[str, List[str]]
    ) -> None:
        """
        Build dependency map for loop body transitions only.
        Auto-detects all transitions in the loop body chain from entry to exit.

        Args:
            full_dependency_map: Complete workflow dependency map
        """
        # Auto-detect all loop body transitions by following the dependency chain
        all_loop_body_transitions = self._discover_loop_body_transitions(
            full_dependency_map
        )

        self.logger.debug(
            f"🔍 Discovered loop body transitions: {all_loop_body_transitions}"
        )

        # Extract dependencies for loop body transitions only
        self.loop_body_dependency_map = {}
        for transition_id in all_loop_body_transitions:
            if transition_id in full_dependency_map:
                # Only include dependencies that are also within the loop body
                loop_body_deps = [
                    dep
                    for dep in full_dependency_map[transition_id]
                    if dep in all_loop_body_transitions
                ]
                self.loop_body_dependency_map[transition_id] = loop_body_deps
            else:
                self.loop_body_dependency_map[transition_id] = []

        self.logger.debug(
            f"🔗 Built loop body dependency map: {self.loop_body_dependency_map}"
        )

    def _discover_loop_body_transitions(
        self, full_dependency_map: Dict[str, List[str]]
    ) -> set:
        """
        Discover all transitions that are part of the loop body.

        For simple cases where entry/exit transitions are the same (single node loop bodies),
        we just use those. For complex cases, we follow the dependency chain.

        Args:
            full_dependency_map: Complete workflow dependency map

        Returns:
            Set of all transition IDs in the loop body
        """
        loop_body_transitions = set()

        # Start with entry and exit transitions
        loop_body_transitions.update(self.entry_transitions)
        loop_body_transitions.update(self.exit_transitions)

        # If entry and exit are the same (simple single-node loop body), we're done
        if (
            set(self.entry_transitions) == set(self.exit_transitions)
            and self.entry_transitions
        ):
            self.logger.debug(f"🔍 Simple loop body detected: {loop_body_transitions}")
            return loop_body_transitions

        # For complex cases, follow the dependency chain
        if self.entry_transitions and self.exit_transitions:
            # Build reverse dependency map to find what depends on what
            reverse_deps = {}
            for transition_id, deps in full_dependency_map.items():
                for dep in deps:
                    if dep not in reverse_deps:
                        reverse_deps[dep] = []
                    reverse_deps[dep].append(transition_id)

            # Follow the chain from entry transitions to exit transitions
            to_explore = set(self.entry_transitions)
            explored = set()

            while to_explore:
                current = to_explore.pop()
                if current in explored:
                    continue

                explored.add(current)
                loop_body_transitions.add(current)

                # Find transitions that depend on this one
                dependents = reverse_deps.get(current, [])
                for dependent in dependents:
                    # Only include if it leads to an exit transition
                    if self._leads_to_exit_transition(dependent, reverse_deps, set()):
                        to_explore.add(dependent)

        return loop_body_transitions

    def _leads_to_exit_transition(
        self, transition_id: str, reverse_deps: Dict[str, List[str]], visited: set
    ) -> bool:
        """
        Check if a transition eventually leads to an exit transition.

        Args:
            transition_id: Transition to check
            reverse_deps: Reverse dependency map
            visited: Set of already visited transitions to avoid cycles

        Returns:
            True if the transition leads to an exit transition
        """
        if transition_id in visited:
            return False

        if transition_id in self.exit_transitions:
            return True

        visited.add(transition_id)

        # Check if any dependent transitions lead to exit
        dependents = reverse_deps.get(transition_id, [])
        for dependent in dependents:
            if self._leads_to_exit_transition(dependent, reverse_deps, visited.copy()):
                return True

        return False

    async def start_iteration(self, iteration_index: int, iteration_item: Any) -> None:
        """
        Start a specific iteration.

        Args:
            iteration_index: Index of the iteration to start
            iteration_item: The item being processed in this iteration
        """
        self.logger.info(
            f"🚀 Starting iteration {iteration_index + 1}/{self.total_iterations}"
        )

        self.current_iteration_index = iteration_index
        self.iteration_states[iteration_index] = "running"
        self.iteration_start_times[iteration_index] = datetime.now()

        # Initialize transition tracking for this iteration
        if self.entry_transitions:
            # Add entry transitions to pending for this iteration
            for entry_transition in self.entry_transitions:
                iteration_transition_id = self._get_iteration_transition_id(
                    entry_transition, iteration_index
                )
                self.iteration_pending_transitions[iteration_index].add(
                    iteration_transition_id
                )

                # Also add to main state manager's pending transitions
                self.main_state_manager.pending_transitions.add(iteration_transition_id)

        await self._store_loop_state()

    async def complete_iteration(
        self, iteration_index: int, result: Any, status: str = "completed"
    ) -> None:
        """
        Mark an iteration as completed.

        Args:
            iteration_index: Index of the completed iteration
            result: Result of the iteration
            status: Status of completion (completed, failed, etc.)
        """
        self.logger.info(
            f"✅ Completing iteration {iteration_index + 1}/{self.total_iterations} with status: {status}"
        )

        self.iteration_states[iteration_index] = status
        self.iteration_results[iteration_index] = result
        self.iteration_end_times[iteration_index] = datetime.now()

        if status == "completed":
            self.completed_iterations += 1
        elif status == "failed":
            self.failed_iterations += 1

        # Clean up transition tracking for this iteration
        await self._cleanup_iteration_transitions(iteration_index)

        await self._store_loop_state()

    async def handle_transition_completion(
        self, transition_id: str, iteration_index: int, result: Any
    ) -> List[str]:
        """
        Handle completion of a transition within a loop iteration.

        Args:
            transition_id: ID of the completed transition
            result: Result of the transition
            iteration_index: Index of the current iteration

        Returns:
            List of next transitions to execute
        """
        self.logger.debug(
            f"🔄 Handling transition completion: {transition_id} for iteration {iteration_index}"
        )

        # Mark transition as completed for this iteration
        self.iteration_completed_transitions[iteration_index].add(transition_id)

        # Remove from pending if it was there
        self.iteration_pending_transitions[iteration_index].discard(transition_id)

        # Check if this is an exit transition
        if self._is_exit_transition(transition_id):
            self.logger.info(
                f"🏁 Exit transition completed for iteration {iteration_index}"
            )
            return []  # No more transitions for this iteration

        # Find next transitions based on dependencies
        next_transitions = await self._find_next_transitions(
            transition_id, iteration_index
        )

        # Add next transitions to pending for this iteration
        for next_transition in next_transitions:
            iteration_transition_id = self._get_iteration_transition_id(
                next_transition, iteration_index
            )
            self.iteration_pending_transitions[iteration_index].add(
                iteration_transition_id
            )

            # Also add to main state manager
            self.main_state_manager.pending_transitions.add(iteration_transition_id)

        await self._store_loop_state()
        return next_transitions

    # NEW STATE MANAGEMENT METHODS FOR LOOP BODY TRANSITIONS

    def get_pending_transitions(self) -> List[str]:
        """
        Get transitions that are ready to execute in the current iteration.

        Returns:
            List of transition IDs ready for execution
        """
        return self.pending_transitions.copy()

    def mark_transition_completed(self, transition_id: str) -> None:
        """
        Mark a transition as completed in the current iteration.

        Args:
            transition_id: ID of the completed transition
        """
        # Remove from pending if present
        if transition_id in self.pending_transitions:
            self.pending_transitions.remove(transition_id)

        # Remove from waiting if present
        if transition_id in self.waiting_transitions:
            self.waiting_transitions.remove(transition_id)

        # Add to completed
        if transition_id not in self.completed_transitions:
            self.completed_transitions.append(transition_id)

        self.logger.debug(f"✅ Marked {transition_id} as completed")

        # Check for newly available transitions
        self._update_pending_transitions()

    def _update_pending_transitions(self) -> None:
        """
        Update pending transitions based on completed dependencies.
        Move transitions from waiting to pending when their dependencies are met.
        """
        newly_pending = []

        for transition_id in self.waiting_transitions.copy():
            dependencies = self.loop_body_dependency_map.get(transition_id, [])

            # Check if all dependencies are completed
            if all(dep in self.completed_transitions for dep in dependencies):
                self.waiting_transitions.remove(transition_id)
                newly_pending.append(transition_id)

        # Add newly available transitions to pending
        self.pending_transitions.extend(newly_pending)

        if newly_pending:
            self.logger.debug(f"🔓 Newly available transitions: {newly_pending}")

    def is_iteration_body_complete(self) -> bool:
        """
        Check if the current iteration's loop body execution is complete.

        Returns:
            True if all exit transitions are completed
        """
        return all(
            exit_t in self.completed_transitions for exit_t in self.exit_transitions
        )

    def clear_iteration_state(self) -> None:
        """
        Clear transition states for the current iteration.
        Called at the start of each new iteration.
        """
        self.completed_transitions.clear()
        self.pending_transitions.clear()
        self.waiting_transitions.clear()

        # Re-initialize for new iteration
        self._initialize_iteration_transition_states()

        self.logger.debug(f"🔄 Cleared iteration state - Ready for new iteration")

    def _initialize_iteration_transition_states(self) -> None:
        """
        Initialize transition states for the current iteration.
        Entry transitions start as pending, others start as waiting.
        """
        # Entry transitions are ready to execute immediately
        self.pending_transitions = self.entry_transitions.copy()

        # All other loop body transitions start as waiting
        # Use all discovered loop body transitions, not just entry + exit
        all_loop_transitions = set(self.loop_body_dependency_map.keys())
        self.waiting_transitions = [
            t for t in all_loop_transitions if t not in self.entry_transitions
        ]

        self.logger.debug(
            f"📋 Initialized iteration state - Pending: {self.pending_transitions}, Waiting: {self.waiting_transitions}"
        )
        self.logger.debug(f"📋 All loop body transitions: {all_loop_transitions}")

    async def is_iteration_complete(self, iteration_index: int) -> bool:
        """
        Check if a specific iteration is complete.

        Args:
            iteration_index: Index of the iteration to check

        Returns:
            True if iteration is complete
        """
        # Check if any exit transitions have completed
        for exit_transition in self.exit_transitions:
            iteration_transition_id = self._get_iteration_transition_id(
                exit_transition, iteration_index
            )
            if (
                iteration_transition_id
                in self.iteration_completed_transitions[iteration_index]
            ):
                return True

        # Check if all pending transitions are complete
        return len(self.iteration_pending_transitions[iteration_index]) == 0

    async def is_loop_complete(self) -> bool:
        """
        Check if the entire loop is complete.

        Returns:
            True if all iterations are complete
        """
        for i in range(self.total_iterations):
            if not await self.is_iteration_complete(i):
                return False
        return True

    async def update_iteration_progress(
        self, iteration_index: int, status: str
    ) -> None:
        """
        Update the progress status of an iteration.

        Args:
            iteration_index: Index of the iteration
            status: New status
        """
        self.iteration_states[iteration_index] = status
        await self._store_loop_state()

    async def get_loop_progress(self) -> Dict[str, Any]:
        """
        Get current loop progress information.

        Returns:
            Dictionary containing progress information
        """
        return {
            "loop_id": self.loop_id,
            "status": self.loop_status,
            "current_iteration": self.current_iteration_index,
            "total_iterations": self.total_iterations,
            "completed_iterations": self.completed_iterations,
            "failed_iterations": self.failed_iterations,
            "progress_percentage": (
                (self.completed_iterations / self.total_iterations * 100)
                if self.total_iterations > 0
                else 0
            ),
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
        }

    async def cleanup(self) -> None:
        """Clean up loop state and remove from main state manager."""
        self.logger.info(f"🧹 Cleaning up loop state for loop {self.loop_id}")

        self.loop_status = "completed"
        self.end_time = datetime.now()

        # Remove loop state from main state manager
        self.main_state_manager.remove_loop_state(self.loop_id, self.transition_id)

        # Clean up any remaining iteration transitions
        for iteration_index in range(self.total_iterations):
            await self._cleanup_iteration_transitions(iteration_index)

    async def _calculate_total_iterations(
        self, iteration_source: Dict[str, Any]
    ) -> int:
        """Calculate total number of iterations from source configuration."""
        source_type = iteration_source.get("source_type", "iteration_list")

        if source_type == "iteration_list":
            iteration_list = iteration_source.get("iteration_list", [])
            batch_size = iteration_source.get("batch_size", 1)

            # Convert batch_size to int if it's a string (common from tool parameters)
            try:
                batch_size_int = int(batch_size) if isinstance(batch_size, str) else batch_size
                if batch_size_int <= 0:
                    batch_size_int = 1  # Default to 1 if invalid

                return len(iteration_list) // batch_size_int + (
                    1 if len(iteration_list) % batch_size_int > 0 else 0
                )
            except (ValueError, TypeError) as e:
                self.logger.error(f"Invalid batch_size value: {batch_size}. Using default batch_size=1. Error: {e}")
                return len(iteration_list)  # Default to no batching

        elif source_type == "number_range":
            range_config = iteration_source.get("number_range", {})
            start = range_config.get("start")
            end = range_config.get("end")
            step = iteration_source.get("step", 1)

            # Validate that start and end are provided (not None)
            if start is None or end is None:
                self.logger.error(f"Loop range configuration incomplete: start={start}, end={end}")
                return 0

            # Convert string values to integers for range calculation
            try:
                start_int = int(start) if isinstance(start, str) else start
                end_int = int(end) if isinstance(end, str) else end
                step_int = int(step) if isinstance(step, str) else step

                return len(range(start_int, end_int + 1, step_int))
            except (ValueError, TypeError) as e:
                self.logger.error(f"Invalid range values: start={start}, end={end}, step={step}. Error: {e}")
                return 0

        return 0

    def _get_iteration_transition_id(
        self, base_transition_id: str, iteration_index: int
    ) -> str:
        """Generate unique transition ID for a specific iteration."""
        return f"{base_transition_id}_iteration_{iteration_index}"

    def _is_exit_transition(self, transition_id: str) -> bool:
        """Check if a transition ID is an exit transition."""
        # Remove iteration suffix if present
        base_transition_id = transition_id.split("_iteration_")[0]
        return base_transition_id in self.exit_transitions

    async def _find_next_transitions(
        self, completed_transition_id: str, iteration_index: int
    ) -> List[str]:
        """
        Find next transitions to execute after a transition completes.
        Uses the actual workflow dependency map to determine proper execution order.
        """
        # Get the base transition ID (without iteration suffix)
        base_transition_id = completed_transition_id.split("_iteration_")[0]

        # Build reverse dependency map to find what depends on this transition
        next_transitions = []

        for transition_id, deps in self.loop_body_dependency_map.items():
            # If this transition depends on the completed one, it might be ready
            if base_transition_id in deps:
                # Check if all other dependencies are also completed for this iteration
                all_deps_completed = all(
                    dep in self.iteration_completed_transitions[iteration_index]
                    or dep == base_transition_id
                    for dep in deps
                )

                if all_deps_completed:
                    next_transitions.append(transition_id)

        self.logger.debug(
            f"🔄 Next transitions after {base_transition_id}: {next_transitions}"
        )
        return next_transitions

    async def _cleanup_iteration_transitions(self, iteration_index: int) -> None:
        """Clean up transition tracking for a specific iteration."""
        # Remove iteration transitions from main state manager
        for transition_set in [
            self.iteration_pending_transitions[iteration_index],
            self.iteration_waiting_transitions[iteration_index],
        ]:
            for transition_id in transition_set:
                self.main_state_manager.pending_transitions.discard(transition_id)
                self.main_state_manager.waiting_transitions.discard(transition_id)

        # Clear iteration transition sets
        self.iteration_pending_transitions[iteration_index].clear()
        self.iteration_waiting_transitions[iteration_index].clear()

    async def _store_loop_state(self) -> None:
        """Store current loop state in the main state manager."""
        loop_state_data = {
            "loop_id": self.loop_id,
            "transition_id": self.transition_id,
            "status": self.loop_status,
            "current_iteration": self.current_iteration_index,
            "total_iterations": self.total_iterations,
            "completed_iterations": self.completed_iterations,
            "failed_iterations": self.failed_iterations,
            "iteration_states": self.iteration_states,
            "iteration_results": self.iteration_results,
            "iteration_errors": self.iteration_errors,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "entry_transitions": self.entry_transitions,
            "exit_transitions": self.exit_transitions,
        }

        self.main_state_manager.store_loop_state(
            self.loop_id, self.transition_id, loop_state_data
        )
