"""
Loop Executor - Main orchestrator for loop execution.

This module provides the core loop execution functionality that integrates with
the orchestration engine's transition handler and state management system.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional

from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopExecutor")


class LoopExecutor:
    """
    Main loop executor that handles dynamic loop configuration and execution.

    This class processes loop transitions with different iteration sources,
    manages loop body chain execution, and coordinates with the main workflow
    state management system.
    """

    def __init__(
        self,
        state_manager,
        workflow_utils,
        result_callback,
        transitions_by_id: Dict[str, Any],
        nodes: Dict[str, Any],
        transition_handler,
        user_id: Optional[str] = None,
    ):
        """
        Initialize the LoopExecutor.

        Args:
            state_manager: Main workflow state manager
            workflow_utils: Workflow utilities for parameter resolution
            result_callback: Callback for result handling
            transitions_by_id: Dictionary of all transitions in the workflow
            nodes: Dictionary of all nodes in the workflow
            transition_handler: Main transition handler instance
            user_id: Optional user ID for execution context
        """
        self.state_manager = state_manager
        self.workflow_utils = workflow_utils
        self.result_callback = result_callback
        self.transitions_by_id = transitions_by_id
        self.nodes = nodes
        self.transition_handler = transition_handler
        self.user_id = user_id
        self.logger = logger

        # Loop execution state
        self.current_loop_id = None
        self.current_transition_id = None
        self.loop_state_manager = None
        self.loop_body_executor = None

    def set_orchestration_engine(self, orchestration_engine):
        """Set the orchestration engine reference for proper coordination."""
        self.orchestration_engine = orchestration_engine
        self.logger.debug("🔗 Orchestration engine reference set in LoopExecutor")

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: Dict[str, Any] = None,
        loop_config: Dict[str, Any] = None,
        transition_id: str = None,
        input_data: Dict[str, Any] = None,
        output_routing: Dict[str, Any] = None,
        input_data_configs: List[Dict[str, Any]] = None,
        node_label: str = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Execute a loop transition with dynamic configuration.

        Args:
            tool_name: Name of the loop tool
            tool_parameters: Dynamic tool parameters from transition schema
            loop_config: Pre-built loop configuration
            transition_id: ID of the loop transition
            input_data: Input data for the loop
            output_routing: Output routing configuration
            input_data_configs: Input data configuration mappings

        Returns:
            Dict containing loop execution results with dual outputs
        """
        try:
            self.current_transition_id = transition_id
            self.current_loop_id = f"loop_{transition_id}_{uuid.uuid4().hex[:8]}"
            self.original_input_data = (
                input_data  # Store original input for return_original aggregation
            )

            self.logger.info(
                f"🔄 Starting loop execution for transition {transition_id}"
            )

            # Step 1: Resolve loop configuration dynamically
            resolved_config = await self._resolve_loop_configuration(
                tool_parameters, loop_config, input_data, input_data_configs
            )

            # Step 2: Initialize loop state
            await self._initialize_loop_state(resolved_config)

            # Step 3: Prepare iteration data based on source type
            iteration_data = await self._prepare_iteration_data(resolved_config)

            # Step 4: Execute loop iterations
            results = await self._execute_loop_iterations(
                iteration_data, resolved_config, output_routing
            )

            # Step 5: Aggregate and format results
            final_result = await self._aggregate_results(results, resolved_config)

            self.logger.info(
                f"✅ Loop execution completed for transition {transition_id}"
            )
            return final_result

        except Exception as e:
            self.logger.error(
                f"❌ Loop execution failed for transition {transition_id}: {str(e)}"
            )
            await self._cleanup_loop_state()
            raise

    async def _resolve_loop_configuration(
        self,
        tool_parameters: Dict[str, Any],
        loop_config: Dict[str, Any],
        input_data: Dict[str, Any],
        input_data_configs: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Dynamically resolve loop configuration from various sources.

        This method handles the dynamic nature of loop configurations,
        resolving parameters from tool_params, input data, and pre-built configs.
        """
        from .loop_parameter_resolver import LoopParameterResolver

        resolver = LoopParameterResolver(
            workflow_utils=self.workflow_utils,
            state_manager=self.state_manager,
            logger=self.logger,
        )

        return await resolver.resolve_configuration(
            tool_parameters=tool_parameters,
            loop_config=loop_config,
            input_data=input_data,
            input_data_configs=input_data_configs,
            transition_id=self.current_transition_id,
        )

    async def _initialize_loop_state(self, config: Dict[str, Any]) -> None:
        """Initialize loop state management."""
        from .loop_state_manager import LoopStateManager

        self.loop_state_manager = LoopStateManager(
            loop_id=self.current_loop_id,
            transition_id=self.current_transition_id,
            workflow_id=self.state_manager.workflow_id,
            main_state_manager=self.state_manager,
        )

        await self.loop_state_manager.initialize_loop_state(config)

        # Get dependency map from state manager and initialize loop body configuration
        dependency_map = self.state_manager.get_dependency_map(self.transitions_by_id)
        self.loop_state_manager.initialize_loop_body_configuration(
            config, dependency_map
        )

    async def _prepare_iteration_data(self, config: Dict[str, Any]) -> List[Any]:
        """
        Prepare iteration data based on the iteration source configuration.

        Handles different source types: iteration_list, number_range, etc.
        """
        iteration_source = config.get("iteration_source", {})
        source_type = iteration_source.get("source_type")

        if source_type == "iteration_list":
            return await self._prepare_list_iterations(iteration_source)
        elif source_type == "number_range":
            return await self._prepare_range_iterations(iteration_source)
        else:
            raise ValueError(f"Unsupported iteration source type: {source_type}")

    async def _prepare_list_iterations(
        self, source_config: Dict[str, Any]
    ) -> List[Any]:
        """Prepare iterations from a list source."""
        iteration_list = source_config.get("iteration_list", [])
        batch_size = source_config.get("batch_size", 1)

        # Convert batch_size to int if it's a string (common from tool parameters)
        try:
            batch_size_int = int(batch_size) if isinstance(batch_size, str) else batch_size
            if batch_size_int <= 0:
                batch_size_int = 1  # Default to 1 if invalid
        except (ValueError, TypeError) as e:
            self.logger.error(f"Invalid batch_size value: {batch_size}. Using default batch_size=1. Error: {e}")
            batch_size_int = 1

        if batch_size_int == 1:
            return iteration_list
        else:
            # Create batches
            batches = []
            for i in range(0, len(iteration_list), batch_size_int):
                batch = iteration_list[i : i + batch_size_int]
                batches.append(batch)
            return batches

    async def _prepare_range_iterations(
        self, source_config: Dict[str, Any]
    ) -> List[Any]:
        """Prepare iterations from a number range source."""
        range_config = source_config.get("number_range", {})
        start = range_config.get("start")
        end = range_config.get("end")
        step = source_config.get("step", 1)

        # Validate that start and end are provided (not None)
        if start is None or end is None:
            self.logger.error(f"Loop range configuration incomplete: start={start}, end={end}")
            raise ValueError(f"Loop range requires both start and end values. Got start={start}, end={end}")

        # Convert string values to integers for range calculation
        try:
            start_int = int(start) if isinstance(start, str) else start
            end_int = int(end) if isinstance(end, str) else end
            step_int = int(step) if isinstance(step, str) else step

            return list(range(start_int, end_int + 1, step_int))
        except (ValueError, TypeError) as e:
            self.logger.error(f"Invalid range values: start={start}, end={end}, step={step}. Error: {e}")
            raise ValueError(f"Invalid range values: start={start}, end={end}, step={step}. Error: {e}")

    async def _execute_loop_iterations(
        self,
        iteration_data: List[Any],
        config: Dict[str, Any],
        output_routing: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Execute all loop iterations."""
        from .loop_body_executor import LoopBodyExecutor

        self.loop_body_executor = LoopBodyExecutor(
            state_manager=self.state_manager,
            transition_handler=self.transition_handler,
            transitions_by_id=self.transitions_by_id,
            loop_state_manager=self.loop_state_manager,
            logger=self.logger,
        )

        iteration_settings = config.get("iteration_settings", {})
        parallel_execution = iteration_settings.get("parallel_execution", False)

        if parallel_execution:
            return await self._execute_parallel_iterations(
                iteration_data, config, output_routing
            )
        else:
            return await self._execute_sequential_iterations(
                iteration_data, config, output_routing
            )

    async def _execute_sequential_iterations(
        self,
        iteration_data: List[Any],
        config: Dict[str, Any],
        output_routing: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Execute iterations sequentially."""
        results = []

        for index, item in enumerate(iteration_data):
            self.logger.info(
                f"🔄 Executing iteration {index + 1}/{len(iteration_data)}"
            )

            try:
                result = await self._execute_single_iteration(
                    index, item, config, output_routing
                )
                results.append(result)

                # Update loop state
                await self.loop_state_manager.update_iteration_progress(
                    index, "completed"
                )

            except Exception as e:
                error_result = await self._handle_iteration_error(
                    index, item, e, config
                )
                results.append(error_result)

        return results

    async def _execute_parallel_iterations(
        self,
        iteration_data: List[Any],
        config: Dict[str, Any],
        output_routing: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Execute iterations in parallel with concurrency control."""
        iteration_settings = config.get("iteration_settings", {})
        max_concurrent = iteration_settings.get("max_concurrent", 3)
        preserve_order = iteration_settings.get("preserve_order", True)

        semaphore = asyncio.Semaphore(max_concurrent)

        async def execute_with_semaphore(index, item):
            async with semaphore:
                return await self._execute_single_iteration(
                    index, item, config, output_routing
                )

        tasks = [
            execute_with_semaphore(index, item)
            for index, item in enumerate(iteration_data)
        ]

        if preserve_order:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            results = []
            for coro in asyncio.as_completed(tasks):
                result = await coro
                results.append(result)

        return results

    async def _execute_single_iteration(
        self,
        index: int,
        item: Any,
        config: Dict[str, Any],
        output_routing: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Execute a single loop iteration."""
        # This will be implemented in the loop body executor
        return await self.loop_body_executor.execute_iteration(
            iteration_index=index,
            iteration_item=item,
            loop_config=config,
            output_routing=output_routing,
        )

    async def _handle_iteration_error(
        self, index: int, item: Any, error: Exception, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle errors in individual iterations."""
        error_handling = config.get("error_handling", {})
        on_error = error_handling.get("on_iteration_error", "continue")
        include_errors = error_handling.get("include_errors", True)

        self.logger.error(f"❌ Error in iteration {index}: {str(error)}")

        if on_error == "retry_once":
            try:
                self.logger.info(f"🔄 Retrying iteration {index}")
                return await self._execute_single_iteration(index, item, config, {})
            except Exception as retry_error:
                self.logger.error(
                    f"❌ Retry failed for iteration {index}: {str(retry_error)}"
                )

        if include_errors:
            return {
                "iteration_index": index,
                "item": item,
                "error": str(error),
                "status": "failed",
            }
        else:
            return None

    async def _aggregate_results(
        self, results: List[Dict[str, Any]], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Aggregate loop results according to configuration."""
        from .loop_aggregator import LoopAggregator

        self.logger.info(
            f"🔍 MAIN AGGREGATOR DEBUG: Raw results from loop execution: {results}"
        )

        aggregator = LoopAggregator(logger=self.logger)
        aggregation_config = config.get("result_aggregation", {})

        # Filter out None results (failed iterations with include_errors=False)
        valid_results = [r for r in results if r is not None]

        self.logger.info(
            f"🔍 MAIN AGGREGATOR DEBUG: Valid results after filtering: {valid_results}"
        )
        self.logger.info(
            f"🔍 MAIN AGGREGATOR DEBUG: Aggregation config: {aggregation_config}"
        )

        # Prepare loop metadata
        loop_metadata = {
            "loop_id": self.current_loop_id,
            "transition_id": self.current_transition_id,
            "total_iterations": len(results),
            "execution_mode": (
                "sequential"
                if not config.get("iteration_settings", {}).get(
                    "parallel_execution", False
                )
                else "parallel"
            ),
            "exit_transitions": config.get("loop_body_configuration", {}).get(
                "exit_transitions", []
            ),
            "original_input_data": self.original_input_data,  # Store original input for return_original aggregation
        }

        # Use the aggregator to process results
        return await aggregator.aggregate_results(
            iteration_results=valid_results,
            aggregation_config=aggregation_config,
            loop_metadata=loop_metadata,
        )

    async def _cleanup_loop_state(self) -> None:
        """Clean up loop state on completion or error."""
        if self.loop_state_manager:
            await self.loop_state_manager.cleanup()

        if self.current_loop_id and self.current_transition_id:
            self.state_manager.remove_loop_state(
                self.current_loop_id, self.current_transition_id
            )
