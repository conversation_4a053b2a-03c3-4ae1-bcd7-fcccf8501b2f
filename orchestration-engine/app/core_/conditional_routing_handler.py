"""
Conditional Routing Handler for Workflow Orchestration Engine.

This module handles routing decisions from conditional components, processing
their execution results to determine next transitions in the workflow.

The handler supports both single and multiple transition routing formats
and integrates with the existing transition handler architecture.

Key Features:
- Processes conditional component execution results
- Removes unreachable end transitions (existing functionality)
- Removes unreachable waiting transitions (NEW - fixes edge case)
- Supports graph traversal to identify truly unreachable transitions
- Preserves transitions with multiple dependency paths

Edge Case Fixed:
When a conditional node executes and chooses a path, transitions that were
waiting and depend on the conditional node could incorrectly execute if they
had other completed dependencies. This module now removes such unreachable
waiting transitions to prevent incorrect execution.
"""

import logging
from typing import Any, Dict, List, Optional, Union

from app.utils.enhanced_logger import get_logger


class ConditionalRoutingHandler:
    """
    Handles routing decisions from conditional component execution results.

    This class processes the output from conditional components and extracts
    the target transitions for the orchestration engine to execute next.

    Supports both legacy single transition format and new multiple transition format.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the conditional routing handler.

        Args:
            logger: Optional logger instance. If not provided, creates a new one.
        """
        self.logger = logger or get_logger("ConditionalRoutingHandler")

    def is_conditional_component_result(self, execution_result: Any) -> bool:
        """
        Check if the execution result is from a conditional component.

        A conditional component result is identified by:
        - Being a dictionary
        - Having a 'routing_decision' field (the key identifier)

        Note: The 'status' field is optional as it may be stripped by some executors.

        Args:
            execution_result: The execution result to check

        Returns:
            True if the result is from a conditional component, False otherwise
        """
        if not isinstance(execution_result, dict):
            return False

        # The presence of 'routing_decision' is the key identifier for conditional components
        return "routing_decision" in execution_result

    async def handle_conditional_result(
        self,
        execution_result: Dict[str, Any],
        transition: Dict[str, Any],
        state_manager=None,
    ) -> List[str]:
        """
        Process conditional component result and extract next transitions.

        This method handles both single and multiple transition formats:
        - Single: {"target_transition": "transition_id"}
        - Multiple: {"target_transitions": ["transition_1", "transition_2"]}

        Also removes unreachable end transitions directly from the state manager.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed
            state_manager: Optional state manager to remove unreachable end transitions from

        Returns:
            List of next transition IDs to execute
        """
        transition_id = transition.get("id", "unknown")

        # Validate the execution result
        if not self.is_conditional_component_result(execution_result):
            self.logger.warning(
                f"Invalid conditional component result for transition {transition_id}: "
                f"missing required field (routing_decision)"
            )
            return []

        routing_decision = execution_result.get("routing_decision", {})
        if not routing_decision:
            self.logger.warning(
                f"No routing decision found in conditional result for transition {transition_id}"
            )
            return []

        # Extract target transitions - support both single and multiple formats
        target_transitions = self._extract_target_transitions(
            routing_decision, transition_id
        )

        if not target_transitions:
            self.logger.warning(
                f"No valid target transitions found in routing decision for transition {transition_id}"
            )
            return []

        # Remove unreachable end transitions directly from state manager
        if state_manager:
            # Log current end transitions before removal
            current_end_transitions = state_manager.end_transitions.copy()
            self.logger.info(
                f"🔀 Current end transitions before conditional routing: {current_end_transitions}"
            )

            unreachable_end_transitions = self._extract_unreachable_end_transitions(
                execution_result, transition, transition_id
            )

            if unreachable_end_transitions:
                self.logger.info(
                    f"🔀 Conditional routing for {transition_id} makes {len(unreachable_end_transitions)} end transitions unreachable: {unreachable_end_transitions}"
                )
                state_manager.remove_end_transitions(unreachable_end_transitions)

                # Log end transitions after removal
                remaining_end_transitions = state_manager.end_transitions.copy()
                self.logger.info(
                    f"🔀 Remaining end transitions after conditional routing: {remaining_end_transitions}"
                )
            else:
                self.logger.debug(
                    f"🔀 No unreachable end transitions found for {transition_id}"
                )

        # Log the routing decision
        self._log_routing_decision(execution_result, transition_id, target_transitions)

        return target_transitions

    def _extract_target_transitions(
        self, routing_decision: Dict[str, Any], transition_id: str
    ) -> List[str]:
        """
        Extract target transitions from routing decision.

        Supports both formats:
        - Legacy single: {"target_transition": "transition_id"}
        - New multiple: {"target_transitions": ["transition_1", "transition_2"]}

        Args:
            routing_decision: The routing decision dictionary
            transition_id: Current transition ID for logging

        Returns:
            List of target transition IDs
        """
        # Check for multiple transitions format first (new format)
        if "target_transitions" in routing_decision:
            target_transitions = routing_decision["target_transitions"]
            if isinstance(target_transitions, list):
                # Filter out None/empty values
                valid_transitions = [t for t in target_transitions if t]
                self.logger.debug(
                    f"Found multiple target transitions for {transition_id}: {valid_transitions}"
                )
                return valid_transitions
            else:
                self.logger.warning(
                    f"target_transitions is not a list for transition {transition_id}: {target_transitions}"
                )

        # Check for single transition format (legacy format)
        if "target_transition" in routing_decision:
            target_transition = routing_decision["target_transition"]
            if target_transition:
                self.logger.debug(
                    f"Found single target transition for {transition_id}: {target_transition}"
                )
                return [target_transition]
            else:
                self.logger.warning(
                    f"target_transition is empty for transition {transition_id}"
                )

        # No valid transitions found
        self.logger.warning(
            f"No target_transition or target_transitions found in routing decision for {transition_id}"
        )
        return []

    def _extract_unreachable_end_transitions(
        self,
        execution_result: Dict[str, Any],
        transition: Dict[str, Any],
        transition_id: str,
    ) -> List[str]:
        """
        Extract end transitions that become unreachable due to conditional routing decisions.

        When a conditional node executes and routes to specific paths, any end transitions
        specified in the 'ends_at' fields of non-matching conditions become unreachable
        and should be removed from the workflow's end transition tracking.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed
            transition_id: Current transition ID for logging

        Returns:
            List of end transition IDs that are now unreachable
        """
        unreachable_end_transitions = []

        # Get the original conditions from the transition configuration
        node_info = transition.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])

        # Find the conditional tool configuration
        conditional_tool = None
        for tool in tools_to_use:
            if tool.get("tool_name") == "conditional":
                conditional_tool = tool
                break

        if not conditional_tool:
            self.logger.debug(
                f"No conditional tool found in transition {transition_id}"
            )
            return []

        # Get the conditions and routing decision
        tool_parameters = conditional_tool.get("tool_parameters", {})
        conditions = tool_parameters.get("conditions", [])
        routing_decision = execution_result.get("routing_decision", {})

        # Determine which conditions were matched
        matched_conditions = self._get_matched_conditions(routing_decision)

        # Extract ends_at from non-matching conditions
        for i, condition in enumerate(conditions):
            condition_index = i + 1  # Conditions are 1-indexed

            if condition_index not in matched_conditions:
                # This condition was not matched, check for ends_at field
                ends_at = condition.get("ends_at", [])
                if ends_at:
                    if isinstance(ends_at, str):
                        ends_at = [ends_at]  # Convert single string to list

                    unreachable_end_transitions.extend(ends_at)
                    self.logger.debug(
                        f"Condition {condition_index} not matched, marking end transitions as unreachable: {ends_at}"
                    )

        # Remove duplicates and return
        return list(set(unreachable_end_transitions))

    def _get_matched_conditions(self, routing_decision: Dict[str, Any]) -> List[int]:
        """
        Extract which conditions were matched from the routing decision.

        Args:
            routing_decision: The routing decision from conditional component result

        Returns:
            List of condition indices (1-based) that were matched
        """
        matched_conditions = []

        # Check for single condition match (legacy format)
        matched_condition = routing_decision.get("matched_condition")
        if matched_condition is not None:
            if isinstance(matched_condition, int):
                matched_conditions.append(matched_condition)
            elif isinstance(matched_condition, list):
                matched_conditions.extend(matched_condition)

        # Check for multiple condition matches (new format)
        matched_conditions_list = routing_decision.get("matched_conditions", [])
        if matched_conditions_list:
            matched_conditions.extend(matched_conditions_list)

        # Remove duplicates and return
        return list(set(matched_conditions))

    def _log_routing_decision(
        self,
        execution_result: Dict[str, Any],
        transition_id: str,
        target_transitions: List[str],
    ) -> None:
        """
        Log the routing decision details for debugging and monitoring.

        Args:
            execution_result: The full execution result
            transition_id: Current transition ID
            target_transitions: List of target transition IDs
        """
        routing_decision = execution_result.get("routing_decision", {})
        metadata = execution_result.get("metadata", {})

        # Extract key information for logging
        matched_condition = routing_decision.get("matched_condition")
        condition_result = routing_decision.get("condition_result", False)
        execution_time = routing_decision.get("execution_time_ms", 0)
        total_conditions = metadata.get("total_conditions", 0)

        # Log the routing decision
        if len(target_transitions) == 1:
            self.logger.info(
                f"🔀 Conditional routing for {transition_id}: "
                f"routing to {target_transitions[0]} "
                f"(condition: {matched_condition}, result: {condition_result}, "
                f"time: {execution_time:.1f}ms, total_conditions: {total_conditions})"
            )
        else:
            self.logger.info(
                f"🔀 Conditional routing for {transition_id}: "
                f"routing to {len(target_transitions)} transitions {target_transitions} "
                f"(conditions: {matched_condition}, result: {condition_result}, "
                f"time: {execution_time:.1f}ms, total_conditions: {total_conditions})"
            )

        # Log additional metadata if available
        if "evaluation_order" in metadata:
            self.logger.debug(
                f"🔀 Evaluation order for {transition_id}: {metadata['evaluation_order']}"
            )

        if "evaluation_strategy" in metadata:
            self.logger.debug(
                f"🔀 Evaluation strategy for {transition_id}: {metadata['evaluation_strategy']}"
            )

    def _extract_all_possible_transitions(
        self, transition: Dict[str, Any]
    ) -> List[str]:
        """
        Extract all possible next transitions from conditional tool configuration.

        This method parses the conditional tool configuration to find all possible
        target transitions that could be chosen, including default transitions.
        This is used to identify which transitions become unreachable when certain
        conditions are not matched.

        Args:
            transition: The transition configuration containing conditional tool

        Returns:
            List of all possible target transition IDs
        """
        all_possible_transitions = []
        transition_id = transition.get("id", "unknown")

        # Get the conditional tool configuration
        node_info = transition.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])

        # Find the conditional tool
        conditional_tool = None
        for tool in tools_to_use:
            if tool.get("tool_name") == "conditional":
                conditional_tool = tool
                break

        if not conditional_tool:
            self.logger.debug(
                f"No conditional tool found in transition {transition_id}"
            )
            return []

        # Extract conditions and their target transitions
        tool_parameters = conditional_tool.get("tool_parameters", {})
        conditions = tool_parameters.get("conditions", [])

        for condition in conditions:
            next_transition = condition.get("next_transition")
            if next_transition and next_transition not in all_possible_transitions:
                all_possible_transitions.append(next_transition)

        # Check for default transition
        default_transition = tool_parameters.get("default_transition")
        if default_transition and default_transition not in all_possible_transitions:
            all_possible_transitions.append(default_transition)

        self.logger.debug(
            f"Extracted {len(all_possible_transitions)} possible transitions from {transition_id}: {all_possible_transitions}"
        )

        return all_possible_transitions

    def _build_forward_dependency_graph(
        self, dependency_map: Dict[str, List[str]]
    ) -> Dict[str, List[str]]:
        """
        Build a forward dependency graph from the standard dependency map.

        The standard dependency map shows what each transition depends on:
        transition_id -> [dependencies]

        This method creates a reverse mapping showing what depends on each transition:
        transition_id -> [dependents]

        This enables forward traversal from conditional nodes to find all
        transitions that could be affected by routing decisions.

        Args:
            dependency_map: Standard dependency map (transition_id -> [dependencies])

        Returns:
            Forward dependency graph (transition_id -> [dependents])
        """
        forward_graph = {}

        # Initialize empty lists for all transitions
        for transition_id in dependency_map.keys():
            forward_graph[transition_id] = []

        # Build reverse mappings
        for dependent_id, dependencies in dependency_map.items():
            for dependency_id in dependencies:
                # Ensure the dependency exists in the forward graph
                if dependency_id not in forward_graph:
                    forward_graph[dependency_id] = []

                # Add the dependent to the dependency's forward list
                if dependent_id not in forward_graph[dependency_id]:
                    forward_graph[dependency_id].append(dependent_id)

        self.logger.debug(
            f"Built forward dependency graph with {len(forward_graph)} nodes"
        )

        # Log non-empty forward dependencies for debugging
        non_empty_forwards = {k: v for k, v in forward_graph.items() if v}
        if non_empty_forwards:
            self.logger.debug(f"Non-empty forward dependencies: {non_empty_forwards}")

        return forward_graph

    def _find_reachable_transitions(
        self,
        start_transitions: List[str],
        forward_graph: Dict[str, List[str]],
        max_depth: int = 100,
    ) -> set[str]:
        """
        Find all transitions reachable from the given starting transitions.

        Uses breadth-first search (BFS) to traverse the forward dependency graph
        and find all transitions that are reachable from the starting points.
        Includes cycle detection to prevent infinite loops.

        Args:
            start_transitions: List of transition IDs to start traversal from
            forward_graph: Forward dependency graph (transition_id -> [dependents])
            max_depth: Maximum traversal depth to prevent infinite loops

        Returns:
            Set of all reachable transition IDs (including start transitions)
        """
        if not start_transitions:
            return set()

        reachable = set()
        queue = [
            (transition_id, 0) for transition_id in start_transitions
        ]  # (transition_id, depth)
        visited = set()

        while queue:
            current_transition, depth = queue.pop(0)

            # Skip if already visited or max depth exceeded
            if current_transition in visited or depth > max_depth:
                continue

            visited.add(current_transition)
            reachable.add(current_transition)

            # Add dependents to queue for further traversal
            dependents = forward_graph.get(current_transition, [])
            for dependent in dependents:
                if dependent not in visited:
                    queue.append((dependent, depth + 1))

        self.logger.debug(
            f"Found {len(reachable)} reachable transitions from {start_transitions}: {reachable}"
        )

        return reachable

    def _remove_unreachable_waiting_transitions(
        self,
        conditional_transition: Dict[str, Any],
        routing_decision: Dict[str, Any],
        state_manager,
        dependency_map: Dict[str, List[str]],
    ) -> List[str]:
        """
        Remove waiting transitions that are unreachable due to conditional routing decisions.

        This is the main orchestration method that combines all helper methods to:
        1. Extract all possible transitions from the conditional configuration
        2. Identify which transitions were chosen vs unchosen
        3. Find all transitions reachable from unchosen paths
        4. Remove unreachable transitions from waiting state (preserving multi-path transitions)

        Args:
            conditional_transition: The conditional transition configuration
            routing_decision: The routing decision from conditional execution
            state_manager: Workflow state manager to modify waiting transitions
            dependency_map: Standard dependency map for the workflow

        Returns:
            List of transition IDs that were removed from waiting state
        """
        transition_id = conditional_transition.get("id", "unknown")

        # Step 1: Extract all possible transitions from conditional config
        all_possible_transitions = self._extract_all_possible_transitions(
            conditional_transition
        )

        if not all_possible_transitions:
            self.logger.debug(
                f"No possible transitions found for conditional {transition_id}"
            )
            return []

        # Step 2: Extract chosen transitions from routing decision
        chosen_transitions = self._extract_target_transitions(
            routing_decision, transition_id
        )

        if not chosen_transitions:
            self.logger.debug(
                f"No chosen transitions found for conditional {transition_id}"
            )
            return []

        # Step 3: Identify unchosen transitions
        unchosen_transitions = [
            t for t in all_possible_transitions if t not in chosen_transitions
        ]

        if not unchosen_transitions:
            self.logger.debug(
                f"No unchosen transitions for conditional {transition_id} - all paths chosen"
            )
            return []

        self.logger.info(
            f"🔀 Conditional {transition_id}: chosen={chosen_transitions}, unchosen={unchosen_transitions}"
        )

        # Step 4: Build forward dependency graph
        forward_graph = self._build_forward_dependency_graph(dependency_map)

        # Step 5: Find all transitions reachable from unchosen paths
        unreachable_from_unchosen = self._find_reachable_transitions(
            unchosen_transitions, forward_graph
        )

        # Step 6: Find all transitions reachable from chosen paths
        reachable_from_chosen = self._find_reachable_transitions(
            chosen_transitions, forward_graph
        )

        # Step 7: Identify truly unreachable transitions (only reachable via unchosen paths)
        truly_unreachable = unreachable_from_unchosen - reachable_from_chosen

        # Step 8: Filter to only waiting transitions
        waiting_transitions_to_remove = [
            t for t in truly_unreachable if t in state_manager.waiting_transitions
        ]

        if waiting_transitions_to_remove:
            self.logger.info(
                f"🔀 Removing {len(waiting_transitions_to_remove)} unreachable waiting transitions: {waiting_transitions_to_remove}"
            )

            # Log current waiting state before removal
            self.logger.debug(
                f"🔀 Waiting transitions before removal: {state_manager.waiting_transitions}"
            )

            # Remove from waiting state
            for transition_to_remove in waiting_transitions_to_remove:
                state_manager.waiting_transitions.discard(transition_to_remove)

            # Log waiting state after removal
            self.logger.debug(
                f"🔀 Waiting transitions after removal: {state_manager.waiting_transitions}"
            )
        else:
            self.logger.debug(
                f"🔀 No unreachable waiting transitions to remove for conditional {transition_id}"
            )

        return waiting_transitions_to_remove
