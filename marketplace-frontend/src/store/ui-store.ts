import { create } from 'zustand';
import { FilterOptions, ItemType } from '@/lib/api/types';

interface UIState {
  // Search and filter
  searchQuery: string;
  filters: FilterOptions;
  activeTab: ItemType;

  // UI state
  isSidebarOpen: boolean;
  isFilterDrawerOpen: boolean;

  // Actions
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<FilterOptions>) => void;
  resetFilters: () => void;
  setActiveTab: (tab: ItemType) => void;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  toggleFilterDrawer: () => void;
  setFilterDrawerOpen: (isOpen: boolean) => void;
}

const DEFAULT_FILTERS: FilterOptions = {
  sortBy: 'rating',
  sortOrder: 'desc',
};

export const useUIStore = create<UIState>()((set) => ({
  // Initial state
  searchQuery: '',
  filters: DEFAULT_FILTERS,
  activeTab: 'MCP',
  isSidebarOpen: false,
  isFilterDrawerOpen: false,

  // Actions
  setSearchQuery: (query) => set({ searchQuery: query }),

  setFilters: (filters) => set((state) => ({
    filters: { ...state.filters, ...filters },
  })),

  resetFilters: () => set({ filters: DEFAULT_FILTERS }),

  setActiveTab: (tab) => set({ activeTab: tab }),

  toggleSidebar: () => set((state) => ({
    isSidebarOpen: !state.isSidebarOpen,
  })),

  setSidebarOpen: (isOpen) => set({ isSidebarOpen: isOpen }),

  toggleFilterDrawer: () => set((state) => ({
    isFilterDrawerOpen: !state.isFilterDrawerOpen,
  })),

  setFilterDrawerOpen: (isOpen) => set({ isFilterDrawerOpen: isOpen }),
}));
