import Link from "next/link";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function NotFound() {
  return (
    <>
      <main className="min-h-screen bg-background">
        <div className="container flex flex-col items-center justify-center px-4 py-16 md:px-6 md:py-24">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">404</h1>
          <h2 className="mt-4 text-xl font-semibold tracking-tight sm:text-2xl md:text-3xl">Page Not Found</h2>
          <p className="mt-4 text-center text-muted-foreground">
            Sorry, we couldn&apos;t find the page you&apos;re looking for.
          </p>
          <Button asChild className="mt-8">
            <Link href="/">Return Home</Link>
          </Button>
        </div>
      </main>
      <Footer />
    </>
  );
}
