/**
 * Cookie utility functions for handling authentication tokens
 */

import Cookies from 'js-cookie';

// Cookie names
export const COOKIE_NAMES = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  TOKEN_TYPE: 'token_type',
};

// Build default cookie options
const getDefaultOptions = () => {
  const options: any = {
    expires: 7, // 7 days
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax' as const,
  };

  // Only add domain if it's specified in environment
  if (process.env.NEXT_PUBLIC_COOKIE_DOMAIN) {
    options.domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;
  }

  return options;
};

// Cookie utility functions
export const cookieUtils = {
  // Set a cookie with the given name and value
  set: (name: string, value: string, options = {}) => {
    Cookies.set(name, value, { ...getDefaultOptions(), ...options });
  },

  // Get a cookie value by name
  get: (name: string) => {
    return Cookies.get(name);
  },

  // Remove a cookie by name
  remove: (name: string) => {
    // Remove with path only
    Cookies.remove(name, { path: '/' });

    // Also remove with domain if specified
    if (process.env.NEXT_PUBLIC_COOKIE_DOMAIN) {
      Cookies.remove(name, {
        path: '/',
        domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN
      });
    }
  },

  // Clear all cookies (for logout)
  clearAll: () => {
    const expiredOptions: any = {
      path: '/',
      secure: true,
      sameSite: 'none',
      expires: new Date(0), // Expire immediately
    };

    if (process.env.NEXT_PUBLIC_COOKIE_DOMAIN) {
      expiredOptions.domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;
    }

    Cookies.set(COOKIE_NAMES.ACCESS_TOKEN, '', expiredOptions);
    Cookies.set(COOKIE_NAMES.REFRESH_TOKEN, '', expiredOptions);
    Cookies.set(COOKIE_NAMES.TOKEN_TYPE, '', expiredOptions);

    // Fallback removal to cover edge cases
    cookieUtils.remove(COOKIE_NAMES.ACCESS_TOKEN);
    cookieUtils.remove(COOKIE_NAMES.REFRESH_TOKEN);
    cookieUtils.remove(COOKIE_NAMES.TOKEN_TYPE);
  },

  // Set the access token
  setAccessToken: (token: string) => {
    cookieUtils.set(COOKIE_NAMES.ACCESS_TOKEN, token);
  },

  // Get the access token
  getAccessToken: () => {
    return cookieUtils.get(COOKIE_NAMES.ACCESS_TOKEN);
  },

  // Remove the access token
  removeAccessToken: () => {
    cookieUtils.remove(COOKIE_NAMES.ACCESS_TOKEN);
  },

  // Set the refresh token
  setRefreshToken: (token: string) => {
    cookieUtils.set(COOKIE_NAMES.REFRESH_TOKEN, token);
  },

  // Get the refresh token
  getRefreshToken: () => {
    return cookieUtils.get(COOKIE_NAMES.REFRESH_TOKEN);
  },

  // Remove the refresh token
  removeRefreshToken: () => {
    cookieUtils.remove(COOKIE_NAMES.REFRESH_TOKEN);
  },

  // Set the token type
  setTokenType: (type: string) => {
    cookieUtils.set(COOKIE_NAMES.TOKEN_TYPE, type);
  },

  // Get the token type
  getTokenType: () => {
    return cookieUtils.get(COOKIE_NAMES.TOKEN_TYPE);
  },

  // Remove the token type
  removeTokenType: () => {
    cookieUtils.remove(COOKIE_NAMES.TOKEN_TYPE);
  },

  // Set all auth tokens
  setAuthTokens: (accessToken: string, refreshToken: string, tokenType: string) => {
    cookieUtils.setAccessToken(accessToken);
    cookieUtils.setRefreshToken(refreshToken);
    cookieUtils.setTokenType(tokenType);
  },

  // Remove all auth tokens
  removeAuthTokens: () => {
    cookieUtils.removeAccessToken();
    cookieUtils.removeRefreshToken();
    cookieUtils.removeTokenType();
  },

  // Clear all cookies on logout (comprehensive cleanup)
  clearAllOnLogout: () => {
    cookieUtils.clearAll();
  },
};
