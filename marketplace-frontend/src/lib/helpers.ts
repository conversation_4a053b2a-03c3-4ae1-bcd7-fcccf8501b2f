export const getAuthUrl = () => process.env.NEXT_PUBLIC_AUTH_URL || "https://ruh-auth.rapidinnovation.dev";

/**
 * Returns the URL for the RUH Developer Platform
 * @returns {string} The URL for the RUH Developer Platform
 */
export const getDeveloperPlatformUrl = () => process.env.NEXT_PUBLIC_DEVELOPER_PLATFORM_URL || "https://ruh-developer.rapidinnovation.dev/";

/**
 * Redirects the user to the RUH Developer Platform
 * Opens in a new tab by default
 */
export const redirectToDeveloperPlatform = () => {
  window.open(getDeveloperPlatformUrl(), "_blank");
};

export const getRuhUrl = () => process.env.NEXT_PUBLIC_RUH_URL || "";

export const getApiUrl = () => process.env.NEXT_PUBLIC_API_URL || "https://api.ruh.ai";

export const getRuhExecutionApiUrl = () => process.env.NEXT_PUBLIC_RUH_EXECUTION_API_URL || "https://api.ruh.ai";



// Helper function to get default image based on item type
export const getDefaultImageForType = (type: "MCP" | "AGENT" | "WORKFLOW"): string => {
  switch (type) {
    case "AGENT":
      return "/assets/agent.png";
    case "MCP":
      return "/assets/mcp.png";
    case "WORKFLOW":
      return "/assets/workflow.png";
    default:
      return "/assets/default_avatar.png";
  }
};