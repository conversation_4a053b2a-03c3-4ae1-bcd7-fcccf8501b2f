import generalAxiosClient from "@/lib/general-axios-client";
import axiosClient from "../axios-client";
import { ApiResponse } from "../types";
import {
  Workflow,
  WorkflowResponse,
  WorkflowDetailResponse,
  MarketplaceFilterParams,
  ListWorkflowVersionsResponse,
  WorkflowVersionsFilterParams,
} from "../types/marketplace-items";
import { getApiUrl } from "@/lib/helpers";

/**
 * Workflow Service
 * Handles API calls to the Workflow templates endpoints
 */
export const workflowService = {
  /**
   * Get all Workflows with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with WorkflowResponse
   */
  getWorkflows: async (
    params?: MarketplaceFilterParams
  ): Promise<ApiResponse<WorkflowResponse>> => {
    try {
      // Convert params to match the API's expected format
      const apiParams: Record<string, any> = {};

      if (params) {
        // Map the params to the API's expected format
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
        // Use page_size if available, otherwise use limit for backward compatibility
        else if (params.limit) apiParams.page_size = params.limit;

        if (params.search) apiParams.search = params.search;
        if (params.category) apiParams.category = params.category;
        if (params.tags) apiParams.tags = params.tags;

        if (params.visibility) apiParams.visibility = params.visibility;
        if (params.status) apiParams.status = params.status;
      }

      console.log("API params for /workflows endpoint:", apiParams);

      const response = await axiosClient.get<WorkflowResponse>("/workflows", {
        params: apiParams,
      });

      return {
        data: response.data,
        status: response.status,
        message: "Workflows retrieved successfully",
      };
    } catch (error: unknown) {
      console.error("Error fetching Workflows:", error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get a single Workflow by ID
   * @param id Workflow ID
   * @param userId Optional user ID to check if the item is already added
   * @returns Promise with Workflow
   */
  getWorkflowById: async (
    id: string,
    userId?: string
  ): Promise<ApiResponse<Workflow>> => {
    try {
      // Add userId to params if provided
      const params = userId ? { user_id: userId } : undefined;
      const response = await axiosClient.get<WorkflowDetailResponse>(
        `/workflows/${id}`,
        { params }
      );
      console.log(`Fetching Workflow with ID ${id}:`, response);

      // Extract the workflow from the response
      if (response.data && response.data.workflow) {
        return {
          data: response.data.workflow,
          status: response.status,
          message: response.data.message || "Workflow retrieved successfully",
        };
      } else {
        throw new Error("Invalid response format: workflow property not found");
      }
    } catch (error: unknown) {
      console.error(`Error fetching Workflow with ID ${id}:`, error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get all versions of a workflow
   * @param workflowId Workflow ID
   * @param params Optional filter parameters
   * @returns Promise with ListWorkflowVersionsResponse
   */
  getWorkflowVersions: async (
    workflowId: string,
    params?: WorkflowVersionsFilterParams
  ): Promise<ApiResponse<ListWorkflowVersionsResponse>> => {
    try {
      // Convert params to match the API's expected format
      const apiParams: Record<string, any> = {};

      if (params) {
        if (params.marketplace_listing_id)
          apiParams.marketplace_listing_id = params.marketplace_listing_id;
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
      }

      const response = await axiosClient.get<ListWorkflowVersionsResponse>(
        `/public/${workflowId}/versions`,
        { params: apiParams }
      );

      return {
        data: response.data,
        status: response.status,
        message:
          response.data.message || "Workflow versions retrieved successfully",
      };
    } catch (error: unknown) {
      console.error(
        `Error fetching versions for Workflow with ID ${workflowId}:`,
        error
      );

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  pullUpdatesFromSourceWorkflow: async (sourceWorkflowId: string) => {
    try {
      const BASE_URL = getApiUrl();
      console.log("BASE_URL: ",{BASE_URL});
      const payload = {
        source_workflow_id: sourceWorkflowId,
      };
      const response = await generalAxiosClient.post(
        `${BASE_URL}/workflows/pull-updates`,
        payload
      );
      return response.data;
    } catch (error: unknown) {
      console.error(
        `Error pulling updates for Workflow with ID ${sourceWorkflowId}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Get workflow authentication summary
   * @param id Workflow ID
   * @returns Promise with workflow auth summary
   */
  getWorkflowAuthSummary: async (
    id: string
  ): Promise<ApiResponse<{
    env_credential_status: 'not_required' | 'pending_input';
    credential_requirements?: any;
  }>> => {
    try {
      const response = await axiosClient.get(`/workflows/${id}/auth-summary`);
      return {
        data: response.data,
        status: response.status,
        message: "Workflow auth summary retrieved successfully",
      };
    } catch (error: unknown) {
      console.error("Error fetching workflow auth summary:", error);
      throw error;
    }
  },

  /**
   * Import workflow with authentication
   * @param id Workflow ID
   * @param request Import request with credential mapping
   * @returns Promise with import result
   */
  importWithAuth: async (
    id: string,
    request: {
      action: 'import';
      credential_mapping?: Record<string, { credential_id: string; credential_name: string }>;
    }
  ): Promise<ApiResponse<{
    action: string;
    workflow_id: string;
    credential_summary: any;
    user_coverage: any;
  }>> => {
    try {
      const response = await axiosClient.post(`/workflows/${id}/import-with-auth`, request);
      return {
        data: response.data,
        status: response.status,
        message: "Workflow imported successfully",
      };
    } catch (error: unknown) {
      console.error("Error importing workflow with auth:", error);
      throw error;
    }
  },
};
