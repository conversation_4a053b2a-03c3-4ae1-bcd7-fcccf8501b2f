import axiosClient from '../axios-client';
import { ApiResponse, FilterOptions, MarketplaceItem, PaginatedResponse, SearchParams } from '../types';

const MARKETPLACE_ENDPOINTS = {
  ITEMS: '/all',
  ITEM: (id: string) => `/all/${id}`,
  MCPS: '/mcps',
  AGENTS: '/agents',
  WORKFLOWS: '/workflows',
  USE: '/use',
};

export const marketplaceService = {
  /**
   * Get all marketplace items with optional filtering
   */
  getItems: async (params?: SearchParams): Promise<ApiResponse<PaginatedResponse<MarketplaceItem>>> => {
    const response = await axiosClient.get<ApiResponse<PaginatedResponse<MarketplaceItem>>>(
      MARKETPLACE_ENDPOINTS.ITEMS,
      { params }
    );
    return response.data;
  },

  /**
   * Get a single marketplace item by ID
   */
  getItemById: async (id: string): Promise<ApiResponse<MarketplaceItem>> => {
    const response = await axiosClient.get<ApiResponse<MarketplaceItem>>(
      MARKETPLACE_ENDPOINTS.ITEM(id)
    );
    return response.data;
  },

  /**
   * Get all MCPs with optional filtering
   */
  getMCPs: async (params?: FilterOptions): Promise<ApiResponse<PaginatedResponse<MarketplaceItem>>> => {
    const response = await axiosClient.get<ApiResponse<PaginatedResponse<MarketplaceItem>>>(
      MARKETPLACE_ENDPOINTS.MCPS,
      { params }
    );
    return response.data;
  },

  /**
   * Get all Agents with optional filtering
   */
  getAgents: async (params?: FilterOptions): Promise<ApiResponse<PaginatedResponse<MarketplaceItem>>> => {
    const response = await axiosClient.get<ApiResponse<PaginatedResponse<MarketplaceItem>>>(
      MARKETPLACE_ENDPOINTS.AGENTS,
      { params }
    );
    return response.data;
  },

  /**
   * Get all Workflows with optional filtering
   */
  getWorkflows: async (params?: FilterOptions): Promise<ApiResponse<PaginatedResponse<MarketplaceItem>>> => {
    const response = await axiosClient.get<ApiResponse<PaginatedResponse<MarketplaceItem>>>(
      MARKETPLACE_ENDPOINTS.WORKFLOWS,
      { params }
    );
    return response.data;
  },

  /**
   * Mark an item as used/added to RUH
   * @param itemId The ID of the item to mark as used
   * @param itemType The type of item (AGENT, WORKFLOW, MCP)
   * @returns Promise with the response
   */
  useItem: async (itemId: string, itemType: 'AGENT' | 'WORKFLOW' | 'MCP'): Promise<ApiResponse<any>> => {
    const response = await axiosClient.post<ApiResponse<any>>(
      MARKETPLACE_ENDPOINTS.USE,
      {
        item_id: itemId,
        item_type: itemType
      }
    );
    return response.data;
  },
};