import axiosClient from '../axios-client';
import { ApiResponse } from '../types';
import { Agent, MarketplaceFilterParams, MCP, Workflow } from '../types/marketplace-items';

/**
 * Combined Marketplace Response metadata type
 */
export interface CombinedMarketplaceResponseMetadata {
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
  next_page: number | null;
  prev_page: number | null;
}

/**
 * Combined Marketplace Response type
 */
export interface CombinedMarketplaceResponse {
  data: (MCP | Agent | Workflow)[];
  metadata: CombinedMarketplaceResponseMetadata;
}

/**
 * Legacy Combined Marketplace Response type (for backward compatibility)
 */
export interface LegacyCombinedMarketplaceResponse {
  templates: (MCP | Agent | Workflow)[];
  total: number;
  page: number;
  total_pages: number;
}

const MARKETPLACE_ENDPOINTS = {
  ALL: '/all',
  MCPS: '/mcps',
  AGENTS: '/agents',
  WORKFLOWS: '/workflows',
};

/**
 * Combined Marketplace Service
 * Handles API calls to fetch combined marketplace items (MCPs, Agents, and Workflows)
 */
export const combinedMarketplaceService = {
  /**
   * Get all marketplace items (MCPs, Agents, and Workflows) combined
   * @param params Optional filter parameters
   * @returns Promise with CombinedMarketplaceResponse
   */
  getCombinedItems: async (params?: MarketplaceFilterParams): Promise<ApiResponse<CombinedMarketplaceResponse>> => {
    try {
      const apiParams: Record<string, any> = {};

      if (params) {
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
        else if (params.limit) apiParams.page_size = params.limit;
        if (params.search) apiParams.search = params.search;
        if (params.category) apiParams.category = params.category;
        if (params.category_type) apiParams.category_type = params.category_type;
        if (params.tags) apiParams.tags = params.tags;
        if (params.visibility) apiParams.visibility = params.visibility;
        if (params.status) apiParams.status = params.status;
      }

      console.log('API params for /all endpoint:', apiParams);

      const response = await axiosClient.get<CombinedMarketplaceResponse>(MARKETPLACE_ENDPOINTS.ALL, { params: apiParams });

      console.log('Raw /all endpoint response:', response.data);

      return {
        data: response.data,
        status: response.status,
        message: 'Combined marketplace items retrieved successfully',
      };
    } catch (error: unknown) {
      console.error('Error fetching combined marketplace items:', error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get a single marketplace item by ID and type
   * @param id Item ID
   * @param type Item type (MCP, AGENT, or WORKFLOW)
   * @returns Promise with the item
   */
  getCombinedItemById: async (id: string, type: 'MCP' | 'AGENT' | 'WORKFLOW'): Promise<ApiResponse<MCP | Agent | Workflow>> => {
    try {
      let endpoint = '';

      // Determine the endpoint based on the item type
      switch (type) {
        case 'MCP':
          endpoint = `${MARKETPLACE_ENDPOINTS.MCPS}/${id}`;
          break;
        case 'AGENT':
          endpoint = `${MARKETPLACE_ENDPOINTS.AGENTS}/${id}`;
          break;
        case 'WORKFLOW':
          endpoint = `${MARKETPLACE_ENDPOINTS.WORKFLOWS}/${id}`;
          break;
        default:
          throw new Error(`Invalid item type: ${type}`);
      }

      const response = await axiosClient.get(endpoint);

      // Log the raw response for debugging
      console.log(`Raw ${type} detail endpoint response:`, response.data);

      // Handle different response structures
      let itemData: MCP | Agent | Workflow;

      if (response.data.template) {
        // Old response structure with template property
        itemData = response.data.template;
      } else if (response.data.data) {
        // New response structure with data property
        itemData = response.data.data;
      } else if (response.data.workflow && type === 'WORKFLOW') {
        // Workflow detail response structure
        itemData = response.data.workflow;
      } else if (response.data.agent && type === 'AGENT') {
        // Agent detail response structure
        itemData = response.data.agent;
      } else if (response.data.mcp && type === 'MCP') {
        // MCP detail response structure
        itemData = response.data.mcp;
      } else {
        // Assume the response is the item itself
        itemData = response.data;
      }

      // Log the extracted item data
      console.log(`Extracted ${type} item data:`, itemData);

      return {
        data: itemData,
        status: response.status,
        message: 'Item retrieved successfully',
      };
    } catch (error: unknown) {
      console.error(`Error fetching item with ID ${id}:`, error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },
};
