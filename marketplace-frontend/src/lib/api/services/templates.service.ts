import axiosClient from '../axios-client';
import {
  MCPTemplateResponse,
  AgentTemplateResponse,
  WorkflowTemplateResponse,
  TemplateFilterParams
} from '../types/templates';
import { ApiResponse } from '../types';

// API endpoints
const TEMPLATE_ENDPOINTS = {
  MCP_TEMPLATES: '/mcp-templates',
  AGENT_TEMPLATES: '/agent-templates',
  WORKFLOW_TEMPLATES: '/workflow-templates',
  MCP_TEMPLATE: (id: string) => `/mcp-templates/${id}`,
  AGENT_TEMPLATE: (id: string) => `/agent-templates/${id}`,
  WORKFLOW_TEMPLATE: (id: string) => `/workflow-templates/${id}`,
};

/**
 * Templates Service
 * Handles API calls to the template endpoints
 */
export const templatesService = {
  /**
   * Get MCP templates with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with MCPTemplateResponse
   */
  getMCPTemplates: async (params?: TemplateFilterParams): Promise<ApiResponse<MCPTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<MCPTemplateResponse>>(
        TEMPLATE_ENDPOINTS.MCP_TEMPLATES,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching MCP templates:', error);
      throw error;
    }
  },

  /**
   * Get a single MCP template by ID
   * @param id MCP template ID
   * @returns Promise with MCPTemplateResponse
   */
  getMCPTemplateById: async (id: string): Promise<ApiResponse<MCPTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<MCPTemplateResponse>>(
        TEMPLATE_ENDPOINTS.MCP_TEMPLATE(id)
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Error fetching MCP template with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get Agent templates with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with AgentTemplateResponse
   */
  getAgentTemplates: async (params?: TemplateFilterParams): Promise<ApiResponse<AgentTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<AgentTemplateResponse>>(
        TEMPLATE_ENDPOINTS.AGENT_TEMPLATES,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching Agent templates:', error);
      throw error;
    }
  },

  /**
   * Get a single Agent template by ID
   * @param id Agent template ID
   * @returns Promise with AgentTemplateResponse
   */
  getAgentTemplateById: async (id: string): Promise<ApiResponse<AgentTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<AgentTemplateResponse>>(
        TEMPLATE_ENDPOINTS.AGENT_TEMPLATE(id)
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Error fetching Agent template with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get Workflow templates with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with WorkflowTemplateResponse
   */
  getWorkflowTemplates: async (params?: TemplateFilterParams): Promise<ApiResponse<WorkflowTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<WorkflowTemplateResponse>>(
        TEMPLATE_ENDPOINTS.WORKFLOW_TEMPLATES,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching Workflow templates:', error);
      throw error;
    }
  },

  /**
   * Get a single Workflow template by ID
   * @param id Workflow template ID
   * @returns Promise with WorkflowTemplateResponse
   */
  getWorkflowTemplateById: async (id: string): Promise<ApiResponse<WorkflowTemplateResponse>> => {
    try {
      const response = await axiosClient.get<ApiResponse<WorkflowTemplateResponse>>(
        TEMPLATE_ENDPOINTS.WORKFLOW_TEMPLATE(id)
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Error fetching Workflow template with ID ${id}:`, error);
      throw error;
    }
  },
};
