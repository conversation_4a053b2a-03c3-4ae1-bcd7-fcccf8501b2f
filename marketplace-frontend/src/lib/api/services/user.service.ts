import axiosClient from "../axios-client";
import { ApiResponse } from "../types";

export interface UserCredential {
  id: string;
  name: string;
  description?: string;
  type: string;
  created_at: string;
  updated_at: string;
}

/**
 * User Service
 * Handles API calls to the User service endpoints
 */
export const userService = {
  /**
   * Get user credentials
   * @returns Promise with user credentials
   */
  getUserCredentials: async (): Promise<ApiResponse<UserCredential[]>> => {
    try {
      const response = await axiosClient.get("/user/credentials");
      return {
        data: response.data.credentials || [],
        status: response.status,
        message: "User credentials retrieved successfully",
      };
    } catch (error: unknown) {
      console.error("Error fetching user credentials:", error);
      throw error;
    }
  },

  /**
   * Create user credential
   * @param credential Credential data
   * @returns Promise with created credential
   */
  createUserCredential: async (credential: {
    name: string;
    type: string;
    value: string;
    description?: string;
  }): Promise<ApiResponse<UserCredential>> => {
    try {
      const response = await axiosClient.post("/user/credentials", credential);
      return {
        data: response.data,
        status: response.status,
        message: "User credential created successfully",
      };
    } catch (error: unknown) {
      console.error("Error creating user credential:", error);
      throw error;
    }
  },

  /**
   * Update user credential
   * @param id Credential ID
   * @param credential Updated credential data
   * @returns Promise with updated credential
   */
  updateUserCredential: async (
    id: string,
    credential: {
      name?: string;
      description?: string;
    }
  ): Promise<ApiResponse<UserCredential>> => {
    try {
      const response = await axiosClient.put(`/user/credentials/${id}`, credential);
      return {
        data: response.data,
        status: response.status,
        message: "User credential updated successfully",
      };
    } catch (error: unknown) {
      console.error("Error updating user credential:", error);
      throw error;
    }
  },

  /**
   * Delete user credential
   * @param id Credential ID
   * @returns Promise with deletion result
   */
  deleteUserCredential: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await axiosClient.delete(`/user/credentials/${id}`);
      return {
        data: undefined,
        status: response.status,
        message: "User credential deleted successfully",
      };
    } catch (error: unknown) {
      console.error("Error deleting user credential:", error);
      throw error;
    }
  },
};
