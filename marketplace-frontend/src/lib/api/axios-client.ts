import axios from 'axios';
import { cookieUtils } from './cookie';

// Define base API URL from environment variable or use a default
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://e91e-106-215-176-118.ngrok-free.app/api/v1';
const API_BASE_URL = BASE_URL + '/marketplace';

// Log the API URL for debugging
console.log('API Base URL:', API_BASE_URL);
console.log('Environment variable:', process.env.NEXT_PUBLIC_API_URL);

// Create an Axios instance with default config
const axiosClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 50000, // 10 seconds
});

// Request interceptor for adding auth token
axiosClient.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const accessToken = cookieUtils.getAccessToken();
      const tokenType = 'Bearer';

      if (accessToken && config.headers) {
        config.headers.Authorization = `${tokenType} ${accessToken}`;
      }
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
axiosClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    // Handle common errors (e.g., 401 unauthorized, etc.)
    if (error.response?.status === 401) {
      // Handle unauthorized error (e.g., redirect to login)
      console.error('Unauthorized access');
      // Optional: redirect to login page
      // window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

export default axiosClient;
