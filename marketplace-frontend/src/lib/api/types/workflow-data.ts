/**
 * Types for workflow data structure
 */

/**
 * Tool in a workflow node
 */
export interface WorkflowNodeTool {
  tool_id: number;
  tool_name: string;
  input_schema?: Record<string, any>;
  output_schema?: Record<string, any>;
}

/**
 * Node in a workflow
 */
export interface WorkflowNode {
  id: string;
  server_script_path?: string;
  server_tools?: WorkflowNodeTool[];
  // Add other node properties as needed
}

/**
 * Transition between nodes in a workflow
 */
export interface WorkflowTransition {
  source: string;
  target: string;
  // Add other transition properties as needed
}

/**
 * Complete workflow data structure
 */
export interface WorkflowData {
  nodes: WorkflowNode[];
  transitions: WorkflowTransition[];
}
