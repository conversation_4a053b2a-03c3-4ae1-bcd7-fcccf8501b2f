'use client';

import { useQuery, useMutation } from '@tanstack/react-query';
import { api } from '@/lib/api';

export interface WorkflowAuthRequirement {
  type: 'env_key' | 'oauth';
  key?: string; // For env_key type
  provider?: string; // For oauth type
  tool_name?: string; // For oauth type
  description: string;
  component_count: number;
}

export interface WorkflowAuthSummary {
  requires_authentication: boolean;
  env_credential_status: 'not_required' | 'pending_input';
  credential_requirements?: {
    credentials: WorkflowAuthRequirement[];
    last_analyzed: string;
  };
}

export interface CredentialMapping {
  [fieldName: string]: {
    credential_id: string;
    credential_name: string;
  };
}

/**
 * Hook to analyze workflow authentication requirements
 */
export function useWorkflowAuth(workflowId: string) {
  return useQuery({
    queryKey: ['workflow-auth', workflowId],
    queryFn: async (): Promise<WorkflowAuthSummary> => {
      const response = await api.workflow.getWorkflowAuthSummary(workflowId);
      return {
        requires_authentication: response.data.env_credential_status === 'pending_input',
        env_credential_status: response.data.env_credential_status,
        credential_requirements: response.data.credential_requirements
      };
    },
    enabled: !!workflowId,
  });
}

/**
 * Hook to import workflow with authentication
 */
export function useWorkflowImportWithAuth() {
  return useMutation({
    mutationFn: async ({ 
      workflowId, 
      credentialMapping 
    }: { 
      workflowId: string; 
      credentialMapping?: CredentialMapping;
    }) => {
      const response = await api.workflow.importWithAuth(workflowId, {
        action: 'import',
        credential_mapping: credentialMapping
      });
      return response.data;
    },
  });
}
