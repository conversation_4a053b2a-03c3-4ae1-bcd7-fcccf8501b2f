'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { cookieUtils } from '@/lib/cookie';
import { userService } from '@/services/user-service';
import { useAuthStore } from '@/store';
import { getAuthUrl } from '@/lib/helpers';

export function useAuth() {
  const queryClient = useQueryClient();
  const { setUser, logout: storeLogout } = useAuthStore();

  // Check if access token exists
  const hasAccessToken = typeof window !== 'undefined' ? !!cookieUtils.getAccessToken() : false;

  // Get current user query
  const { data: currentUser, isLoading: isLoadingUser } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      if (!hasAccessToken) return null;
      try {
        const userData = await userService.getCurrentUser();
        // Update the store with the user data
        setUser(userData);
        return userData;
      } catch (error) {
        console.error('Error fetching user data:', error);
        // If there's an error fetching the user, log them out
        storeLogout();
        return null;
      }
    },
    enabled: hasAccessToken, // Only run if we have a token
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });

  // Handle login redirect
  const handleLoginRedirect = () => {
    const authUrl = getAuthUrl();
    // Add current path as redirect parameter
    const redirectUrl = `${authUrl}/?redirect_url=${encodeURIComponent(window.location.href)}`;
    window.location.href = redirectUrl;
  };

  // Determine if user is authenticated
  const isAuthenticated = hasAccessToken && !!currentUser;

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      // No need to call API for logout, just clear cookies
      return Promise.resolve();
    },
    onSettled: () => {
      // Clear cookies
      cookieUtils.clearAllOnLogout();
      // Update store
      storeLogout();
      queryClient.clear();
      // Refresh the page
      // window.location.reload();
    },
  });

  return {
    user: currentUser,
    isAuthenticated,
    isLoading: isLoadingUser || logoutMutation.isPending,
    logout: logoutMutation.mutate,
    handleLoginRedirect,
  };
}
