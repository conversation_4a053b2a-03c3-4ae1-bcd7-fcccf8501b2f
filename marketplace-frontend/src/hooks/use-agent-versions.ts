'use client';

import { api, ListAgentVersionsResponse } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';

interface UseAgentVersionsParams {
  agentId: string;
  page?: number;
  pageSize?: number;
  enabled?: boolean;
}

/**
 * Hook to fetch versions for a specific agent
 * @param params Parameters for fetching agent versions
 * @returns Query result with agent versions data
 */
export function useAgentVersions({
  agentId,
  page = 1,
  pageSize = 10,
  enabled = true
}: UseAgentVersionsParams) {
  return useQuery<ListAgentVersionsResponse, Error>({
    queryKey: ['agent-versions', agentId, page, pageSize],
    queryFn: async () => {
      if (!agentId) {
        throw new Error('No agent ID provided');
      }

      const response = await api.agent.getAgentVersions(agentId, {
        page,
        page_size: pageSize
      });

      return response.data;
    },
    enabled: enabled && !!agentId,
  });
}