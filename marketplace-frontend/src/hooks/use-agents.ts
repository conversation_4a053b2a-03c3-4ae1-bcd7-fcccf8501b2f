'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { MarketplaceFilterParams } from '@/lib/api/types/marketplace-items';

/**
 * Hook to fetch all Agents with optional filtering
 */
export function useAgents(params?: MarketplaceFilterParams) {
  return useQuery({
    queryKey: ['agents', params],
    queryFn: async () => {
      const response = await api.agent.getAgents(params);
      return response.data;
    },
  });
}

/**
 * Hook to fetch a single Agent by ID
 * @param id Agent ID
 * @param userId Optional user ID to check if the item is already added
 */
export function useAgent(id: string, userId?: string) {
  return useQuery({
    queryKey: ['agent', id, userId], // Include userId in the query key to refetch when it changes
    queryFn: async () => {
      const response = await api.agent.getAgentById(id, userId);
      return response.data;
    },
    enabled: !!id, // Only run the query if an ID is provided
  });
}
