'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';

export interface UserCredential {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Hook to fetch user's credentials for credential selection
 */
export function useUserCredentials() {
  return useQuery({
    queryKey: ['user-credentials'],
    queryFn: async (): Promise<UserCredential[]> => {
      // This would call the user service to get credentials
      // For now, return empty array - implement when user credential API is available
      try {
        const response = await api.user.getUserCredentials();
        return response.data || [];
      } catch (error) {
        console.warn('User credentials API not available yet:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
