'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { FilterOptions, ItemType, SearchParams } from '@/lib/api/types';
import { useUIStore } from '@/store';

export function useMarketplaceItems(params?: SearchParams) {
  const { searchQuery, filters, activeTab } = useUIStore();

  // Combine the store filters with any passed in params
  const queryParams: SearchParams = {
    ...filters,
    ...params,
    query: searchQuery || params?.query,
    type: activeTab as ItemType,
  };

  return useQuery({
    queryKey: ['marketplace', 'items', queryParams],
    queryFn: async () => {
      const response = await api.marketplace.getItems(queryParams);
      return response.data;
    },
  });
}

export function useMarketplaceItem(id: string) {
  return useQuery({
    queryKey: ['marketplace', 'item', id],
    queryFn: async () => {
      const response = await api.marketplace.getItemById(id);
      return response.data;
    },
    enabled: !!id,
  });
}

export function useMCPs(filters?: FilterOptions) {
  return useQuery({
    queryKey: ['marketplace', 'mcps', filters],
    queryFn: async () => {
      const response = await api.marketplace.getMCPs(filters);
      return response.data;
    },
  });
}

export function useAgents(filters?: FilterOptions) {
  return useQuery({
    queryKey: ['marketplace', 'agents', filters],
    queryFn: async () => {
      const response = await api.marketplace.getAgents(filters);
      return response.data;
    },
  });
}

export function useWorkflows(filters?: FilterOptions) {
  return useQuery({
    queryKey: ['marketplace', 'workflows', filters],
    queryFn: async () => {
      const response = await api.marketplace.getWorkflows(filters);
      return response.data;
    },
  });
}
