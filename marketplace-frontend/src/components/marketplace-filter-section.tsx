"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface MarketplaceFilterSectionProps {
  currentTab: string;
  currentCategory: string;
  searchQuery: string;
  onTabChange: (value: string) => void;
  onCategoryChange: (value: string) => void;
  onClearFilters: () => void;
}

export function MarketplaceFilterSection({
  currentTab,
  currentCategory,
  searchQuery,
  onTabChange,
  onCategoryChange,
  onClearFilters
}: MarketplaceFilterSectionProps) {
  // Categories for the select dropdown
  const categories = [
    { value: "all", label: "All" },
    { value: "productivity-workflow", label: "Productivity" },
    { value: "data-science-ml", label: "Data Science" },
    { value: "analytics-monitoring", label: "Analytics" },
    { value: "content-management", label: "Content" },
    { value: "collaboration-tools", label: "Collaboration" },
    { value: "developer-tools", label: "Developer" }
  ];

  return (
    <div className="flex flex-wrap items-center justify-center gap-4 py-2">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Type:</span>
        <div className="bg-background rounded-lg">
          <div className="flex border rounded-lg overflow-hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onTabChange("agents")}
              className={`rounded-none border-0 h-full px-4 ${currentTab === "agents" ? "bg-purple-600 text-white hover:bg-purple-700" : "hover:bg-muted/50"}`}
            >
              Agents
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onTabChange("mcps")}
              className={`rounded-none border-0 h-full px-4 ${currentTab === "mcps" ? "bg-purple-600 text-white hover:bg-purple-700" : "hover:bg-muted/50"}`}
            >
              MCPs
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onTabChange("workflows")}
              className={`rounded-none border-0 h-full px-4 ${currentTab === "workflows" ? "bg-purple-600 text-white hover:bg-purple-700" : "hover:bg-muted/50"}`}
            >
              Workflows
            </Button>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Category:</span>
        <Select value={currentCategory} onValueChange={onCategoryChange}>
          <SelectTrigger className="w-[180px] border rounded-lg h-9">
            <SelectValue placeholder="All" />
            <span className="absolute right-3 top-1/2 -translate-y-1/2">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 8.5L2 4.5H10L6 8.5Z" fill="currentColor"/>
              </svg>
            </span>
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {(searchQuery || currentCategory !== "all") && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearFilters}
          className="text-muted-foreground hover:text-foreground"
        >
          Clear all filters
        </Button>
      )}
    </div>
  );
}
