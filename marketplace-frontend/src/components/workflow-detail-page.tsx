"use client";

import { BaseDetailPage } from "@/components/base-detail-page";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MarketplaceItem } from "@/data/marketplace-items";
import { redirectToDeveloperPlatform } from "@/lib/helpers";
import { useWorkflowData } from "@/hooks/use-workflow-data";
import { workflowService } from "@/lib/api/services/workflow.service";
import { WorkflowVersionInDB } from "@/lib/api/types/marketplace-items";
import {
  Workflow,
  Wrench,
  Loader2,
  Tag,
  Calendar,
  RefreshCw,
} from "lucide-react";
import { useState, useEffect } from "react";
import { format } from "date-fns";

interface WorkflowDetailPageProps {
  item: MarketplaceItem;
  relatedItems?: MarketplaceItem[];
  refetch?: () => void;
}

export function WorkflowDetailPage({
  item,
  relatedItems = [],
  refetch,
}: WorkflowDetailPageProps) {
  // Extract workflow URL from the item's raw data
  const workflowUrl = item.rawData?.workflow_url;

  // Log the workflow URL for debugging
  console.log("Workflow URL:", workflowUrl);
  console.log("Raw data:", item.rawData);

  // Fetch workflow data using our custom hook
  const { data: workflowData, isLoading, error } = useWorkflowData(workflowUrl);

  // State for versions tab
  const [versions, setVersions] = useState<WorkflowVersionInDB[]>([]);
  const [versionsLoading, setVersionsLoading] = useState(false);
  const [versionsError, setVersionsError] = useState<string | null>(null);
  const [versionsPage, setVersionsPage] = useState(1);
  const [versionsTotalPages, setVersionsTotalPages] = useState(1);

  // Extract all tools from all nodes without filtering
  const allTools =
    workflowData?.nodes?.flatMap((node) => {
      // Log each node's server_tools for debugging
      console.log(`Node ${node.id} server_tools:`, node.server_tools);

      // Return all tools from the node, even empty objects
      return node.server_tools || [];
    }) || [];

  // Log the extracted tools for debugging
  console.log("All extracted tools:", allTools);

  // Function to fetch workflow versions
  const fetchVersions = async (pageNum: number) => {
    if (!item.rawData?.source_workflow_id) return;

    setVersionsLoading(true);
    setVersionsError(null);
    try {
      const response = await workflowService.getWorkflowVersions(
        item.rawData.source_workflow_id,
        {
          page: pageNum,
          page_size: 10,
        }
      );
      setVersions(response.data.versions);
      setVersionsTotalPages(response.data.total_pages);
    } catch (err) {
      setVersionsError("Failed to load workflow versions");
      console.error("Error fetching workflow versions:", err);
    } finally {
      setVersionsLoading(false);
    }
  };

  // Load versions when component mounts
  useEffect(() => {
    fetchVersions(1);
  }, [item.rawData?.source_workflow_id]);

  // Don't filter out duplicates - show all tools

  return (
    <BaseDetailPage item={item} relatedItems={relatedItems} refetch={refetch}>
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full mb-4 justify-start gap-2 dark:border dark:border-border/30 dark:bg-card/80">
          <TabsTrigger
            value="overview"
            className="w-20 dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-white"
          >
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="components"
            className="dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-white"
          >
            Tools
          </TabsTrigger>
          <TabsTrigger
            value="versions"
            className="dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-white"
          >
            Versions
          </TabsTrigger>
          {/* <TabsTrigger
            value="integration"
            className="dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-white"
          >
            Integration
          </TabsTrigger> */}
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-muted-foreground mb-4">{item.description}</p>
          </div>

          {/* Workflow-specific overview content */}
          {item.rawData ? (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">
                Workflow Information
              </h2>
              <div className="grid gap-4 md:grid-cols-2">
                {item.rawData.version ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Workflow className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">Version</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.rawData.version}
                      </p>
                    </CardContent>
                  </Card>
                ) : null}

                {item.rawData.status ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Workflow className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">Status</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.rawData.status}
                      </p>
                    </CardContent>
                  </Card>
                ) : null}
              </div>
            </div>
          ) : null}

          {/* Usage statistics if available */}
          {item.rawData && item.rawData.execution_count ? (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Usage Statistics</h2>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Workflow className="h-5 w-5 text-muted-foreground" />
                    <h3 className="font-medium">Executions</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    This workflow has been executed{" "}
                    {item.rawData.execution_count.toLocaleString()} times.
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : null}
        </TabsContent>

        {/* Nodes Tab Content */}
        <TabsContent value="components">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Tools</h2>
            <p className="text-muted-foreground mb-6">
              This workflow uses the following tools to process and analyze
              data.
            </p>

            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">
                  Loading workflow data...
                </span>
              </div>
            ) : error ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
                <p className="text-red-700 dark:text-red-400">
                  Error loading workflow data. Please try again later.
                </p>
              </div>
            ) : !workflowData ? (
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4 mb-6">
                <p className="text-amber-700 dark:text-amber-400">
                  No workflow data available.
                </p>
              </div>
            ) : (
              <>
                {/* Display tools */}
                {allTools.length > 0 ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {allTools.map((tool, index) => (
                        <Card
                          key={index}
                          className="overflow-hidden hover:shadow-md transition-shadow"
                        >
                          <CardContent className="pt-6">
                            <div className="flex items-start gap-3">
                              <Wrench className="h-5 w-5 text-primary mt-0.5" />
                              <div>
                                {/* Show tool name if available, otherwise show Tool ID */}
                                <h4 className="font-medium text-base">
                                  {tool && tool.tool_name
                                    ? tool.tool_name
                                    : tool && tool.tool_id
                                    ? `Tool ${tool.tool_id}`
                                    : `Tool ${index + 1}`}
                                </h4>
                                {tool && typeof tool.tool_id === "number" && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    Tool ID: {tool.tool_id}
                                  </p>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4 mb-6">
                    <p className="text-amber-700 dark:text-amber-400">
                      No tools found in this workflow.
                    </p>
                  </div>
                )}
              </>
            )}

            {/* Workflow Steps if available */}
            {item.rawData &&
            item.rawData.workflow_steps &&
            typeof item.rawData.workflow_steps === "string" ? (
              <div className="mt-8 pt-6 border-t">
                <h2 className="text-xl font-semibold mb-4">Workflow Steps</h2>
                <div className="bg-muted dark:bg-gray-800 p-4 rounded-md border dark:border-gray-700">
                  <pre className="text-sm overflow-x-auto whitespace-pre-wrap text-foreground dark:text-gray-200">
                    {item.rawData.workflow_steps}
                  </pre>
                </div>
              </div>
            ) : null}

            {/* Show workflow URL for debugging */}
            {workflowUrl ? (
              <div className="mt-8 pt-6 border-t">
                <h2 className="text-xl font-semibold mb-4">Workflow URL</h2>
                <div className="bg-muted dark:bg-gray-800 p-4 rounded-md border dark:border-gray-700">
                  <p className="text-sm text-muted-foreground break-all">
                    {workflowUrl}
                  </p>
                </div>
              </div>
            ) : null}
          </div>
        </TabsContent>

        {/* Versions Tab Content */}
        <TabsContent value="versions">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Version History</h2>
            <p className="text-muted-foreground mb-6">
              View all versions of this workflow and their changes over time.
            </p>

            {versionsLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">
                  Loading versions...
                </span>
              </div>
            ) : versionsError ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
                <div className="flex items-center justify-between">
                  <p className="text-red-700 dark:text-red-400">
                    {versionsError}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchVersions(versionsPage)}
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Retry
                  </Button>
                </div>
              </div>
            ) : !item.rawData?.source_workflow_id ? (
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4 mb-6">
                <p className="text-amber-700 dark:text-amber-400">
                  No workflow ID available for version history.
                </p>
              </div>
            ) : (
              <>
                {/* Versions Table */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  {/* Table Header */}
                  <div className="bg-muted dark:bg-gray-800 px-6 py-3 border-b border-border dark:border-gray-700">
                    <div className="grid grid-cols-12 gap-4 font-medium text-sm">
                      <div className="col-span-3">Version Number</div>
                      <div className="col-span-6">Changelog</div>
                      <div className="col-span-3">Date</div>
                    </div>
                  </div>

                  {/* Table Body */}
                  <div className="divide-y divide-border dark:divide-gray-700">
                    {versions.length > 0 ? (
                      versions.map((version) => (
                        <div
                          key={version.id}
                          className={`px-6 py-4 hover:bg-muted/50 transition-colors ${
                            version.is_current
                              ? "bg-primary/5 dark:bg-primary/10 border-l-4 border-l-primary"
                              : ""
                          }`}
                        >
                          <div className="grid grid-cols-12 gap-4 items-start">
                            {/* Version Number */}
                            <div className="col-span-3">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">
                                  {version.version_number}
                                </span>
                                {version.is_current && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    Current
                                  </Badge>
                                )}
                              </div>
                            </div>

                            {/* Changelog */}
                            <div className="col-span-6">
                              {version.changelog ? (
                                <p className="text-sm text-muted-foreground">
                                  {version.changelog}
                                </p>
                              ) : (
                                <span className="text-sm text-muted-foreground italic">
                                  No changelog provided
                                </span>
                              )}

                              {/* Tags if available */}
                              {version.tags && version.tags.length > 0 && (
                                <div className="flex items-center gap-1 mt-2">
                                  <Tag className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-xs text-muted-foreground">
                                    {version.tags.join(", ")}
                                  </span>
                                </div>
                              )}
                            </div>

                            {/* Date */}
                            <div className="col-span-3">
                              {version.created_at ? (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Calendar className="h-4 w-4" />
                                  <span>
                                    {format(
                                      new Date(version.created_at),
                                      "MMM d, yyyy"
                                    )}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm text-muted-foreground italic">
                                  No date available
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="px-6 py-8 text-center">
                        <p className="text-muted-foreground">
                          No versions found for this workflow.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Pagination */}
                {versionsTotalPages > 1 && (
                  <div className="flex justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newPage = Math.max(1, versionsPage - 1);
                        setVersionsPage(newPage);
                        fetchVersions(newPage);
                      }}
                      disabled={versionsPage === 1 || versionsLoading}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center px-4 text-sm text-muted-foreground">
                      Page {versionsPage} of {versionsTotalPages}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newPage = Math.min(
                          versionsTotalPages,
                          versionsPage + 1
                        );
                        setVersionsPage(newPage);
                        fetchVersions(newPage);
                      }}
                      disabled={
                        versionsPage === versionsTotalPages || versionsLoading
                      }
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </TabsContent>

        {/* Integration Tab Content */}
        <TabsContent value="integration">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">API Integration</h2>
            <p className="text-muted-foreground mb-4">
              Integrate this workflow into your applications.
            </p>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Get your API Key</h3>
              <p className="text-muted-foreground mb-3">
                You&apos;ll need to sign in to the RUH Developer Portal and
                generate an API key to use this workflow.
              </p>
              <Button
                variant="secondary"
                className="bg-gray-800 hover:bg-gray-700 text-white dark:bg-primary/90 dark:hover:bg-primary dark:text-white dark:border-primary/30"
                onClick={redirectToDeveloperPlatform}
              >
                Generate API Key
              </Button>
            </div>

            <div className="mt-8">
              <h3 className="text-lg font-medium mb-3">Code Example</h3>
              <div className="bg-muted dark:bg-gray-800 p-4 rounded-md border dark:border-gray-700">
                <pre className="text-sm overflow-x-auto">
                  <code className="text-foreground dark:text-gray-200">{`// JavaScript Example
const executeWorkflow = async (inputData) => {
  try {
    const response = await axios.post('https://api.ruh.ai/workflow/${item.id}/execute', {
      input: inputData
    }, {
      headers: {
        'Authorization': 'Bearer your-ruh-api-key',
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error executing workflow:', error);
  }
};`}</code>
                </pre>
              </div>
            </div>

            {/* Builder URL if available */}
            {/* {item.rawData && item.rawData.builder_url && (
              <div className="mt-8">
                <h3 className="text-lg font-medium mb-3">Workflow Builder</h3>
                <p className="text-muted-foreground mb-3">
                  This workflow can be customized using the Workflow Builder.
                </p>
                <Button variant="outline">
                  Open in Workflow Builder
                </Button>
              </div>
            )} */}
          </div>
        </TabsContent>
      </Tabs>
    </BaseDetailPage>
  );
}
