'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search } from 'lucide-react';
import { SearchHeaderCards } from './search-header-cards';

interface SearchBarProps {
  className?: string;
}

export function SearchBar({ className = '' }: SearchBarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('MCPs');
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (!searchQuery.trim()) return;

    // Redirect based on selected type with search query
    const searchParams = new URLSearchParams();
    searchParams.set('search', searchQuery.trim());

    switch (selectedType) {
      case 'Agents':
        router.push(`/agents?${searchParams.toString()}`);
        break;
      case 'Workflows':
        router.push(`/workflows?${searchParams.toString()}`);
        break;
      case 'MCPs':
      default:
        router.push(`/mcps?${searchParams.toString()}`);
        break;
    }
  };

  return (
    <div className={`w-full bg-gradient-to-r from-purple-50/50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-900/95 dark:to-purple-900/20 pt-24 pb-36 px-4 ${className}`} >
      {/* Decorative gradient blobs */}
      <div className="absolute top-10 left-1/4 w-48 h-48 rounded-full radial-gradient-purple opacity-30 dark:opacity-20"></div>
      <div className="absolute top-1/3 right-1/4 w-56 h-56 rounded-full radial-gradient-blue opacity-25 dark:opacity-15"></div>
      <div className="absolute bottom-10 left-10 w-40 h-40 rounded-full radial-gradient-purple opacity-20 dark:opacity-10"></div>
      {/* <div className="absolute -bottom-10 right-20 w-64 h-64 rounded-full radial-gradient-pink opacity-15 dark:opacity-10"></div> */}

      <div className="relative w-full h-full hidden md:block">
        <SearchHeaderCards />
      </div>

      <div className="container mx-auto max-w-4xl relative z-20">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-5xl font-bold text-foreground">
            Discover & Deploy
          </h1>
          <h2 className="text-3xl md:text-5xl font-semibold text-purple-500 dark:text-purple-400 mt-1">
            AI Solutions for Your Needs
          </h2>
          <p className="text-muted-foreground mt-4 max-w-2xl mx-auto">
            Find the perfect AI tools to transform your business, research, and development projects.
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSearch} className="relative">
            <div className="flex items-stretch rounded-full border border-gray-200 dark:border-gray-700 bg-background p-1 shadow-lg">
              {/* Type selector */}
              <div className="flex items-center pl-4">
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="border-0 shadow-none focus:ring-0 w-24 h-9">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MCPs">MCPs</SelectItem>
                    <SelectItem value="Agents">Agents</SelectItem>
                    <SelectItem value="Workflows">Workflows</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Divider */}
              <div className="h-full w-px bg-border mx-2"></div>

              {/* Search input */}
              <div className="flex-1">
                <Input
                  type="search"
                  placeholder="Search for AI solutions..."
                  className="border-0 h-9 w-full rounded-full shadow-none focus-visible:ring-0 focus-visible:border-0"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Search button */}
              <Button
                type="submit"
                size="icon"
                className="rounded-full h-9 w-9 mr-1 bg-purple-600 hover:bg-purple-700"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
