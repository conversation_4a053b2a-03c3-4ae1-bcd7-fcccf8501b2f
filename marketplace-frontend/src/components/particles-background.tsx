"use client";

import { useEffect, useRef } from "react";

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  alpha: number;
}

export function ParticlesBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particles = useRef<Particle[]>([]);
  const animationFrameId = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas to full width/height
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initParticles();
    };

    // Initialize particles
    const initParticles = () => {
      particles.current = [];
      const particleCount = Math.min(Math.floor(window.innerWidth / 8), 150); // Increased particle count

      for (let i = 0; i < particleCount; i++) {
        particles.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 8 + 4, // Bigger particles (4-12px)
          speedX: (Math.random() - 0.5) * 0.3, // Slower movement for bigger particles
          speedY: (Math.random() - 0.5) * 0.3,
          color: getRandomColor(),
          alpha: Math.random() * 0.6 + 0.2, // Slightly more visible
        });
      }
    };

    // Get random color with purple/pink hues
    const getRandomColor = () => {
      const hue = Math.floor(Math.random() * 60) + 270; // 270-330 range (purples to pinks)
      const saturation = Math.floor(Math.random() * 30) + 70; // 70-100% saturation
      const lightness = Math.floor(Math.random() * 20) + 50; // 50-70% lightness
      return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.current.forEach((particle) => {
        // Move particle
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Wrap around edges
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.y > canvas.height) particle.y = 0;
        if (particle.y < 0) particle.y = canvas.height;

        // Draw spherical particle with gradient
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size
        );

        // Create gradient for spherical effect
        const baseColor = particle.color;
        gradient.addColorStop(0, baseColor); // Center of particle
        gradient.addColorStop(0.5, baseColor); // Mid-radius
        gradient.addColorStop(1, baseColor.replace(')', ', 0)').replace('rgb', 'rgba')); // Edge with transparency

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.globalAlpha = particle.alpha;
        ctx.fill();

        // Add highlight for 3D effect
        ctx.beginPath();
        ctx.arc(
          particle.x - particle.size * 0.2,
          particle.y - particle.size * 0.2,
          particle.size * 0.4,
          0, Math.PI * 2
        );
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fill();
      });

      // Connect particles with lines if they're close enough
      connectParticles(ctx);

      animationFrameId.current = requestAnimationFrame(animate);
    };

    // Connect nearby particles with lines
    const connectParticles = (ctx: CanvasRenderingContext2D) => {
      const maxDistance = 150; // Increased connection distance

      for (let i = 0; i < particles.current.length; i++) {
        for (let j = i + 1; j < particles.current.length; j++) {
          const dx = particles.current[i].x - particles.current[j].x;
          const dy = particles.current[i].y - particles.current[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < maxDistance) {
            // Draw line with opacity and width based on distance
            const opacity = 1 - (distance / maxDistance);
            const lineWidth = opacity * 2; // Thicker lines for closer particles

            // Create gradient line for more depth
            const gradient = ctx.createLinearGradient(
              particles.current[i].x, particles.current[i].y,
              particles.current[j].x, particles.current[j].y
            );

            // Get colors from both particles for the gradient
            const color1 = particles.current[i].color;
            const color2 = particles.current[j].color;

            gradient.addColorStop(0, color1.replace(')', `, ${opacity * 0.4})`).replace('hsl', 'hsla'));
            gradient.addColorStop(1, color2.replace(')', `, ${opacity * 0.4})`).replace('hsl', 'hsla'));

            ctx.beginPath();
            ctx.strokeStyle = gradient;
            ctx.lineWidth = lineWidth;
            ctx.moveTo(particles.current[i].x, particles.current[i].y);
            ctx.lineTo(particles.current[j].x, particles.current[j].y);
            ctx.stroke();
          }
        }
      }
    };

    // Set up canvas and start animation
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    animate();

    // Cleanup
    return () => {
      window.removeEventListener("resize", resizeCanvas);
      cancelAnimationFrame(animationFrameId.current);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none opacity-60 dark:opacity-40"
    />
  );
}
