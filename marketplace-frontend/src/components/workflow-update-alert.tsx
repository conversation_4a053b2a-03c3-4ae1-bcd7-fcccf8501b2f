'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronDown, ChevronUp, Loader2, Workflow, X } from 'lucide-react';
import { usePullUpdatesFromSourceWorkflow } from '@/hooks/use-workflows';
import { toast } from 'sonner';

interface WorkflowUpdateAlertProps {
  workflowId: string;
  sourceWorkflowId: string;
  onUpdateSuccess: () => void;
  onClose: () => void;
}

export function WorkflowUpdateAlert({
  workflowId,
  sourceWorkflowId,
  onUpdateSuccess,
  onClose
}: WorkflowUpdateAlertProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const { mutate: pullUpdates, isPending: isPullingUpdates } = usePullUpdatesFromSourceWorkflow(sourceWorkflowId);

  const handlePullUpdates = () => {
    pullUpdates(undefined, {
      onSuccess: () => {
        toast.success(`Successfully pulled updates for workflow ${workflowId}.`);
        onUpdateSuccess();
        setIsVisible(false); // Hide alert after successful update
      },
      onError: (error: Error) => {
        toast.error(`Failed to pull updates for workflow ${workflowId}. ${error.message}`);
      },
    });
  };

  const handleClose = () => {
    setIsVisible(false);
    onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950 dark:to-yellow-950 border border-yellow-300 dark:border-yellow-700 text-yellow-900 dark:text-yellow-100 shadow-lg mb-6 overflow-hidden rounded-lg">
      <CardContent className="p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Workflow className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          <p className="text-base font-semibold">New Version Available</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200/50 dark:hover:bg-yellow-800/50 rounded-full p-2 h-auto"
          >
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200/50 dark:hover:bg-yellow-800/50 rounded-full p-2 h-auto"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
      {!isCollapsed && (
        <div className="px-4 pb-4 pt-2 border-t border-yellow-200 dark:border-orange-800">
          <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-4">
            A newer version of this workflow is available. Would you like to update to the latest version?
          </p>
          <Button
            onClick={handlePullUpdates}
            disabled={isPullingUpdates}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200"
          >
            {isPullingUpdates ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Workflow className="mr-2 h-4 w-4" />
            )}
            {isPullingUpdates ? "Updating..." : "Yes, update now"}
          </Button>
        </div>
      )}
    </Card>
  );
} 