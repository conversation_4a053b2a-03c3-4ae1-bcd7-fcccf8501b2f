"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Footer } from "@/components/footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MarketplaceItemCard } from "@/components/marketplace-item-card";
import { ChevronLeft, Calendar, Download, Tag, User } from "lucide-react";
import { MarketplaceItem } from "@/data/marketplace-items";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";

interface ItemDetailPageProps {
  item: MarketplaceItem;
  relatedItems: MarketplaceItem[];
}

export function ItemDetailPage({ item, relatedItems }: ItemDetailPageProps) {
  const router = useRouter();
  const [isAdding, setIsAdding] = useState(false);
  const [installMethod, setInstallMethod] = useState<'json' | 'url'>('json');
  const [osType, setOsType] = useState<'mac' | 'windows' | 'wsl'>('mac');
  const [isInstallationOpen, setIsInstallationOpen] = useState(false);

  const typeColor = {
    MCP: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
    AGENT: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    WORKFLOW: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  };

  const handleAdd = () => {
    setIsAdding(true);
    // Simulate adding delay
    setTimeout(() => {
      setIsAdding(false);
    }, 2000);
  };

  // Mock tags based on category and type
  const tags = [item.category, "data analysis", "academic", "research"].slice(0, 3);

  return (
    <>
      <main className="min-h-screen bg-background">
        <div className="container px-4 py-8 md:px-6 md:py-12">
          {/* Back button */}
          <Button
            variant="ghost"
            size="sm"
            className="mb-6 -ml-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
            onClick={() => router.back()}
          >
            <ChevronLeft className="mr-1 h-4 w-4" />
            Back to marketplace
          </Button>

          <div className="grid gap-8 md:grid-cols-3">
            {/* Main content */}
            <div className="md:col-span-2">
              {/* Item header and image in flex layout */}
              <div className="flex flex-col md:flex-row gap-6 mb-8">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${typeColor[item.type]}`}>
                      {item.type}
                    </span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-yellow-500">★</span>
                      <span className="ml-1 text-sm font-medium">{item.rating.toFixed(1)}</span>
                      <span className="ml-1 text-sm text-muted-foreground">({item.use_count?.toLocaleString() || 0} uses)</span>
                    </div>
                  </div>
                  <h1 className="text-3xl font-bold">{item.title}</h1>
                  <p className="text-muted-foreground mb-4">by {item.owner_name || "RUH AI"}</p>
                </div>

                {/* Item image */}
                {/* <div className="relative w-full md:w-1/3 max-w-xs bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden" style={{ aspectRatio: '1/1', height: '180px' }}>
                  <Image
                    src={item.imageSrc}
                    alt={item.title}
                    fill
                    className="object-contain p-2"
                  />
                </div> */}
              </div>

              {/* Description */}
              {/* <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Description</h2>
                <p className="text-muted-foreground mb-4">
                  {item.description}
                </p>
              </div> */}
              {/* Tabs */}
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="w-full mb-4 justify-start gap-2">
                  <TabsTrigger value="overview" className="w-20">Overview</TabsTrigger>
                  {item.type === "WORKFLOW" && <TabsTrigger value="components">Components</TabsTrigger>}
                  {(item.type === "MCP" || item.type === "AGENT") && <TabsTrigger value="tools">Tools</TabsTrigger>}
                  {item.type === "AGENT" && <TabsTrigger value="workflows">Workflows</TabsTrigger>}
                  {/* <TabsTrigger value="integration">Integration</TabsTrigger> */}
                </TabsList>

                {/* Overview Tab Content */}
                <TabsContent value="overview">
                  {/* Description */}
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Description</h2>
                    <p className="text-muted-foreground mb-4">
                      {item.description}
                    </p>
                  </div>

              {/* Features */}
              {/* <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Features</h2>
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>Advanced integration with existing systems</li>
                  <li>Real-time data processing and analysis</li>
                  <li>Customizable parameters for different use cases</li>
                  <li>Detailed performance metrics and reporting</li>
                  <li>Regular updates and community support</li>
                </ul>
              </div> */}

                  {/* Requirements */}
                  {/* <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Requirements</h2>
                    <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                      <li>Compatible with RUH Marketplace Core v2.0+</li>
                      <li>Minimum 4GB RAM recommended</li>
                      <li>Internet connection for real-time features</li>
                    </ul>
                  </div> */}
                </TabsContent>

                {/* Components Tab Content - For Workflows */}
                <TabsContent value="components">
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Workflow Components</h2>
                    <p className="text-muted-foreground mb-6">
                      This workflow consists of the following components that work together to process and analyze data.
                    </p>

                    <div className="space-y-6">
                      {/* Component 1 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-600 dark:text-blue-400">
                              <path d="M12 4v16m-8-8h16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Data Ingestion</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Collects and processes data from various sources including APIs, databases, and file systems.
                        </p>
                        <div className="ml-9 mt-2 flex flex-wrap gap-2">
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                            Input
                          </span>
                          <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                            Processing
                          </span>
                        </div>
                      </div>

                      {/* Component 2 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-purple-600 dark:text-purple-400">
                              <path d="M9 3H5a2 2 0 00-2 2v4m6-6h10a2 2 0 012 2v4M3 9v10a2 2 0 002 2h4m-6-6h6m4 0h6m0 0v-6m0 6v4a2 2 0 01-2 2h-4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Data Transformation</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Cleans, normalizes, and transforms raw data into a structured format suitable for analysis.
                        </p>
                        <div className="ml-9 mt-2 flex flex-wrap gap-2">
                          <span className="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                            Transformation
                          </span>
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                            Middleware
                          </span>
                        </div>
                      </div>

                      {/* Component 3 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600 dark:text-green-400">
                              <path d="M9 19l-4-4m0 0l4-4m-4 4h14m-5-4l4 4m0 0l-4 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Analysis Engine</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Performs advanced analytics, machine learning, and statistical analysis on the processed data.
                        </p>
                        <div className="ml-9 mt-2 flex flex-wrap gap-2">
                          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300">
                            Analysis
                          </span>
                          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                            ML
                          </span>
                        </div>
                      </div>

                      {/* Component 4 */}
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-amber-100 dark:bg-amber-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600 dark:text-amber-400">
                              <path d="M9 17H5a2 2 0 01-2-2V5a2 2 0 012-2h14a2 2 0 012 2v10a2 2 0 01-2 2h-4l-4 4z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Output Generation</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Creates reports, visualizations, and actionable insights from the analyzed data.
                        </p>
                        <div className="ml-9 mt-2 flex flex-wrap gap-2">
                          <span className="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                            Output
                          </span>
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                            Visualization
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Dynamic Components from API Data */}
                    {item.rawData && item.rawData.workflow_url && typeof item.rawData.workflow_url === 'string' && item.rawData.workflow_url.startsWith('{') && (
                      <div className="mt-8 pt-6 border-t">
                        <h2 className="text-xl font-semibold mb-4">Workflow Structure</h2>
                        <p className="text-muted-foreground mb-4">
                          This workflow has a defined structure with transitions and nodes as shown below.
                        </p>
                        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md">
                          <p className="text-sm font-medium mb-2">Workflow Definition Available</p>
                          <p className="text-sm text-muted-foreground">
                            The workflow includes a detailed JSON definition with connections and transitions.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Tools Tab Content */}
                <TabsContent value="tools">
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Included Tools</h2>

                    <div className="space-y-6">
                      {/* Tool 1 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-600 dark:text-gray-300">
                              <path d="M12 4.5v15m7.5-7.5h-15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">LLM Integration</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Powerful capabilities for llm integration tasks with intelligent processing and integration options.
                        </p>
                      </div>

                      {/* Tool 2 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-600 dark:text-gray-300">
                              <path d="M12 4.5v15m7.5-7.5h-15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Document Processing</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Powerful capabilities for document processing tasks with intelligent processing and integration options.
                        </p>
                      </div>

                      {/* Tool 3 */}
                      <div className="pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-600 dark:text-gray-300">
                              <path d="M12 4.5v15m7.5-7.5h-15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Web Search</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Powerful capabilities for web search tasks with intelligent processing and integration options.
                        </p>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Workflows Tab Content - For Agents */}
                <TabsContent value="workflows">
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Associated Workflows</h2>

                    <div className="space-y-6">
                      {/* Workflow 1 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600 dark:text-green-400">
                              <path d="M14 5l7 7m0 0l-7 7m7-7H3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Content Creation Pipeline</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          End-to-end workflow for researching, drafting, editing, and publishing content across multiple platforms.
                        </p>
                        <div className="ml-9 mt-2">
                          <Button variant="outline" size="sm" className="text-xs">
                            View Workflow
                          </Button>
                        </div>
                      </div>

                      {/* Workflow 2 */}
                      <div className="border-b pb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-md">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600 dark:text-green-400">
                              <path d="M14 5l7 7m0 0l-7 7m7-7H3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium">Data Analysis Workflow</h3>
                        </div>
                        <p className="text-muted-foreground ml-9">
                          Comprehensive workflow for data processing, analysis, visualization, and reporting.
                        </p>
                        <div className="ml-9 mt-2">
                          <Button variant="outline" size="sm" className="text-xs">
                            View Workflow
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Integration Tab Content */}
                <TabsContent value="integration">
                  {/* API Integration - For MCPs */}
                  {item.type === "MCP" && (
                    <div className="mb-8">
                      <h2 className="text-xl font-semibold mb-4">API Integration</h2>
                      <p className="text-muted-foreground mb-4">
                        Integrate this MCP into your applications.
                      </p>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Get your API Key</h3>
                        <p className="text-muted-foreground mb-3">
                          You&apos;ll need to sign and generate a Nexus Labs API key to connect to this server.
                        </p>
                        <Button variant="secondary" className="bg-gray-800 hover:bg-gray-700 text-white dark:bg-gray-700 dark:hover:bg-gray-600">
                          Generate API Key
                        </Button>
                      </div>

                      {/* <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Installation</h3>
                        <p className="text-muted-foreground mb-3">
                          Install the official SDK using npm:
                        </p>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-sm mb-2 flex justify-between items-center">
                          <code>npm install @nexuslabs/research-assistant-sdk</code>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2v-2M8 5a2 2 0 002 2h8a2 2 0 002-2M8 5a2 2 0 012-2h8a2 2 0 012 2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                            </svg>
                            <span className="sr-only">Copy</span>
                          </Button>
                        </div>
                      </div> */}

                      <div>
                        <h3 className="text-lg font-medium mb-2">Code Examples</h3>
                        <div className="flex border-b">
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-primary">TypeScript</Button>
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-transparent">Python</Button>
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-transparent">cURL</Button>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-sm mt-2 overflow-x-auto">
                          <pre className="text-xs">
                            {`import axios from 'axios';

const fetchData = async () => {
  try {
    const response = await axios.get('https://api.nexuslabs.ai/research-assistant', {
      headers: {
        'Authorization': 'Bearer your-nexus-api-key',
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

fetchData();`}
                          </pre>
                        </div>
                      </div>

                      {/* Code Examples - Commented out as requested
                      <div>
                        <h3 className="text-lg font-medium mb-2">Code Examples</h3>
                        <div className="flex border-b">
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-primary">TypeScript</Button>
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-transparent">Python</Button>
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-transparent">cURL</Button>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-sm mt-2 overflow-x-auto">
                          <pre className="text-xs">
                            {`import axios from 'axios';

const fetchData = async () => {
  try {
    const response = await axios.get('https://api.nexuslabs.ai/research-assistant', {
      headers: {
        'Authorization': 'Bearer your-nexus-api-key',
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

fetchData();`}
                          </pre>
                        </div>
                      </div>
                      */}
                    </div>
                  )}

                  {/* Agent Integration - For Agents */}
                  {item.type === "AGENT" && (
                    <div className="mb-8">
                      <h2 className="text-xl font-semibold mb-4">Agent Integration</h2>
                      <p className="text-muted-foreground mb-4">
                        This agent can be integrated with your existing systems and workflows.
                      </p>

                      {/* Agent Configuration - Commented out as requested */}
                      {/*
                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Agent Configuration</h3>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-sm mb-2 flex justify-between items-center">
                          <code>npm install @ruh/agent-sdk</code>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Clipboard className="h-4 w-4" />
                            <span className="sr-only">Copy</span>
                          </Button>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-medium mb-2">Code Examples</h3>
                        <div className="flex border-b">
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-primary">TypeScript</Button>
                          <Button variant="ghost" className="text-sm px-4 py-2 rounded-none border-b-2 border-transparent">Python</Button>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-sm mt-2 overflow-x-auto">
                          <pre className="text-xs">Code examples would go here</pre>
                        </div>
                      </div>
                      */}

                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Agent Details</h3>
                        <p className="text-muted-foreground mb-3">
                          This agent provides advanced data analysis capabilities and can be integrated with your existing workflows.
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Workflow Integration - For Workflows */}
                  {item.type === "WORKFLOW" && (
                    <div className="mb-8">
                      <h2 className="text-xl font-semibold mb-4">Workflow Integration</h2>
                      <p className="text-muted-foreground mb-4">
                        This workflow can be integrated with your existing systems through our API or deployed as a standalone process.
                      </p>

                      {/* Workflow Details */}
                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Workflow Details</h3>
                        <div className="space-y-4">
                          {item.rawData && (
                            <>
                              {/* Version and Status */}
                              <div className="flex flex-wrap gap-3">
                                {item.rawData.version && (
                                  <div className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full text-sm">
                                    Version: {item.rawData.version}
                                  </div>
                                )}
                                {item.rawData.status && (
                                  <div className="bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full text-sm text-green-800 dark:text-green-300">
                                    Status: {item.rawData.status}
                                  </div>
                                )}
                                {item.rawData.visibility && (
                                  <div className="bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full text-sm text-blue-800 dark:text-blue-300">
                                    Visibility: {item.rawData.visibility}
                                  </div>
                                )}
                              </div>

                              {/* Start Nodes */}
                              {item.rawData.start_nodes && item.rawData.start_nodes.length > 0 && (
                                <div className="mt-4">
                                  <h4 className="text-md font-medium mb-2">Start Nodes</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {item.rawData.start_nodes.map((node: string, index: number) => (
                                      <div key={index} className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-md text-sm">
                                        {node}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Tags */}
                              {item.rawData.tags && Object.keys(item.rawData.tags).length > 0 && (
                                <div className="mt-4">
                                  <h4 className="text-md font-medium mb-2">Tags</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {Object.entries(item.rawData.tags).map(([key, value]: [string, unknown], index: number) => (
                                      <div key={index} className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-md text-sm">
                                        {key}: {Array.isArray(value) ? value.join(', ') : String(value)}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Workflow URLs */}
                              {(item.rawData.workflow_url || item.rawData.builder_url) && (
                                <div className="mt-4 p-4 border rounded-md">
                                  <h4 className="text-md font-medium mb-3">Workflow Resources</h4>

                                  {item.rawData.workflow_url && (
                                    <div className="mb-3">
                                      <div className="text-sm font-medium mb-1">Workflow Definition:</div>
                                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md text-xs font-mono overflow-x-auto">
                                        {typeof item.rawData.workflow_url === 'string' && item.rawData.workflow_url.startsWith('{')
                                          ? 'JSON Definition Available'
                                          : item.rawData.workflow_url}
                                      </div>
                                    </div>
                                  )}

                                  {item.rawData.builder_url && (
                                    <div>
                                      <div className="text-sm font-medium mb-1">Builder Configuration:</div>
                                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md text-xs font-mono overflow-x-auto">
                                        {typeof item.rawData.builder_url === 'string' && item.rawData.builder_url.startsWith('{')
                                          ? 'JSON Configuration Available'
                                          : item.rawData.builder_url}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )}


                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="md:col-span-1">
              <div className="sticky top-24">
                <Card>
                  <CardContent className="pt-6">
                    {/* Install Button */}
                    <div className="border rounded-md p-2 mb-4 mt-0">
                      <Button
                        className="w-full flex items-center justify-center gap-2"
                        size="lg"
                        disabled={isAdding}
                        onClick={handleAdd}
                      >
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                        </svg>
                        {isAdding ? "Adding..." : "Add to RUH"}
                      </Button>
                    </div>

                    {/* Item Details */}
                    <div className="space-y-4">
                      <div className="flex items-start gap-2">
                        <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">Author:</p>
                          <p className="text-sm text-muted-foreground">{item.owner_name || "RUH AI"}</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">Released:</p>
                          <p className="text-sm text-muted-foreground">15/01/2024</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <Download className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">Usage Count:</p>
                          <p className="text-sm text-muted-foreground">{item.use_count?.toLocaleString() || 0}</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <Tag className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">Tags:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {tags.map((tag) => (
                              <span
                                key={tag}
                                className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Installation UI - Only for MCPs */}
                    {item.type === "MCP" && (
                      <div className="mt-4 mb-6 border-b pb-6">
                        <div
                          className="flex items-center justify-between cursor-pointer mb-2 py-2 border-t pt-4"
                          onClick={() => setIsInstallationOpen(!isInstallationOpen)}
                        >
                          <h3 className="font-medium">Installation Details</h3>
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className={`transition-transform ${isInstallationOpen ? 'rotate-180' : ''}`}
                          >
                            <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                          </svg>
                        </div>

                        {isInstallationOpen && (
                          <>
                            {/* Installation method tabs */}
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-md p-1 flex mb-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                className={`flex-1 justify-center ${installMethod === 'json' ? 'rounded-md bg-white dark:bg-gray-700 shadow-sm' : ''}`}
                                onClick={() => setInstallMethod('json')}
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                                  <path d="M7 7l3-3 3 3m0 10l-3 3-3-3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                  <path d="M17 17l3-3-3-3m-10 0l-3 3 3 3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                                JSON
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={`flex-1 justify-center ${installMethod === 'url' ? 'rounded-md bg-white dark:bg-gray-700 shadow-sm' : ''}`}
                                onClick={() => setInstallMethod('url')}
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                                  <path d="M10 13a5 5 0 007.54.54l3-3a5 5 0 00-7.07-7.07l-1.72 1.71" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                  <path d="M14 11a5 5 0 00-7.54-.54l-3 3a5 5 0 007.07 7.07l1.71-1.71" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                                URL
                              </Button>
                            </div>

                            {/* JSON tab content */}
                            {installMethod === 'json' && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-2">Configuration in JSON format:</p>
                                <div className="flex gap-1 mb-3">
                                  <Button
                                    variant={osType === 'mac' ? 'secondary' : 'outline'}
                                    size="sm"
                                    className={`text-xs py-1 h-8`}
                                    onClick={() => setOsType('mac')}
                                  >
                                    Mac/Linux
                                  </Button>
                                  <Button
                                    variant={osType === 'windows' ? 'secondary' : 'outline'}
                                    size="sm"
                                    className={`text-xs py-1 h-8`}
                                    onClick={() => setOsType('windows')}
                                  >
                                    Windows
                                  </Button>
                                  <Button
                                    variant={osType === 'wsl' ? 'secondary' : 'outline'}
                                    size="sm"
                                    className={`text-xs py-1 h-8`}
                                    onClick={() => setOsType('wsl')}
                                  >
                                    WSL
                                  </Button>
                                </div>
                                <div className="flex items-center gap-1 mb-1">
                                  <span className="text-amber-500 text-xs">⬤</span>
                                  <span className="text-sm">JSON for {osType === 'mac' ? 'Mac/Linux' : osType === 'windows' ? 'Windows' : 'WSL'}</span>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-auto">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2v-2M8 5a2 2 0 002 2h8a2 2 0 002-2M8 5a2 2 0 012-2h8a2 2 0 012 2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                    </svg>
                                  </Button>
                                </div>
                                <div className="bg-white dark:bg-gray-800 border rounded-md p-3 text-xs font-mono overflow-hidden">
                                  <div className="flex items-start">
                                    <div className="text-blue-600 dark:text-blue-400 mr-1">📄</div>
                                    <div className="text-gray-700 dark:text-gray-300">research-assistant-mcp</div>
                                  </div>
                                  <div className="ml-4 mt-1">
                                    <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                                      {`{
  "mcpServers": {
    "research-assistant-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@ruh/cli@latest",
        "run",
        "@mem0ai/mcp-1",
        "--key",
        "9ee38a7f-cbc7-4397-a173",
        "--profile",
        "slippery-roadrunner-NE71cL"
      ]
    }
  }
}`}
                                    </pre>
                                  </div>
                                </div>
                                <div className="mt-3 p-3 bg-amber-100 dark:bg-amber-900/30 rounded-md text-xs text-amber-800 dark:text-amber-200 flex items-center gap-2">
                                  <span className="text-amber-500">⚠</span>
                                  <span>Your Ruh key is sensitive. Please don&apos;t share it with anyone.</span>
                                </div>
                              </div>
                            )}

                            {/* URL tab content */}
                            {installMethod === 'url' && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-2">Use this URL to connect to the server</p>
                                <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-md text-xs text-blue-800 dark:text-blue-200 flex items-center gap-2 mb-3">
                                  <span className="text-blue-500">ℹ</span>
                                  <span>This URL contains your authentication token. Keep it secure.</span>
                                </div>
                                <div className="flex items-center gap-1 mb-1">
                                  <span className="text-amber-500 text-xs">🔗</span>
                                  <span className="text-sm">HTTP URL</span>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-auto">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2v-2M8 5a2 2 0 002 2h8a2 2 0 002-2M8 5a2 2 0 012-2h8a2 2 0 012 2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
                                    </svg>
                                  </Button>
                                </div>
                                <div className="bg-white dark:bg-gray-800 border rounded-md p-3 text-xs font-mono overflow-hidden">
                                  <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                                    https://server.ruh.ai/@mem0ai/mcp-1/mcp?profile=slippery-roadrunner-NE71cL
                                  </pre>
                                </div>
                                <div className="mt-3 p-3 bg-amber-100 dark:bg-amber-900/30 rounded-md text-xs text-amber-800 dark:text-amber-200 flex items-center gap-2">
                                  <span className="text-amber-500">⚠</span>
                                  <span>Your Ruh key is sensitive. Please don&apos;t share it with anyone.</span>
                                </div>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Related Items */}
          {relatedItems.length > 0 && (
            <div className="mt-12">
              <h2 className="text-2xl font-semibold mb-6">Related Items</h2>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {relatedItems.map((relatedItem) => (
                  <MarketplaceItemCard
                    key={relatedItem.id}
                    {...relatedItem}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </>
  );
}
