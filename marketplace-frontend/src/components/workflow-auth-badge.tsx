'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldCheck } from 'lucide-react';
import { useWorkflowAuth } from '@/hooks/use-workflow-auth';

interface WorkflowAuthBadgeProps {
  workflowId: string;
  className?: string;
}

export function WorkflowAuthBadge({ workflowId, className }: WorkflowAuthBadgeProps) {
  const { data: authData, isLoading } = useWorkflowAuth(workflowId);

  if (isLoading) {
    return null; // Don't show anything while loading
  }

  if (!authData?.requires_authentication) {
    return (
      <Badge variant="secondary" className={className}>
        <ShieldCheck className="h-3 w-3 mr-1" />
        No Auth Required
      </Badge>
    );
  }

  const requirementCount = authData.credential_requirements?.credentials?.length || 0;

  return (
    <Badge variant="outline" className={className}>
      <Shield className="h-3 w-3 mr-1" />
      Auth Required ({requirementCount})
    </Badge>
  );
}
