// 'use client';

// import { useState } from 'react';
// import Image from 'next/image';
// import { useMCPs } from '@/hooks/use-mcps';
// import { useAgents } from '@/hooks/use-agents';
// import { useWorkflows } from '@/hooks/use-workflows';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// export function MarketplaceTabs() {
//   const [page, setPage] = useState(1);
//   const limit = 10;

//   // Fetch data using our custom hooks
//   const {
//     data: mcpData,
//     isLoading: mcpLoading
//   } = useMCPs({ page: page, limit: limit });

//   const {
//     data: agentData,
//     isLoading: agentLoading
//   } = useAgents({ page: page, limit: limit });

//   const {
//     data: workflowData,
//     isLoading: workflowLoading
//   } = useWorkflows({ page: page, limit: limit });

//   return (
//     <Tabs defaultValue="mcps" className="w-full">
//       <TabsList className="grid w-full grid-cols-3">
//         <TabsTrigger value="mcps">MCPs</TabsTrigger>
//         <TabsTrigger value="agents">Agents</TabsTrigger>
//         <TabsTrigger value="workflows">Workflows</TabsTrigger>
//       </TabsList>

//       <TabsContent value="mcps">
//         {mcpLoading ? (
//           <div className="flex justify-center p-8">Loading MCPs...</div>
//         ) : (
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
//             {mcpData?.templates.map((mcp) => (
//               <div key={mcp.id} className="border rounded-lg p-4 shadow-sm">
//                 <h3 className="text-lg font-semibold">{mcp.name}</h3>
//                 <p className="text-sm text-gray-500 mt-2">{mcp.description}</p>
//                 <div className="flex items-center justify-between mt-4">
//                   <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
//                     {mcp.category}
//                   </span>
//                   <span className="text-xs text-gray-500">
//                     {new Date(mcp.updated_at).toLocaleDateString()}
//                   </span>
//                 </div>
//               </div>
//             ))}
//           </div>
//         )}

//         {mcpData && mcpData.total_pages > 1 && (
//           <div className="flex justify-center mt-6 gap-2">
//             <button
//               onClick={() => setPage(Math.max(1, page - 1))}
//               disabled={page === 1}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Previous
//             </button>
//             <span className="flex items-center px-4">
//               Page {page} of {mcpData.total_pages}
//             </span>
//             <button
//               onClick={() => setPage(Math.min(mcpData.total_pages, page + 1))}
//               disabled={page === mcpData.total_pages}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Next
//             </button>
//           </div>
//         )}
//       </TabsContent>

//       <TabsContent value="agents">
//         {agentLoading ? (
//           <div className="flex justify-center p-8">Loading Agents...</div>
//         ) : (
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
//             {agentData?.templates.map((agent) => (
//               <div key={agent.id} className="border rounded-lg p-4 shadow-sm">
//                 <div className="flex items-center gap-3">
//                   {agent.avatar && (
//                     <div className="relative w-10 h-10">
//                       <Image
//                         src={agent.avatar}
//                         alt={agent.name}
//                         width={40}
//                         height={40}
//                         className="rounded-full object-cover"
//                       />
//                     </div>
//                   )}
//                   <h3 className="text-lg font-semibold">{agent.name}</h3>
//                 </div>
//                 <p className="text-sm text-gray-500 mt-2">{agent.description}</p>
//                 <div className="flex items-center justify-between mt-4">
//                   <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
//                     {agent.agent_category}
//                   </span>
//                   <span className="text-xs text-gray-500">
//                     {new Date(agent.updated_at).toLocaleDateString()}
//                   </span>
//                 </div>
//               </div>
//             ))}
//           </div>
//         )}

//         {agentData && agentData.total_pages > 1 && (
//           <div className="flex justify-center mt-6 gap-2">
//             <button
//               onClick={() => setPage(Math.max(1, page - 1))}
//               disabled={page === 1}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Previous
//             </button>
//             <span className="flex items-center px-4">
//               Page {page} of {agentData.total_pages}
//             </span>
//             <button
//               onClick={() => setPage(Math.min(agentData.total_pages, page + 1))}
//               disabled={page === agentData.total_pages}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Next
//             </button>
//           </div>
//         )}
//       </TabsContent>

//       <TabsContent value="workflows">
//         {workflowLoading ? (
//           <div className="flex justify-center p-8">Loading Workflows...</div>
//         ) : (
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
//             {workflowData?.templates.map((workflow) => (
//               <div key={workflow.id} className="border rounded-lg p-4 shadow-sm">
//                 <h3 className="text-lg font-semibold">{workflow.name}</h3>
//                 <p className="text-sm text-gray-500 mt-2">{workflow.description}</p>
//                 <div className="flex items-center justify-between mt-4">
//                   <div className="flex items-center gap-2">
//                     <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
//                       {workflow.category}
//                     </span>
//                     <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
//                       v{workflow.version}
//                     </span>
//                   </div>
//                   <span className="text-xs text-gray-500">
//                     {workflow.execution_count} executions
//                   </span>
//                 </div>
//               </div>
//             ))}
//           </div>
//         )}

//         {workflowData && workflowData.total_pages > 1 && (
//           <div className="flex justify-center mt-6 gap-2">
//             <button
//               onClick={() => setPage(Math.max(1, page - 1))}
//               disabled={page === 1}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Previous
//             </button>
//             <span className="flex items-center px-4">
//               Page {page} of {workflowData.total_pages}
//             </span>
//             <button
//               onClick={() => setPage(Math.min(workflowData.total_pages, page + 1))}
//               disabled={page === workflowData.total_pages}
//               className="px-4 py-2 border rounded-md disabled:opacity-50"
//             >
//               Next
//             </button>
//           </div>
//         )}
//       </TabsContent>
//     </Tabs>
//   );
// }
