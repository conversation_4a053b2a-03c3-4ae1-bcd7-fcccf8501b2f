'use client';

import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface AgentMiniCardProps {
  name: string;
  avatar?: string;
  skills?: string[];
  position?: 'left' | 'right';
  className?: string;
  type?: 'AGENT' | 'MCP' | 'WORKFLOW';
}

export function AgentMiniCard({
  name,
  avatar = '/assets/agent.png',
  skills = [],
  position = 'left',
  className,
  type = 'AGENT',
}: AgentMiniCardProps) {
  return (
    <div className={`absolute ${className} ${
      type === 'AGENT'
        ? 'animate-bounce-slow'
        : type === 'MCP'
        ? 'animate-pulse-slow'
        : 'animate-float-slow'
    } pointer-events-none`} style={{
      zIndex: 5,
      ['--rotation' as string]: position === 'left' ? '-3deg' : '3deg',
      ['--delay' as string]: `${Math.random() * 0.5}s`
    }}>
      <Card className="w-32 overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 rounded-xl">
        <CardContent className="p-2 flex flex-col items-center gap-1">
          <div className={`relative w-12 h-12 rounded-full overflow-hidden mb-1 border-2 ${
            type === 'AGENT'
              ? 'border-blue-100 dark:border-blue-800'
              : type === 'MCP'
              ? 'border-purple-100 dark:border-purple-800'
              : 'border-green-100 dark:border-green-800'
          }`}>
            <div className="absolute top-0 right-0 z-10">
              <Badge
                className={`text-[0.5rem] px-1 py-0 h-3 ${
                  type === 'AGENT'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300'
                    : type === 'MCP'
                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300'
                    : 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300'
                }`}
              >
                {/* Empty badge, type label removed */}
              </Badge>
            </div>
            <Image
              src={avatar}
              alt={name}
              fill
              className="object-cover"
            />
          </div>
          <h3 className="text-xs font-medium text-center line-clamp-1">{name}</h3>
          <div className="flex flex-wrap justify-center gap-1 mt-1">
            {skills.slice(0, 2).map((skill, index) => (
              <Badge
                key={index}
                variant="secondary"
                className={`text-[0.6rem] px-1 py-0 h-4 ${
                  type === 'AGENT'
                    ? 'bg-blue-100/50 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                    : type === 'MCP'
                    ? 'bg-purple-100/50 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                    : 'bg-green-100/50 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                }`}
              >
                {skill}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
