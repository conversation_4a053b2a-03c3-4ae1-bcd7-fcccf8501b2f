"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { AlignLeft, ChevronDown, Search, X } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { SearchHeaderCards } from "./search-header-cards";

interface FilterTag {
  id: string;
  label: string;
}

interface SearchHeaderProps {
  title?: string;
  subtitle?: string;
  onSearch?: (query: string, tags?: string) => void; // Updated to include tags parameter
  initialQuery?: string;
  initialTags?: string; // Added to support initial tags
  currentTab?: string;
  currentCategory?: string;
  onTabChange?: (value: string) => void;
  onCategoryChange?: (value: string) => void;
  onClearFilters?: () => void;
}

export function SearchHeader({
  title = "Discover & Deploy",
  subtitle = "Advanced AI Solutions",
  onSearch,
  initialQuery = "",
  initialTags = "",
  currentTab = "all",
  currentCategory = "all",
  onTabChange,
  onCategoryChange,
  onClearFilters,
}: SearchHeaderProps) {
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [activeTags, setActiveTags] = useState<string[]>([]);
  const router = useRouter();
  const pathname = usePathname();

  // Available filter tags with key:value format for API
  const filterTags = useMemo<FilterTag[]>(() => [
    { id: "research", label: "research" },
    { id: "data_analysis", label: "data analysis" },
    { id: "automation", label: "automation" },
    { id: "content", label: "content" },
    { id: "finance", label: "finance" },
    { id: "development", label: "development" },
    { id: "analytics", label: "analytics" },
    { id: "nlp", label: "nlp" },
  ], []);

  // Initialize search query from initialQuery prop
  useEffect(() => {
    setSearchQuery(initialQuery);
  }, [initialQuery]);

  // Initialize active tags from initialTags if provided
  // Using useEffect instead of useState
  useEffect(() => {
    if (initialTags) {
      const tagPairs = initialTags.split(',');
      const initialActiveTags = tagPairs.map(pair => pair.trim()).filter(pair => {
        // Find matching tag in filterTags
        return filterTags.some(tag => tag.id === pair);
      });

      if (initialActiveTags.length > 0) {
        setActiveTags(initialActiveTags);
      }
    }
  }, [initialTags, filterTags]);

  // Categories for the select dropdown
  const categories = [
    { value: "all", label: "All" },
    { value: "engineering", label: "Engineering" },
    { value: "marketing", label: "Marketing" },
    { value: "sales", label: "Sales" },
    { value: "customer_support", label: "Customer Support" },
    { value: "human_resources", label: "Human Resources" },
    { value: "finance", label: "Finance" }
  ];

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
  };

  // Convert active tags to API format (comma-separated key:value pairs)
  const getTagsString = (): string => {
    if (activeTags.length === 0) return '';
    return activeTags.join(',');
  };

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Get tags string in API format
    const tagsParam = getTagsString();

    if (onSearch) {
      // Pass both search query and tags to the onSearch handler
      onSearch(searchQuery, tagsParam);
    } else {
      // Create a new URLSearchParams object
      const params = new URLSearchParams();

      if (searchQuery) {
        params.set("search", searchQuery);
      }

      if (tagsParam) {
        params.set("tags", tagsParam);
      }

      router.push(`${pathname}?${params.toString()}`);
    }
  };

  // Handle filter tag click - adds tag to active tags
  const handleFilterTagClick = (tag: FilterTag) => {
    // Don't modify the search query directly anymore
    // Instead, just add to active tags for API filtering

    // Add to active tags if not already present
    if (!activeTags.includes(tag.id)) {
      const newActiveTags = [...activeTags, tag.id];
      setActiveTags(newActiveTags);

      // Get updated tags string
      const tagsParam = newActiveTags.join(',');

      // If onSearch is provided, call it with current search query and updated tags
      if (onSearch) {
        onSearch(searchQuery, tagsParam);
      }
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tagId: string) => {
    // Find the tag to remove
    const tagToRemove = filterTags.find(tag => tag.id === tagId);
    if (!tagToRemove) return;

    // Remove from active tags
    const newActiveTags = activeTags.filter(id => id !== tagId);
    setActiveTags(newActiveTags);

    // Get updated tags string
    const tagsParam = newActiveTags.length > 0 ? newActiveTags.join(',') : '';

    // If onSearch is provided, call it with current search query and updated tags
    if (onSearch) {
      onSearch(searchQuery, tagsParam);
    }
  };

  return (
    <div className="w-full relative bg-gradient-to-r from-purple-50/50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-900/95 dark:to-purple-900/20 pt-12 pb-6 px-4 md:pt-16 md:pb-8 overflow-hidden" style={{ minHeight: "300px", height: "auto", paddingBottom: "60px" }}>
      {/* Decorative gradient blobs */}
      {/* <div className="absolute top-10 left-1/4 w-48 h-48 rounded-full radial-gradient-purple opacity-30 dark:opacity-20"></div>
      <div className="absolute top-1/3 right-1/4 w-56 h-56 rounded-full radial-gradient-blue opacity-25 dark:opacity-15"></div>
      <div className="absolute bottom-10 left-10 w-40 h-40 rounded-full radial-gradient-purple opacity-20 dark:opacity-10"></div>
      <div className="absolute -bottom-10 right-20 w-64 h-64 rounded-full radial-gradient-pink opacity-15 dark:opacity-10"></div> */}

      {/* Animated marketplace cards - positioned absolutely, hidden on small screens */}
      {/* <div className="relative w-full h-full hidden md:block">
        <SearchHeaderCards />
      </div> */}

      <div className="container mx-auto max-w-4xl relative z-20">
        {/* <div className="text-center mb-8">
          <h1 className="text-3xl md:text-5xl font-bold text-foreground">
            {title}
          </h1>
          <h2 className="text-3xl md:text-5xl font-semibold text-purple-500 dark:text-purple-400 mt-1">
            {subtitle}
          </h2>
          <p className="text-muted-foreground mt-4 max-w-2xl mx-auto">
            Find the perfect AI tools to transform your business, research, and development projects.
          </p>
        </div> */}

        <div className="max-w-3xl mx-auto">
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="relative flex items-stretch rounded-full border border-gray-200 dark:border-gray-700 bg-background p-1 shadow-sm focus-within:ring-1 focus-within:ring-purple-300/50 dark:bg-background/90">
              {/* Tags dropdown */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-1 rounded-full px-3 py-2 text-sm font-medium text-muted-foreground hover:bg-transparent hover:text-foreground"
                  >
                    <AlignLeft className="h-4 w-4" />
                    <span>All Tags</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-4" align="start">
                  <div className="space-y-1">
                    <h4 className="font-medium mb-2">Filter by tags</h4>
                    <div className="space-y-3">
                      {filterTags.map((tag) => (
                        <div key={tag.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={tag.id}
                            checked={activeTags.includes(tag.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                handleFilterTagClick(tag);
                              } else {
                                handleRemoveTag(tag.id);
                              }
                            }}
                          />
                          <Label htmlFor={tag.id} className="text-sm font-normal cursor-pointer">
                            {tag.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Divider */}
              <div className="h-full w-px bg-border mx-1"></div>

              {/* Search input */}
              <div className="relative flex-1">
                <Input
                  type="search"
                  placeholder="Search for AI solutions..."
                  className="border-0 pl-4 h-9 w-full rounded-full shadow-none focus-visible:ring-0 focus-visible:border-0"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              {/* Search button */}
              <Button
                type="submit"
                size="icon"
                variant="ghost"
                className="rounded-full h-9 w-9 mr-1"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </form>

          {/* Filter section - Category and Tags aligned */}
          <div className="flex flex-wrap items-center justify-center gap-4 py-4 mt-4 bg-white/80 dark:bg-gray-900/80 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Category:</span>
              <Select
                value={currentCategory}
                onValueChange={(value) => onCategoryChange && onCategoryChange(value)}
                defaultValue="all"
              >
                <SelectTrigger className="w-[180px] border rounded-lg h-9">
                  <SelectValue>
                    {categories.find(cat => cat.value === currentCategory)?.label || "All"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {(searchQuery || currentCategory !== "all" || activeTags.length > 0) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onClearFilters && onClearFilters()}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear all filters
              </Button>
            )}
          </div>

          {/* Filter tags - positioned after the filter section */}
          <div className="flex flex-wrap items-center justify-center gap-2 mt-4">
            {activeTags.length > 0 ? (
              // Show active tags with remove option
              activeTags.map((tagId) => {
                const tag = filterTags.find(t => t.id === tagId);
                if (!tag) return null;
                return (
                  <Badge
                    key={tag.id}
                    variant="secondary"
                    className="bg-purple-500/10 hover:bg-purple-500/20 text-purple-700 dark:text-purple-300 rounded-full px-3 py-1 cursor-pointer flex items-center gap-1"
                    onClick={() => handleRemoveTag(tag.id)}
                  >
                    {tag.label}
                    <X className="h-3 w-3 ml-1" />
                  </Badge>
                );
              })
            ) : (
              // Show suggested tags when no active tags
              filterTags.slice(0, 4).map((tag) => (
                <Badge
                  key={tag.id}
                  variant="secondary"
                  className="bg-purple-500/10 hover:bg-purple-500/20 text-purple-700 dark:text-purple-300 rounded-full px-3 py-1 cursor-pointer flex items-center gap-1"
                  onClick={() => handleFilterTagClick(tag)}
                >
                  {tag.label}
                </Badge>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
