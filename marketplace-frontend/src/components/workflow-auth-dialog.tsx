'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, Key, AlertTriangle, CheckCircle } from 'lucide-react';
import { useWorkflowAuth, useWorkflowImportWithAuth, type CredentialMapping } from '@/hooks/use-workflow-auth';
import { useUserCredentials } from '@/hooks/use-user-credentials';

interface WorkflowAuthDialogProps {
  workflowId: string;
  workflowName: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (workflowId: string) => void;
}

export function WorkflowAuthDialog({
  workflowId,
  workflowName,
  isOpen,
  onClose,
  onSuccess,
}: WorkflowAuthDialogProps) {
  const [credentialMapping, setCredentialMapping] = useState<CredentialMapping>({});
  const [step, setStep] = useState<'analysis' | 'credentials' | 'importing'>('analysis');

  // Hooks
  const { data: authData, isLoading: isAnalyzing, error: authError } = useWorkflowAuth(workflowId);
  const { data: userCredentials, isLoading: isLoadingCredentials } = useUserCredentials();
  const importMutation = useWorkflowImportWithAuth();

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setCredentialMapping({});
      setStep('analysis');
    }
  }, [isOpen]);

  // Move to credentials step when analysis is complete
  useEffect(() => {
    if (authData && !authData.requires_authentication) {
      // No auth required, proceed directly to import
      handleImport();
    } else if (authData && authData.requires_authentication && step === 'analysis') {
      setStep('credentials');
    }
  }, [authData, step]);

  const handleCredentialSelect = (fieldName: string, credentialId: string) => {
    const credential = userCredentials?.find(c => c.id === credentialId);
    if (credential) {
      setCredentialMapping(prev => ({
        ...prev,
        [fieldName]: {
          credential_id: credentialId,
          credential_name: credential.name,
        },
      }));
    }
  };

  const handleImport = async () => {
    setStep('importing');
    try {
      const result = await importMutation.mutateAsync({
        workflowId,
        credentialMapping: Object.keys(credentialMapping).length > 0 ? credentialMapping : undefined,
      });
      onSuccess(result.workflow_id);
      onClose();
    } catch (error) {
      console.error('Import failed:', error);
      setStep('credentials');
    }
  };

  const canProceed = () => {
    if (!authData?.requires_authentication) return true;
    
    const requirements = authData.credential_requirements?.credentials || [];
    return requirements.every(req => 
      req.type === 'oauth' || credentialMapping[req.key || '']
    );
  };

  const renderAnalysisStep = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Analyzing workflow authentication requirements...</span>
      </div>
    </div>
  );

  const renderCredentialsStep = () => {
    if (!authData?.credential_requirements) return null;

    const requirements = authData.credential_requirements.credentials || [];
    const envRequirements = requirements.filter(req => req.type === 'env_key');
    const oauthRequirements = requirements.filter(req => req.type === 'oauth');

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">Authentication Required</h3>
        </div>

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            This workflow requires authentication to access external services. 
            Please configure your credentials below.
          </AlertDescription>
        </Alert>

        {envRequirements.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                API Keys & Environment Variables
              </CardTitle>
              <CardDescription>
                Select existing credentials or you'll need to create them after import.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {envRequirements.map((req) => (
                <div key={req.key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">{req.key}</label>
                      <p className="text-xs text-muted-foreground">{req.description}</p>
                      <Badge variant="secondary" className="text-xs">
                        Used by {req.component_count} component{req.component_count !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </div>
                  
                  <Select
                    value={credentialMapping[req.key || '']?.credential_id || ''}
                    onValueChange={(value) => handleCredentialSelect(req.key || '', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a credential or create after import" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Create after import</SelectItem>
                      {userCredentials?.map((cred) => (
                        <SelectItem key={cred.id} value={cred.id}>
                          {cred.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {oauthRequirements.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>OAuth Connections</CardTitle>
              <CardDescription>
                These will be configured automatically during workflow execution.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {oauthRequirements.map((req) => (
                <div key={`${req.provider}-${req.tool_name}`} className="flex items-center gap-2 py-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">
                    {req.provider} ({req.tool_name}) - {req.description}
                  </span>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderImportingStep = () => (
    <div className="space-y-4 text-center">
      <Loader2 className="h-8 w-8 animate-spin mx-auto" />
      <div>
        <h3 className="text-lg font-semibold">Importing Workflow</h3>
        <p className="text-muted-foreground">
          Setting up your workflow with the configured authentication...
        </p>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import "{workflowName}"</DialogTitle>
          <DialogDescription>
            {step === 'analysis' && 'Checking authentication requirements...'}
            {step === 'credentials' && 'Configure authentication for this workflow'}
            {step === 'importing' && 'Importing workflow to your workspace...'}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {authError && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Failed to analyze workflow: {authError.message}
              </AlertDescription>
            </Alert>
          )}

          {step === 'analysis' && renderAnalysisStep()}
          {step === 'credentials' && renderCredentialsStep()}
          {step === 'importing' && renderImportingStep()}
        </div>

        <DialogFooter>
          {step === 'credentials' && (
            <>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleImport} 
                disabled={!canProceed() || importMutation.isPending}
              >
                {importMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Importing...
                  </>
                ) : (
                  'Import Workflow'
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
