'use client';

import Image from 'next/image';

interface ItemAvatarProps {
  title: string;
  className?: string;
  fallback?: string;
  type?: "MCP" | "AGENT" | "WORKFLOW";
  imageUrl?: string;
}

export function ItemAvatar({ title, className = "", fallback = "I", type, imageUrl }: ItemAvatarProps) {
  // Default image URL - use a public image in our project
  const defaultImageUrl = "/assets/default_avatar.png";
  // Get item initials for avatar fallback
  const getItemInitials = () => {
    if (!title) {
      return fallback;
    }

    // Handle case where title might be empty or not a string
    const name = typeof title === 'string' ? title.trim() : '';
    if (!name) {
      return fallback;
    }

    // Split by spaces and get first letter of each part
    const nameParts = name.split(" ").filter(part => part.length > 0);

    if (nameParts.length === 0) {
      return fallback;
    }

    // If only one name part, take up to two characters from it
    if (nameParts.length === 1) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }

    // Otherwise take first letter of first and last parts
    return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
  };

  // Get background color based on type
  const getBgColor = () => {
    switch (type) {
      case "MCP":
        return "bg-gray-600";
      case "AGENT":
        return "bg-blue-600";
      case "WORKFLOW":
        return "bg-green-600";
      default:
        return "bg-blue-600";
    }
  };

  // Function to ensure URL is properly formatted
  const getValidImageUrl = (url?: string): string => {
    if (!url) return defaultImageUrl;

    // If it's already an absolute URL (starts with http:// or https://)
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it's a relative URL but doesn't start with a slash, add one
    if (!url.startsWith('/')) {
      return `/${url}`;
    }

    return url;
  };

  return (
    <div className={`relative overflow-hidden rounded-md ${className}`}>
      <Image
        src={getValidImageUrl(imageUrl)}
        alt={title}
        fill
        className="w-full h-full object-cover"
        onError={(e) => {
          // If image fails to load, show a colored background with initials
          const imgElement = e.target as HTMLImageElement;
          imgElement.style.display = 'none';
          // Find the next sibling and remove hidden class
          const nextElement = imgElement.nextElementSibling as HTMLElement;
          if (nextElement) nextElement.classList.remove('hidden');
        }}
      />
      <div
        className={`${getBgColor()} text-white font-medium flex items-center justify-center absolute inset-0 hidden`}
        style={{
          fontSize: className.includes('h-12') ? '1.25rem' : className.includes('h-16') ? '1.5rem' : '1rem',
        }}
      >
        {getItemInitials()}
      </div>
    </div>
  );
}
