"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { ParticlesBackground } from "./particles-background";

export function AnimatedHero() {
  // State for animated particles
  const [mounted, setMounted] = useState(false);

  // Set mounted state after component mounts to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <section className="relative bg-purple-50 dark:bg-gray-900 py-16 md:py-24 overflow-hidden">
      {/* Particles background */}
      {mounted && <ParticlesBackground />}

      {/* Enhanced animated background elements */}
      <div className="absolute inset-0">
        {/* Main background blobs with enhanced animations */}
        <div
          className="absolute left-0 top-0 h-96 w-96 rounded-full bg-purple-200/40 dark:bg-purple-900/30 blur-3xl
                    animate-in fade-in duration-1000 blob-animation-1"
        ></div>
        <div
          className="absolute right-0 bottom-0 h-96 w-96 rounded-full bg-purple-200/40 dark:bg-purple-900/30 blur-3xl
                    animate-in fade-in duration-1000 delay-300 blob-animation-2"
        ></div>
        <div
          className="absolute left-1/2 top-1/3 -translate-x-1/2 h-64 w-64 rounded-full bg-pink-200/30 dark:bg-pink-900/20 blur-3xl
                    animate-in fade-in duration-1000 delay-500 blob-animation-3"
        ></div>

        {/* Additional decorative elements */}
        {mounted && (
          <>
            {/* Small floating particles */}
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute rounded-full bg-primary/20 dark:bg-primary/10 animate-in fade-in"
                style={{
                  width: `${Math.random() * 10 + 5}px`,
                  height: `${Math.random() * 10 + 5}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `float-up-down ${Math.random() * 5 + 5}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 5}s`,
                }}
              ></div>
            ))}
          </>
        )}
      </div>

      <div className="container relative z-10">
        <div className="flex flex-col items-center text-center mx-auto">
          {/* Enhanced animated heading with shimmer effect */}
          <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl max-w-3xl relative">
            <span className="inline-block animate-in fade-in slide-in-from-bottom-8 duration-700">
              Discover the Future of{" "}
            </span>
            <span className="text-shimmer font-extrabold inline-block animate-in fade-in slide-in-from-bottom-4 duration-700 delay-300 glow-animation">
              AI Agents
            </span>
          </h1>

          {/* Animated paragraph with floating animation */}
          <p className="mt-6 text-gray-600 dark:text-gray-300 text-lg max-w-2xl animate-in fade-in slide-in-from-bottom-4 duration-700 delay-500 float-animation">
            Explore our marketplace of Multi-Agent Cognitive Protocols, Specialized Agents, and Automated Workflows to enhance your AI capabilities.
          </p>

          {/* Enhanced animated buttons with pulse effect */}
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <div className="animate-in fade-in zoom-in-95 duration-700 delay-700">
              <Button
                size="lg"
                className="bg-primary text-primary-foreground hover:bg-primary/90 button-hover pulse-button"
                asChild
              >
                <Link href="/marketplace">Explore Marketplace</Link>
              </Button>
            </div>
            <div className="animate-in fade-in zoom-in-95 duration-700 delay-900">
              <Button
                size="lg"
                variant="outline"
                className="button-hover"
              >
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Animated gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-purple-50/70 dark:to-gray-900/70 pointer-events-none"></div>
    </section>
  );
}
