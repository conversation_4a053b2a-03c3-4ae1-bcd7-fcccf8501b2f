// Types for marketplace items
export type ItemType = "MCP" | "AGENT" | "WORKFLOW";

export interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  category: string;
  owner_name: string; // Previously author
  rating: number;
  use_count: number; // Previously downloads
  imageSrc: string;
  type: ItemType;
  rawData?: any; // Raw API response data for additional details
  average_rating?: number;
  is_added?: boolean; // Whether the item is already added to RUH by the current user
}
