FROM node:20-alpine as builder
WORKDIR /app
COPY package*.json ./
COPY tsconfig*.json ./
# Copy .env for build
COPY .env ./
RUN npm install
COPY . .
RUN npm run build
# Production stage
FROM node:20-alpine
WORKDIR /app
COPY --from=builder /app ./
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# # Performance optimizations
ENV NODE_OPTIONS='--max-old-space-size=950'
ENV NEXT_SHARP_PATH=/tmp/node_modules/sharp
CMD ["npm", "start"]
