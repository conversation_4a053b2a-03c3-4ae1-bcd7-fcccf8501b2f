#!/bin/bash

# Test Database Setup Script
# Creates a separate test database for marketplace authentication testing

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
TEST_DB_NAME="workflow_service_test"
TEST_DB_USER="postgres"  # Adjust as needed
TEST_DB_HOST="localhost"
TEST_DB_PORT="5432"

echo -e "${BLUE}🗄️  Setting up Test Database for Marketplace Authentication${NC}"
echo "============================================================="

# Function to check if PostgreSQL is running
check_postgres() {
    echo -e "${YELLOW}🔍 Checking PostgreSQL connection...${NC}"
    
    if pg_isready -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER >/dev/null 2>&1; then
        echo -e "${GREEN}   ✅ PostgreSQL is running${NC}"
        return 0
    else
        echo -e "${RED}   ❌ PostgreSQL is not running or not accessible${NC}"
        echo -e "   💡 Start PostgreSQL first: brew services start postgresql"
        return 1
    fi
}

# Function to create test database
create_test_database() {
    echo -e "${YELLOW}🔧 Creating test database: $TEST_DB_NAME${NC}"
    
    # Check if database already exists
    if psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER -lqt | cut -d \| -f 1 | grep -qw $TEST_DB_NAME; then
        echo -e "${YELLOW}   ⚠️  Database $TEST_DB_NAME already exists${NC}"
        read -p "   🤔 Do you want to drop and recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}   🗑️  Dropping existing database...${NC}"
            dropdb -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER $TEST_DB_NAME
            echo -e "${GREEN}   ✅ Database dropped${NC}"
        else
            echo -e "${BLUE}   ℹ️  Using existing database${NC}"
            return 0
        fi
    fi
    
    # Create the database
    createdb -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER $TEST_DB_NAME
    echo -e "${GREEN}   ✅ Test database created: $TEST_DB_NAME${NC}"
}

# Function to create test environment file
create_test_env() {
    echo -e "${YELLOW}🔧 Creating test environment configuration...${NC}"
    
    # Create test environment file for workflow-service
    cat > "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/.env.test" << EOF
# Test Database Configuration
DATABASE_URL=postgresql://$TEST_DB_USER@$TEST_DB_HOST:$TEST_DB_PORT/$TEST_DB_NAME

# Test Environment Settings
ENVIRONMENT=test
DEBUG=true
LOG_LEVEL=DEBUG

# Test API Settings
API_HOST=0.0.0.0
API_PORT=8001

# Test Authentication (if needed)
JWT_SECRET_KEY=test-secret-key-for-marketplace-auth-testing
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Test External Services (mock endpoints)
AGENT_SERVICE_URL=http://localhost:8002
USER_SERVICE_URL=http://localhost:8003

# Test Redis (if needed)
REDIS_URL=redis://localhost:6379/1
EOF

    echo -e "${GREEN}   ✅ Test environment file created: workflow-service/.env.test${NC}"
    
    # Create test environment file for api-gateway
    cat > "/Users/<USER>/Desktop/ruh_ai/backend/api-gateway/.env.test" << EOF
# Test Environment Configuration
ENVIRONMENT=test
DEBUG=true
LOG_LEVEL=DEBUG

# Test API Settings
API_HOST=0.0.0.0
API_PORT=8000

# Test Service URLs
WORKFLOW_SERVICE_URL=http://localhost:8001
AGENT_SERVICE_URL=http://localhost:8002
USER_SERVICE_URL=http://localhost:8003

# Test Authentication
JWT_SECRET_KEY=test-secret-key-for-marketplace-auth-testing
JWT_ALGORITHM=HS256

# Test Redis
REDIS_URL=redis://localhost:6379/1
EOF

    echo -e "${GREEN}   ✅ Test environment file created: api-gateway/.env.test${NC}"
}

# Function to run migrations on test database
run_test_migrations() {
    echo -e "${YELLOW}🔧 Running migrations on test database...${NC}"
    
    cd "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service"
    
    # Set test environment
    export DATABASE_URL="postgresql://$TEST_DB_USER@$TEST_DB_HOST:$TEST_DB_PORT/$TEST_DB_NAME"
    
    # Run migrations
    echo -e "   📊 Current migration status:"
    poetry run alembic current || echo "   ⚠️  No migrations applied yet"
    
    echo -e "   🔄 Applying migrations..."
    poetry run alembic upgrade head
    
    echo -e "${GREEN}   ✅ Migrations completed${NC}"
}

# Function to verify test database setup
verify_test_setup() {
    echo -e "${YELLOW}🔍 Verifying test database setup...${NC}"
    
    # Check database connection
    if psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER -d $TEST_DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Database connection successful${NC}"
    else
        echo -e "${RED}   ❌ Database connection failed${NC}"
        return 1
    fi
    
    # Check if tables exist
    TABLE_COUNT=$(psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
    echo -e "   📊 Tables in test database: $TABLE_COUNT"
    
    # List tables
    echo -e "   📋 Available tables:"
    psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER -d $TEST_DB_NAME -c "\dt" | grep -E "^\s+(public\s+)?\w+" | awk '{print "      - " $2}' || echo "      (No tables found)"
    
    echo -e "${GREEN}   ✅ Test database verification completed${NC}"
}

# Function to create test data
create_test_data() {
    echo -e "${YELLOW}🔧 Creating test data...${NC}"
    
    cd "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service"
    
    # Set test environment
    export DATABASE_URL="postgresql://$TEST_DB_USER@$TEST_DB_HOST:$TEST_DB_PORT/$TEST_DB_NAME"
    
    # Create test workflow with credential requirements
    poetry run python -c "
import sys
sys.path.append('app')
from app.database.database import get_db
from app.models.workflow import Workflow
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import determine_env_credential_status
import uuid
from datetime import datetime

# Sample workflow data with authentication requirements
workflow_data = {
    'name': 'Test AI Content Generator',
    'description': 'Test workflow requiring OpenAI and GitHub authentication',
    'workflow_url': '/test/ai-content-generator',
    'builder_url': '/test/ai-content-generator/builder',
    'owner_id': 'test-user-123',
    'owner_type': 'USER',
    'visibility': 'PUBLIC',
    'start_nodes': [
        {
            'id': 'openai-node',
            'type': 'openai_chat',
            'inputs': {
                'openai_api_key': {
                    'input_type': 'credential',
                    'required': True,
                    'description': 'OpenAI API key'
                },
                'query': {
                    'input_type': 'string',
                    'required': True,
                    'value': 'Generate content'
                }
            }
        }
    ],
    'available_nodes': [
        {
            'id': 'github-node',
            'type': 'github_api',
            'inputs': {
                'github_token': {
                    'input_type': 'credential',
                    'required': True,
                    'description': 'GitHub token'
                }
            }
        }
    ]
}

try:
    db = next(get_db())
    
    # Analyze workflow for credentials
    analyzer = OptimizedCredentialAnalyzer()
    credential_summary = analyzer.analyze_workflow_optimized(workflow_data)
    env_status = determine_env_credential_status(credential_summary)
    
    # Create test workflow
    test_workflow = Workflow(
        id=str(uuid.uuid4()),
        name=workflow_data['name'],
        description=workflow_data['description'],
        workflow_url=workflow_data['workflow_url'],
        builder_url=workflow_data['builder_url'],
        start_nodes=workflow_data['start_nodes'],
        available_nodes=workflow_data['available_nodes'],
        owner_id=workflow_data['owner_id'],
        owner_type=workflow_data['owner_type'],
        visibility=workflow_data['visibility']
    )
    
    # Add credential analysis if fields exist
    if hasattr(test_workflow, 'credential_summary'):
        test_workflow.credential_summary = credential_summary.dict()
        test_workflow.env_credential_status = env_status
    
    db.add(test_workflow)
    db.commit()
    db.refresh(test_workflow)
    
    print(f'✅ Test workflow created: {test_workflow.id}')
    print(f'📊 Credential requirements: {credential_summary.total_requirements}')
    print(f'⏱️  Setup time: {credential_summary.estimated_setup_time} minutes')
    
    db.close()
    
except Exception as e:
    print(f'❌ Error creating test data: {str(e)}')
    import traceback
    traceback.print_exc()
"
    
    echo -e "${GREEN}   ✅ Test data creation completed${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}🚀 Starting test database setup...${NC}"
    echo ""
    
    # Step 1: Check PostgreSQL
    if ! check_postgres; then
        exit 1
    fi
    echo ""
    
    # Step 2: Create test database
    create_test_database
    echo ""
    
    # Step 3: Create test environment files
    create_test_env
    echo ""
    
    # Step 4: Run migrations
    run_test_migrations
    echo ""
    
    # Step 5: Verify setup
    verify_test_setup
    echo ""
    
    # Step 6: Create test data
    create_test_data
    echo ""
    
    # Summary
    echo "============================================================="
    echo -e "${GREEN}🎉 Test Database Setup Complete!${NC}"
    echo "============================================================="
    echo -e "📊 Database: $TEST_DB_NAME"
    echo -e "🔗 Connection: postgresql://$TEST_DB_USER@$TEST_DB_HOST:$TEST_DB_PORT/$TEST_DB_NAME"
    echo -e "📁 Environment files: .env.test (created in both services)"
    echo ""
    echo -e "${BLUE}💡 Next Steps:${NC}"
    echo "   1. Start services with test environment: ./start_test_services.sh"
    echo "   2. Run API tests: ./test_marketplace_auth_curl.sh"
    echo "   3. Verify test data: psql -d $TEST_DB_NAME -c 'SELECT id, name FROM workflows;'"
    echo ""
    echo -e "${YELLOW}⚠️  Remember: This is a separate test database - your production data is safe!${NC}"
}

# Handle Ctrl+C
trap 'echo -e "\n${YELLOW}🛑 Setup interrupted${NC}"; exit 1' INT

# Run main function
main
