Preparing to execute workflow with ID: 4cf0e8e9-620f-4961-95b5-38b22b6ff58e

Sending workflow execution request...

✅ Workflow execution started successfully

Streaming logs with correlation ID: b9b5007f-934b-4d1e-8f9e-e48c60ca7842-1753861693228827

Connected to execution stream...

Stream connected: {
  "message": "Stream connected",
  "id": "b9b5007f-934b-4d1e-8f9e-e48c60ca7842-1753861693228827"
}

{
  "workflow_id": "4cf0e8e9-620f-4961-95b5-38b22b6ff58e",
  "status": "Initialized",
  "message": "Workflow Initialized",
  "result": "Workflow Initialized",
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-SelectDataComponent-1753852841462",
  "message": "Starting execution...",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "status": "started",
  "sequence": 0,
  "workflow_status": "running"
}

{
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data",
  "tool_name": "SelectDataComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Select Data",
  "status": "connecting",
  "sequence": 1,
  "workflow_status": "running"
}

{
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data",
  "tool_name": "SelectDataComponent",
  "message": "Transition Result received.",
  "result": {
    "output_data": "7"
  },
  "status": "completed",
  "sequence": 2,
  "workflow_status": "running",
  "raw_result": {
    "output_data": "7"
  },
  "approval_required": false
}

{
  "result": "Completed transition in 1.72 seconds",
  "message": "Transition completed in 1.72 seconds",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "status": "time_logged",
  "sequence": 3,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-LoopNode-1753852849426",
  "message": "Starting execution...",
  "transition_id": "transition-LoopNode-1753852849426",
  "status": "started",
  "sequence": 4,
  "workflow_status": "running"
}

{
  "transition_id": "transition-LoopNode-1753852849426",
  "node_label": "For Each Loop",
  "tool_name": "LoopNode",
  "message": "Connecting to server",
  "result": "Connecting to server For Each Loop",
  "status": "connecting",
  "sequence": 5,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 6,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 7,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 8,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 9,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 10,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 11,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 3",
  "status": "completed",
  "sequence": 12,
  "workflow_status": "running",
  "raw_result": "ran 3",
  "approval_required": false
}

{
  "result": "Completed transition in 3.79 seconds",
  "message": "Transition completed in 3.79 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 13,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 4",
  "status": "completed",
  "sequence": 14,
  "workflow_status": "running",
  "raw_result": "ran 4",
  "approval_required": false
}

{
  "result": "Completed transition in 4.46 seconds",
  "message": "Transition completed in 4.46 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 15,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 5",
  "status": "completed",
  "sequence": 16,
  "workflow_status": "running",
  "raw_result": "ran 5",
  "approval_required": false
}

{
  "result": "Completed transition in 5.81 seconds",
  "message": "Transition completed in 5.81 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 17,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 18,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 19,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 20,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 21,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 22,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 23,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 6",
  "status": "completed",
  "sequence": 24,
  "workflow_status": "running",
  "raw_result": "ran 6",
  "approval_required": false
}

{
  "result": "Completed transition in 4.11 seconds",
  "message": "Transition completed in 4.11 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 25,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 7",
  "status": "completed",
  "sequence": 26,
  "workflow_status": "running",
  "raw_result": "ran 7",
  "approval_required": false
}

{
  "result": "Completed transition in 4.71 seconds",
  "message": "Transition completed in 4.71 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 27,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 8",
  "status": "completed",
  "sequence": 28,
  "workflow_status": "running",
  "raw_result": "ran 8",
  "approval_required": false
}

{
  "result": "Completed transition in 5.48 seconds",
  "message": "Transition completed in 5.48 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 29,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 30,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 31,
  "workflow_status": "running"
}

{
  "result": "Starting execution of transition: transition-CombineTextComponent-1753852886677",
  "message": "Starting execution...",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "started",
  "sequence": 32,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Connecting to server",
  "result": "Connecting to server Combine Text",
  "status": "connecting",
  "sequence": 33,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 9",
  "status": "completed",
  "sequence": 34,
  "workflow_status": "running",
  "raw_result": "ran 9",
  "approval_required": false
}

{
  "result": "Completed transition in 3.11 seconds",
  "message": "Transition completed in 3.11 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 35,
  "workflow_status": "running"
}

{
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text",
  "tool_name": "CombineTextComponent",
  "message": "Transition Result received.",
  "result": "ran 10",
  "status": "completed",
  "sequence": 36,
  "workflow_status": "running",
  "raw_result": "ran 10",
  "approval_required": false
}

{
  "result": "Completed transition in 3.84 seconds",
  "message": "Transition completed in 3.84 seconds",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "status": "time_logged",
  "sequence": 37,
  "workflow_status": "running"
}

{
  "transition_id": "transition-LoopNode-1753852849426",
  "node_label": "For Each Loop",
  "tool_name": "LoopNode",
  "message": "Transition Result received.",
  "result": {
    "output_type": "loop_completion",
    "final_results": [
      "ran 3",
      "ran 4",
      "ran 5",
      "ran 6",
      "ran 7",
      "ran 8",
      "ran 9",
      "ran 10"
    ],
    "current_item": null,
    "aggregation_metadata": {
      "aggregation_type": "collect_all",
      "total_iterations": 8,
      "successful_iterations": 8,
      "failed_iterations": 0
    },
    "loop_metadata": {
      "loop_id": "loop_transition-LoopNode-1753852849426_7c06778c",
      "transition_id": "transition-LoopNode-1753852849426",
      "total_iterations": 8,
      "execution_mode": "parallel",
      "exit_transitions": [
        "transition-CombineTextComponent-1753852886677"
      ],
      "original_input_data": {
        "end": "7",
        "source_type": "number_range",
        "batch_size": "1",
        "start": "3",
        "step": "1",
        "parallel_execution": true,
        "max_concurrent": 3,
        "preserve_order": true,
        "iteration_timeout": 60,
        "aggregation_type": "collect_all",
        "include_metadata": true,
        "on_iteration_error": "continue",
        "include_errors": true
      }
    }
  },
  "status": "completed",
  "sequence": 38,
  "workflow_status": "running",
  "raw_result": {
    "output_type": "loop_completion",
    "final_results": [
      "ran 3",
      "ran 4",
      "ran 5",
      "ran 6",
      "ran 7",
      "ran 8",
      "ran 9",
      "ran 10"
    ],
    "current_item": null,
    "aggregation_metadata": {
      "aggregation_type": "collect_all",
      "total_iterations": 8,
      "successful_iterations": 8,
      "failed_iterations": 0
    },
    "loop_metadata": {
      "loop_id": "loop_transition-LoopNode-1753852849426_7c06778c",
      "transition_id": "transition-LoopNode-1753852849426",
      "total_iterations": 8,
      "execution_mode": "parallel",
      "exit_transitions": [
        "transition-CombineTextComponent-1753852886677"
      ],
      "original_input_data": {
        "end": "7",
        "source_type": "number_range",
        "batch_size": "1",
        "start": "3",
        "step": "1",
        "parallel_execution": true,
        "max_concurrent": 3,
        "preserve_order": true,
        "iteration_timeout": 60,
        "aggregation_type": "collect_all",
        "include_metadata": true,
        "on_iteration_error": "continue",
        "include_errors": true
      }
    }
  },
  "approval_required": false
}

{
  "result": "Completed transition in 24.76 seconds",
  "message": "Transition completed in 24.76 seconds",
  "transition_id": "transition-LoopNode-1753852849426",
  "status": "time_logged",
  "sequence": 39,
  "workflow_status": "running"
}

{
  "status": "complete",
  "result": "Workflow '4cf0e8e9-620f-4961-95b5-38b22b6ff58e' executed successfully.",
  "workflow_status": "completed"
}

✅ Workflow completed successfully