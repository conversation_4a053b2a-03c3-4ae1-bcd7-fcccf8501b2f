#!/bin/bash

# Marketplace Authentication API Testing Script
# Tests the complete flow using curl commands

set -e  # Exit on any error

# Configuration
API_GATEWAY_URL="http://localhost:8000"
WORKFLOW_SERVICE_URL="http://localhost:8001"
AUTH_TOKEN="Bearer test-token-123"  # Replace with real token
TEST_DB_NAME="workflow_service_test"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Marketplace Authentication API Testing (Test Database)${NC}"
echo "=========================================================="
echo -e "${YELLOW}🗄️  Using test database: $TEST_DB_NAME${NC}"
echo ""

# Test workflow data
WORKFLOW_DATA='{
  "name": "AI Content Generator Test",
  "description": "Test workflow with OpenAI and GitHub authentication",
  "workflow_url": "/test/ai-content-generator",
  "builder_url": "/test/ai-content-generator/builder",
  "owner_id": "test-user-123",
  "owner_type": "USER",
  "visibility": "PUBLIC",
  "start_nodes": [
    {
      "id": "openai-node",
      "type": "openai_chat",
      "inputs": {
        "openai_api_key": {
          "input_type": "credential",
          "required": true,
          "description": "OpenAI API key"
        },
        "query": {
          "input_type": "string",
          "required": true,
          "value": "Generate content"
        }
      }
    }
  ],
  "available_nodes": [
    {
      "id": "github-node",
      "type": "github_api",
      "inputs": {
        "github_token": {
          "input_type": "credential",
          "required": true,
          "description": "GitHub token"
        },
        "repository": {
          "input_type": "string",
          "required": true,
          "value": "my-repo"
        }
      }
    }
  ]
}'

# Function to check if service is running
check_service() {
    local url=$1
    local name=$2
    
    echo -e "${YELLOW}🔍 Checking if $name is running...${NC}"
    
    if curl -s --connect-timeout 5 "$url/health" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ $name is running${NC}"
        return 0
    else
        echo -e "${RED}   ❌ $name is not running at $url${NC}"
        return 1
    fi
}

# Function to create test workflow
create_test_workflow() {
    echo -e "${YELLOW}🔧 Step 1: Creating test workflow...${NC}"
    
    # Try to create workflow via workflow service
    RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Authorization: $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$WORKFLOW_DATA" \
        "$WORKFLOW_SERVICE_URL/workflows" 2>/dev/null || echo "HTTPSTATUS:000")
    
    HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$HTTP_STATUS" -eq 201 ]; then
        WORKFLOW_ID=$(echo $BODY | python3 -c "import sys, json; print(json.load(sys.stdin)['id'])" 2>/dev/null || echo "test-workflow-123")
        echo -e "${GREEN}   ✅ Workflow created: $WORKFLOW_ID${NC}"
        echo "$WORKFLOW_ID"
    else
        echo -e "${YELLOW}   ⚠️  Using mock workflow ID (service may not be running)${NC}"
        echo "test-workflow-123"
    fi
}

# Function to test auth summary endpoint
test_auth_summary() {
    local workflow_id=$1
    echo -e "${YELLOW}🔍 Step 2: Testing authentication summary endpoint...${NC}"
    
    RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -H "Authorization: $AUTH_TOKEN" \
        "$API_GATEWAY_URL/marketplace/workflows/$workflow_id/auth-summary" 2>/dev/null || echo "HTTPSTATUS:000")
    
    HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')
    
    echo -e "   📡 GET /marketplace/workflows/$workflow_id/auth-summary"
    echo -e "   📊 Status: $HTTP_STATUS"
    
    if [ "$HTTP_STATUS" -eq 200 ]; then
        echo -e "${GREEN}   ✅ Authentication summary retrieved!${NC}"
        echo -e "   📋 Response:"
        echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
        return 0
    else
        echo -e "${RED}   ❌ Failed with status $HTTP_STATUS${NC}"
        echo -e "   Response: $BODY"
        return 1
    fi
}

# Function to test analyze action
test_analyze_action() {
    local workflow_id=$1
    echo -e "${YELLOW}🔬 Step 3: Testing analyze action...${NC}"
    
    ANALYZE_DATA='{"action": "analyze"}'
    
    RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Authorization: $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$ANALYZE_DATA" \
        "$API_GATEWAY_URL/marketplace/workflows/$workflow_id/import-with-auth" 2>/dev/null || echo "HTTPSTATUS:000")
    
    HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')
    
    echo -e "   📡 POST /marketplace/workflows/$workflow_id/import-with-auth"
    echo -e "   📦 Payload: $ANALYZE_DATA"
    echo -e "   📊 Status: $HTTP_STATUS"
    
    if [ "$HTTP_STATUS" -eq 200 ]; then
        echo -e "${GREEN}   ✅ Analysis completed successfully!${NC}"
        echo -e "   📋 Response:"
        echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
        return 0
    else
        echo -e "${RED}   ❌ Failed with status $HTTP_STATUS${NC}"
        echo -e "   Response: $BODY"
        return 1
    fi
}

# Function to test import action
test_import_action() {
    local workflow_id=$1
    echo -e "${YELLOW}📥 Step 4: Testing import action...${NC}"
    
    IMPORT_DATA='{
        "action": "import",
        "credential_mapping": {
            "openai_api_key": {
                "credential_id": "cred-openai-123",
                "credential_name": "My OpenAI Key"
            },
            "github_token": {
                "credential_id": "cred-github-456",
                "credential_name": "My GitHub Token"
            }
        }
    }'
    
    RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Authorization: $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$IMPORT_DATA" \
        "$API_GATEWAY_URL/marketplace/workflows/$workflow_id/import-with-auth" 2>/dev/null || echo "HTTPSTATUS:000")
    
    HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')
    
    echo -e "   📡 POST /marketplace/workflows/$workflow_id/import-with-auth"
    echo -e "   📊 Status: $HTTP_STATUS"
    
    if [ "$HTTP_STATUS" -eq 200 ]; then
        echo -e "${GREEN}   ✅ Import completed successfully!${NC}"
        echo -e "   📋 Response:"
        echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
        return 0
    else
        echo -e "${RED}   ❌ Failed with status $HTTP_STATUS${NC}"
        echo -e "   Response: $BODY"
        return 1
    fi
}

# Main test execution
main() {
    local start_time=$(date +%s)
    
    # Check if services are running
    echo -e "${BLUE}🔍 Pre-flight checks${NC}"
    echo "------------------------"
    
    API_GATEWAY_RUNNING=false
    WORKFLOW_SERVICE_RUNNING=false
    
    if check_service "$API_GATEWAY_URL" "API Gateway"; then
        API_GATEWAY_RUNNING=true
    fi
    
    if check_service "$WORKFLOW_SERVICE_URL" "Workflow Service"; then
        WORKFLOW_SERVICE_RUNNING=true
    fi
    
    echo ""
    
    # Create test workflow
    WORKFLOW_ID=$(create_test_workflow)
    echo ""
    
    # Run tests
    AUTH_SUMMARY_SUCCESS=false
    ANALYZE_SUCCESS=false
    IMPORT_SUCCESS=false
    
    if test_auth_summary "$WORKFLOW_ID"; then
        AUTH_SUMMARY_SUCCESS=true
    fi
    echo ""
    
    if test_analyze_action "$WORKFLOW_ID"; then
        ANALYZE_SUCCESS=true
    fi
    echo ""
    
    if test_import_action "$WORKFLOW_ID"; then
        IMPORT_SUCCESS=true
    fi
    echo ""
    
    # Summary
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "=============================================="
    echo -e "${BLUE}📊 TEST SUMMARY${NC}"
    echo "=============================================="
    echo -e "⏱️  Duration: ${duration}s"
    echo -e "🆔 Workflow ID: $WORKFLOW_ID"
    echo ""
    echo -e "🔧 Services:"
    echo -e "   API Gateway: $([ "$API_GATEWAY_RUNNING" = true ] && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
    echo -e "   Workflow Service: $([ "$WORKFLOW_SERVICE_RUNNING" = true ] && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
    echo ""
    echo -e "🧪 Tests:"
    echo -e "   Auth Summary: $([ "$AUTH_SUMMARY_SUCCESS" = true ] && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
    echo -e "   Analyze Action: $([ "$ANALYZE_SUCCESS" = true ] && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
    echo -e "   Import Action: $([ "$IMPORT_SUCCESS" = true ] && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
    echo ""
    
    if [ "$AUTH_SUMMARY_SUCCESS" = true ] && [ "$ANALYZE_SUCCESS" = true ]; then
        echo -e "${GREEN}🎉 Marketplace Authentication API is working!${NC}"
    else
        echo -e "${YELLOW}⚠️  Some endpoints need attention${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}💡 To start services:${NC}"
    echo "   API Gateway: cd api-gateway && poetry run uvicorn app.main:app --port 8000"
    echo "   Workflow Service: cd workflow-service && poetry run uvicorn app.main:app --port 8001"
}

# Run the main function
main
