"""
Credential analysis models for workflow authentication
Implements the single field approach aligned with MCP pattern
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class CredentialRequirementSpec(BaseModel):
    """Individual credential requirement specification"""
    credential_type: str = Field(..., description="Type of credential (api_key, oauth, password)")
    field_name: str = Field(..., description="Field name in component")
    is_required: bool = Field(True, description="Whether credential is required")
    component_count: int = Field(1, description="Number of components using this credential")
    description: Optional[str] = Field(None, description="Human-readable description")
    provider_name: Optional[str] = Field(None, description="Provider name (openai, github, etc.)")

    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ProviderSummary(BaseModel):
    """Summary of credentials needed for a specific provider"""
    count: int = Field(..., description="Number of components using this provider")
    types: List[str] = Field(..., description="Types of credentials needed")
    required: bool = Field(..., description="Whether any credential from this provider is required")
    fields: List[str] = Field(..., description="List of field names needed")

    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CredentialSummary(BaseModel):
    """
    Simplified credential summary stored in workflows.credential_summary JSONB
    Single field approach - stores both env and oauth credentials together
    """
    total_requirements: int
    by_provider: Dict[str, ProviderSummary]
    estimated_setup_time: int = Field(..., description="Estimated setup time in minutes")
    last_analyzed: datetime = Field(default_factory=datetime.utcnow)
    credential_requirements: List[CredentialRequirementSpec] = Field(default_factory=list)
    
    @property
    def requires_authentication(self) -> bool:
        """
        Simple check: if credential_requirements is populated, auth is required
        This aligns with the MCP pattern where env_credential_status is determined
        by the presence of credentials
        """
        return len(self.credential_requirements) > 0

    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OptimizedImportRequest(BaseModel):
    """Request model for optimized import endpoint"""
    action: str = Field(..., description="'analyze' or 'import'")
    credential_mapping: Optional[Dict[str, Dict[str, str]]] = Field(
        None, 
        description="Mapping of field_name to {credential_id, credential_name}"
    )

    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CredentialCoverageReport(BaseModel):
    """User's credential coverage against workflow requirements"""
    total_requirements: int
    available_credentials: int
    missing_credentials: int
    coverage_percentage: float
    detailed_status: List[Dict[str, Any]]

    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Helper functions for credential status determination
def determine_env_credential_status(credential_summary: Optional[Dict[str, Any]]) -> str:
    """
    Determine env_credential_status based on credential summary
    Follows MCP pattern: 'pending_input' if credentials needed, 'not_required' if not
    """
    if not credential_summary:
        return "not_required"
    
    credential_requirements = credential_summary.get("credential_requirements", [])
    
    if credential_requirements and len(credential_requirements) > 0:
        return "pending_input"  # Authentication required
    else:
        return "not_required"   # No authentication needed


def create_credential_summary_from_dict(data: Dict[str, Any]) -> CredentialSummary:
    """
    Create CredentialSummary from dictionary data
    Handles conversion of nested dictionaries to proper models
    """
    # Convert provider summaries
    by_provider = {}
    for provider_name, provider_data in data.get("by_provider", {}).items():
        by_provider[provider_name] = ProviderSummary(**provider_data)
    
    # Convert credential requirements
    credential_requirements = []
    for req_data in data.get("credential_requirements", []):
        credential_requirements.append(CredentialRequirementSpec(**req_data))
    
    # Handle datetime conversion
    last_analyzed = data.get("last_analyzed")
    if isinstance(last_analyzed, str):
        last_analyzed = datetime.fromisoformat(last_analyzed.replace('Z', '+00:00'))
    elif last_analyzed is None:
        last_analyzed = datetime.utcnow()
    
    return CredentialSummary(
        total_requirements=data.get("total_requirements", 0),
        by_provider=by_provider,
        estimated_setup_time=data.get("estimated_setup_time", 0),
        analysis_version=data.get("analysis_version", 2),
        last_analyzed=last_analyzed,
        credential_requirements=credential_requirements
    )


# Validation functions
def validate_credential_mapping(credential_mapping: Dict[str, Dict[str, str]]) -> bool:
    """
    Validate credential mapping structure
    Each mapping should have credential_id and credential_name
    """
    if not credential_mapping:
        return True  # Empty mapping is valid
    
    for field_name, mapping in credential_mapping.items():
        if not isinstance(mapping, dict):
            return False
        
        required_keys = {"credential_id", "credential_name"}
        if not required_keys.issubset(mapping.keys()):
            return False
    
    return True


def validate_import_request(request: OptimizedImportRequest) -> List[str]:
    """
    Validate import request and return list of validation errors
    """
    errors = []
    
    # Validate action
    if request.action not in ["analyze", "import"]:
        errors.append("Action must be 'analyze' or 'import'")
    
    # Validate credential mapping if provided
    if request.credential_mapping and not validate_credential_mapping(request.credential_mapping):
        errors.append("Invalid credential mapping structure")
    
    # For import action, credential mapping should be provided
    if request.action == "import" and not request.credential_mapping:
        errors.append("Credential mapping required for import action")
    
    return errors


# Constants for credential types and providers
CREDENTIAL_TYPES = {
    "api_key": "API Key",
    "oauth": "OAuth Token", 
    "password": "Password",
    "token": "Access Token"
}

KNOWN_PROVIDERS = {
    "openai": "OpenAI",
    "github": "GitHub",
    "google": "Google",
    "slack": "Slack",
    "discord": "Discord",
    "anthropic": "Anthropic",
    "telegram": "Telegram"
}

# Default setup times (in minutes) by credential type
DEFAULT_SETUP_TIMES = {
    "api_key": 3,
    "oauth": 10,
    "password": 5,
    "token": 3
}


def get_provider_display_name(provider_name: str) -> str:
    """Get display name for provider"""
    return KNOWN_PROVIDERS.get(provider_name, provider_name.title())


def get_credential_type_display_name(credential_type: str) -> str:
    """Get display name for credential type"""
    return CREDENTIAL_TYPES.get(credential_type, credential_type.replace("_", " ").title())


def estimate_setup_time_for_type(credential_type: str) -> int:
    """Get estimated setup time for credential type"""
    return DEFAULT_SETUP_TIMES.get(credential_type, 5)
