"""
Test workflow model for credential migration testing
This allows testing marketplace authentication without affecting the original workflows table
"""

from datetime import datetime
import uuid
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Float,
    ForeignKey,
    Enum,
    JSON,
    Boolean,
    ARRAY,
    Text,
)
from sqlalchemy.orm import declarative_base

# Use the same base as the main models
from app.models.workflow import Base

# Import enums from constants
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)


class WorkflowCredentialMigrationTest(Base):
    """
    Test workflow model with credential analysis fields
    Safe for testing marketplace authentication functionality
    """
    __tablename__ = "workflows_credential_migration_test"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    image_url = Column(String, nullable=True)

    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    start_nodes = Column(JSON, nullable=False, default=lambda: [])
    available_nodes = Column(JSON, nullable=False, default=lambda: [])

    owner_id = Column(String, nullable=False)
    owner_type = Column(Enum(WorkflowOwnerTypeEnum), nullable=False)

    # Link to the current active version (for testing, we'll use String)
    current_version_id = Column(String, nullable=True)

    # If this workflow was created by cloning from another workflow
    is_imported = Column(Boolean, default=False)
    workflow_template_id = Column(String, nullable=True)
    template_owner_id = Column(String, nullable=True)
    source_version_id = Column(String, nullable=True)
    is_customizable = Column(Boolean, default=True)

    # New field to track if workflow has been updated since last version creation
    is_updated = Column(Boolean, default=False)

    user_ids = Column(ARRAY(String), nullable=True, default=list)

    visibility = Column(
        Enum(WorkflowVisibilityEnum), nullable=False, default=WorkflowVisibilityEnum.PRIVATE
    )
    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    category = Column(Enum(WorkflowCategoryEnum), nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True, default=None)
    
    # ✅ NEW: Marketplace Authentication Fields for Testing
    credential_summary = Column(
        JSON, 
        nullable=True, 
        default=None, 
        comment="Precomputed credential requirements for marketplace authentication"
    )
    env_credential_status = Column(
        String(20), 
        nullable=True, 
        default='not_required',
        comment="Authentication status: not_required, pending_input"
    )

    def __repr__(self):
        return f"<WorkflowCredentialMigrationTest id={self.id} name='{self.name}'>"

    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'image_url': self.image_url,
            'workflow_url': self.workflow_url,
            'builder_url': self.builder_url,
            'start_nodes': self.start_nodes,
            'available_nodes': self.available_nodes,
            'owner_id': self.owner_id,
            'owner_type': self.owner_type.value if self.owner_type else None,
            'visibility': self.visibility.value if self.visibility else None,
            'status': self.status.value if self.status else None,
            'category': self.category.value if self.category else None,
            'tags': self.tags,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'credential_summary': self.credential_summary,
            'env_credential_status': self.env_credential_status,
        }
