"""Create test workflows table for credential migration testing

Revision ID: 006_create_test_workflows_credential_migration_test
Revises: 005_add_credential_analysis_fields
Create Date: 2025-01-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_create_test_workflows_credential_migration_test'
down_revision = '8df2016f8a76'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create a test workflows table with credential analysis fields for safe testing
    This allows testing marketplace authentication without affecting the original workflows table
    """
    
    # Create test workflows table with all original fields plus credential analysis fields
    op.create_table(
        'workflows_credential_migration_test',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('image_url', sa.String(), nullable=True),
        sa.Column('workflow_url', sa.String(), nullable=False),
        sa.Column('builder_url', sa.String(), nullable=False),
        sa.Column('start_nodes', sa.JSON(), nullable=False, default=lambda: []),
        sa.Column('available_nodes', sa.JSON(), nullable=False, default=lambda: []),
        sa.Column('owner_id', sa.String(), nullable=False),
        sa.Column('owner_type', sa.String(), nullable=False),
        sa.Column('current_version_id', sa.String(), nullable=True),
        sa.Column('is_imported', sa.Boolean(), default=False),
        sa.Column('workflow_template_id', sa.String(), nullable=True),
        sa.Column('template_owner_id', sa.String(), nullable=True),
        sa.Column('source_version_id', sa.String(), nullable=True),
        sa.Column('is_customizable', sa.Boolean(), default=True),
        sa.Column('is_updated', sa.Boolean(), default=False),
        sa.Column('user_ids', postgresql.ARRAY(sa.String()), nullable=True, default=list),
        sa.Column('visibility', sa.String(), nullable=False, default='PRIVATE'),
        sa.Column('status', sa.String(), nullable=False, default='ACTIVE'),
        sa.Column('category', sa.String(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True, default=list),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, default=None),
        
        # NEW: Marketplace Authentication Fields for testing
        sa.Column(
            'credential_summary', 
            postgresql.JSON(astext_type=sa.Text()), 
            nullable=True, 
            default=None,
            comment="Precomputed credential requirements for marketplace authentication"
        ),
        sa.Column(
            'env_credential_status', 
            sa.String(20), 
            nullable=True, 
            default='not_required',
            comment="Authentication status: not_required, pending_input"
        ),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance testing
    op.create_index(
        'idx_workflows_credential_test_credential_summary', 
        'workflows_credential_migration_test', 
        ['credential_summary'], 
        postgresql_using='gin'
    )
    
    op.create_index(
        'idx_workflows_credential_test_env_status', 
        'workflows_credential_migration_test', 
        ['env_credential_status']
    )
    
    op.create_index(
        'idx_workflows_credential_test_visibility', 
        'workflows_credential_migration_test', 
        ['visibility']
    )


def downgrade() -> None:
    """
    Drop the test workflows table and its indexes
    Safe to run - only removes test table
    """
    op.drop_index('idx_workflows_credential_test_visibility', table_name='workflows_credential_migration_test')
    op.drop_index('idx_workflows_credential_test_env_status', table_name='workflows_credential_migration_test')
    op.drop_index('idx_workflows_credential_test_credential_summary', table_name='workflows_credential_migration_test')
    op.drop_table('workflows_credential_migration_test')
