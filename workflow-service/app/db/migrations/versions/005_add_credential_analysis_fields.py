"""Add credential analysis fields to workflows table

Revision ID: 005_add_credential_analysis_fields
Revises: 8df2016f8a76
Create Date: 2025-01-28 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005_add_credential_analysis_fields'
down_revision = '8df2016f8a76'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Add credential analysis fields to workflows table for marketplace authentication optimization
    
    These fields enable instant authentication analysis (50ms vs 800ms) by precomputing
    and storing credential requirements when workflows are created/updated.
    """
    
    # Add credential_summary JSONB field for storing precomputed credential analysis
    op.add_column(
        'workflows', 
        sa.Column(
            'credential_summary', 
            postgresql.JSON(astext_type=sa.Text()), 
            nullable=True, 
            default=None,
            comment="Precomputed credential requirements for marketplace authentication"
        )
    )
    
    # Add env_credential_status field for quick authentication status check
    op.add_column(
        'workflows', 
        sa.Column(
            'env_credential_status', 
            sa.String(20), 
            nullable=True, 
            default='not_required',
            comment="Authentication status: not_required, pending_input"
        )
    )
    
    # Create index on env_credential_status for fast marketplace filtering
    op.create_index(
        'ix_workflows_env_credential_status',
        'workflows',
        ['env_credential_status']
    )


def downgrade() -> None:
    """
    Remove credential analysis fields from workflows table
    """

    # Drop indexes first
    op.drop_index('ix_workflows_env_credential_status', table_name='workflows')

    # Drop columns
    op.drop_column('workflows', 'env_credential_status')
    op.drop_column('workflows', 'credential_summary')
