"""
Test service for marketplace authentication using the test workflows table
This allows safe testing without affecting production workflows
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from sqlalchemy.orm import Session
from sqlalchemy import and_

# Test model
from app.models.workflow_test import WorkflowCredentialMigrationTest

# Credential analysis
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import (
    CredentialSummary,
    CredentialCoverageReport,
    OptimizedImportRequest,
    determine_env_credential_status,
    create_credential_summary_from_dict,
    validate_import_request
)

logger = logging.getLogger(__name__)


class WorkflowAuthTestService:
    """
    Test service for marketplace authentication functionality
    Uses the test workflows table for safe testing
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.analyzer = OptimizedCredentialAnalyzer()
    
    def create_test_workflow(self, workflow_data: Dict[str, Any]) -> WorkflowCredentialMigrationTest:
        """
        Create a test workflow with credential analysis
        """
        try:
            # Analyze workflow for credentials
            credential_summary = self.analyzer.analyze_workflow_optimized(workflow_data)
            env_status = determine_env_credential_status(credential_summary)
            
            # Create test workflow
            test_workflow = WorkflowCredentialMigrationTest(
                name=workflow_data.get('name', 'Test Workflow'),
                description=workflow_data.get('description', 'Test workflow for credential analysis'),
                workflow_url=workflow_data.get('workflow_url', '/test'),
                builder_url=workflow_data.get('builder_url', '/test/builder'),
                start_nodes=workflow_data.get('start_nodes', []),
                available_nodes=workflow_data.get('available_nodes', []),
                owner_id=workflow_data.get('owner_id', 'test-user'),
                owner_type=workflow_data.get('owner_type', 'USER'),
                visibility=workflow_data.get('visibility', 'PUBLIC'),
                credential_summary=credential_summary.dict(),
                env_credential_status=env_status
            )
            
            self.db.add(test_workflow)
            self.db.commit()
            self.db.refresh(test_workflow)
            
            logger.info(f"Created test workflow {test_workflow.id} with credential analysis")
            return test_workflow
            
        except Exception as e:
            logger.error(f"Failed to create test workflow: {str(e)}")
            self.db.rollback()
            raise
    
    def get_test_workflow_with_auth_summary(self, workflow_id: str) -> Optional[Dict]:
        """
        Get test workflow with authentication summary
        """
        try:
            workflow = self.db.query(WorkflowCredentialMigrationTest).filter(
                WorkflowCredentialMigrationTest.id == workflow_id
            ).first()
            
            if not workflow:
                return None
            
            return {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'start_nodes': workflow.start_nodes,
                'available_nodes': workflow.available_nodes,
                'credential_summary': workflow.credential_summary,
                'env_credential_status': workflow.env_credential_status,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get test workflow {workflow_id}: {str(e)}")
            return None
    
    def analyze_and_update_test_workflow(self, workflow_id: str, workflow_data: Dict) -> bool:
        """
        Analyze and update test workflow with credential requirements
        """
        try:
            workflow = self.db.query(WorkflowCredentialMigrationTest).filter(
                WorkflowCredentialMigrationTest.id == workflow_id
            ).first()
            
            if not workflow:
                logger.warning(f"Test workflow {workflow_id} not found")
                return False
            
            # Analyze credentials
            credential_summary = self.analyzer.analyze_workflow_optimized(workflow_data)
            env_status = determine_env_credential_status(credential_summary)
            
            # Update workflow
            workflow.credential_summary = credential_summary.dict()
            workflow.env_credential_status = env_status
            workflow.updated_at = datetime.utcnow()
            
            # Update workflow data if provided
            if 'start_nodes' in workflow_data:
                workflow.start_nodes = workflow_data['start_nodes']
            if 'available_nodes' in workflow_data:
                workflow.available_nodes = workflow_data['available_nodes']
            
            self.db.commit()
            logger.info(f"Updated test workflow {workflow_id} with credential analysis")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update test workflow {workflow_id}: {str(e)}")
            self.db.rollback()
            return False
    
    def get_marketplace_test_workflows(self, limit: int = 10) -> List[Dict]:
        """
        Get test workflows for marketplace testing
        """
        try:
            workflows = self.db.query(WorkflowCredentialMigrationTest).filter(
                WorkflowCredentialMigrationTest.visibility == 'PUBLIC'
            ).limit(limit).all()
            
            result = []
            for workflow in workflows:
                result.append({
                    'id': workflow.id,
                    'name': workflow.name,
                    'description': workflow.description,
                    'credential_summary': workflow.credential_summary,
                    'env_credential_status': workflow.env_credential_status,
                    'requires_authentication': workflow.env_credential_status == 'pending_input',
                    'created_at': workflow.created_at.isoformat() if workflow.created_at else None
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get marketplace test workflows: {str(e)}")
            return []
    
    def cleanup_test_workflows(self) -> int:
        """
        Clean up all test workflows
        Returns number of workflows deleted
        """
        try:
            count = self.db.query(WorkflowCredentialMigrationTest).count()
            self.db.query(WorkflowCredentialMigrationTest).delete()
            self.db.commit()
            
            logger.info(f"Cleaned up {count} test workflows")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup test workflows: {str(e)}")
            self.db.rollback()
            return 0
