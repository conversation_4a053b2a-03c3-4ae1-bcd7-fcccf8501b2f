"""
Workflow authentication service
Handles credential analysis, workflow import with authentication, and credential management
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from sqlalchemy.orm import Session
from sqlalchemy import and_

# Database models
from app.models.workflow_builder.workflow import Workflow

# Credential analysis
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import (
    CredentialSummary,
    CredentialCoverageReport,
    OptimizedImportRequest,
    determine_env_credential_status,
    create_credential_summary_from_dict,
    validate_import_request
)

# External services
from app.services.user_service import UserServiceClient
from app.utils.logger import setup_logger

logger = setup_logger("workflow-auth-service")


class WorkflowAuthService:
    """
    Service for handling workflow authentication requirements
    Implements the optimized single-field approach for credential management
    """
    
    def __init__(self, db_session: Session = None):
        """Initialize the workflow auth service"""
        self.db = db_session
        self.credential_analyzer = OptimizedCredentialAnalyzer()
        self.user_service = UserServiceClient()
        logger.info("WorkflowAuthService initialized")
    
    async def save_workflow_with_credential_analysis(self, workflow_data: Dict[str, Any]) -> Workflow:
        """
        Save workflow with automatic credential analysis
        This is called when a workflow is created or updated
        """
        try:
            logger.info(f"Analyzing credentials for workflow: {workflow_data.get('id', 'unknown')}")
            
            # Validate workflow data
            validation_errors = self.credential_analyzer.validate_workflow_data(workflow_data)
            if validation_errors:
                raise ValueError(f"Invalid workflow data: {', '.join(validation_errors)}")
            
            # Analyze credentials
            credential_summary = self.credential_analyzer.analyze_workflow_optimized(workflow_data)
            
            # Save workflow to database (this would be implemented based on your existing workflow save logic)
            workflow = await self._save_workflow_to_db(workflow_data)
            
            # Add credential analysis to workflow
            workflow.credential_summary = credential_summary.model_dump()
            workflow.env_credential_status = determine_env_credential_status(workflow.credential_summary)
            
            # Commit changes
            self.db.commit()
            
            logger.info(f"Workflow saved with credential analysis: {workflow.id}")
            logger.info(f"Credential status: {workflow.env_credential_status}")
            logger.info(f"Total requirements: {credential_summary.total_requirements}")
            
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to save workflow with credential analysis: {str(e)}")
            if self.db:
                self.db.rollback()
            raise
    
    async def get_workflow_with_auth_summary(self, workflow_id: str) -> Optional[Workflow]:
        """
        Get workflow with authentication summary
        Returns workflow with precomputed credential data
        """
        try:
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            
            if not workflow:
                logger.warning(f"Workflow not found: {workflow_id}")
                return None
            
            logger.info(f"Retrieved workflow with auth summary: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to get workflow auth summary: {str(e)}")
            raise
    
    async def import_workflow_with_credentials(
        self, 
        workflow_id: str, 
        user_id: str, 
        credential_mapping: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """
        Import workflow with credential mapping
        Creates/updates user credentials and imports workflow
        """
        try:
            logger.info(f"Importing workflow {workflow_id} for user {user_id}")
            logger.info(f"Credential mapping: {len(credential_mapping)} credentials")
            
            # Create or update user credentials if mapping provided
            if credential_mapping:
                await self.user_service.create_or_update_credentials(user_id, credential_mapping)
                logger.info(f"Updated {len(credential_mapping)} credentials for user {user_id}")
            
            # Import workflow for user (this would call your existing import logic)
            import_result = await self._import_workflow_to_user(workflow_id, user_id)
            
            logger.info(f"Successfully imported workflow {workflow_id} for user {user_id}")
            return import_result
            
        except Exception as e:
            logger.error(f"Failed to import workflow with credentials: {str(e)}")
            raise
    
    async def analyze_user_credential_coverage(
        self, 
        user_id: str, 
        credential_summary: CredentialSummary
    ) -> CredentialCoverageReport:
        """
        Analyze user's credential coverage against workflow requirements
        """
        try:
            logger.info(f"Analyzing credential coverage for user {user_id}")
            
            # Get user's existing credentials
            user_credentials = await self.user_service.get_user_credentials(user_id)
            
            # Analyze coverage
            total_requirements = len(credential_summary.credential_requirements)
            available_credentials = 0
            detailed_status = []
            
            for req in credential_summary.credential_requirements:
                # Check if user has matching credential
                matching_cred = self._find_matching_credential(req, user_credentials)
                
                if matching_cred:
                    available_credentials += 1
                    status = {
                        "field_name": req.field_name,
                        "status": "available",
                        "credential_id": matching_cred["id"],
                        "credential_name": matching_cred["name"]
                    }
                else:
                    status = {
                        "field_name": req.field_name,
                        "status": "missing",
                        "credential_id": None,
                        "credential_name": None
                    }
                
                detailed_status.append(status)
            
            missing_credentials = total_requirements - available_credentials
            coverage_percentage = (available_credentials / total_requirements * 100) if total_requirements > 0 else 100
            
            coverage_report = CredentialCoverageReport(
                total_requirements=total_requirements,
                available_credentials=available_credentials,
                missing_credentials=missing_credentials,
                coverage_percentage=coverage_percentage,
                detailed_status=detailed_status
            )
            
            logger.info(f"Coverage analysis complete: {coverage_percentage:.1f}% coverage")
            return coverage_report
            
        except Exception as e:
            logger.error(f"Failed to analyze credential coverage: {str(e)}")
            raise
    
    async def reanalyze_workflow_credentials(self, workflow_id: str) -> Workflow:
        """
        Reanalyze workflow credentials (for updates or cache refresh)
        """
        try:
            logger.info(f"Reanalyzing credentials for workflow: {workflow_id}")
            
            # Get existing workflow
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                raise ValueError(f"Workflow not found: {workflow_id}")
            
            # Get workflow data (this would fetch the actual workflow definition)
            workflow_data = await self._get_workflow_data(workflow_id)
            
            # Reanalyze credentials
            credential_summary = self.credential_analyzer.analyze_workflow_optimized(workflow_data)
            
            # Update workflow
            workflow.credential_summary = credential_summary.model_dump()
            workflow.env_credential_status = determine_env_credential_status(workflow.credential_summary)
            
            # Commit changes
            self.db.commit()
            
            logger.info(f"Reanalysis complete for workflow: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to reanalyze workflow credentials: {str(e)}")
            if self.db:
                self.db.rollback()
            raise
    
    async def batch_analyze_workflows(self, workflow_ids: List[str]) -> List[Workflow]:
        """
        Batch analyze multiple workflows for credential requirements
        Useful for marketplace updates or bulk operations
        """
        try:
            logger.info(f"Batch analyzing {len(workflow_ids)} workflows")
            
            # Get workflows that need analysis
            workflows = self.db.query(Workflow).filter(
                and_(
                    Workflow.id.in_(workflow_ids),
                    Workflow.credential_summary.is_(None)  # Only analyze workflows without existing analysis
                )
            ).all()
            
            results = []
            for workflow in workflows:
                try:
                    updated_workflow = await self.reanalyze_workflow_credentials(workflow.id)
                    results.append(updated_workflow)
                except Exception as e:
                    logger.error(f"Failed to analyze workflow {workflow.id}: {str(e)}")
                    # Continue with other workflows
            
            logger.info(f"Batch analysis complete: {len(results)} workflows analyzed")
            return results
            
        except Exception as e:
            logger.error(f"Failed to batch analyze workflows: {str(e)}")
            raise
    
    def _determine_env_credential_status(self, credential_summary: Optional[Dict[str, Any]]) -> str:
        """
        Determine env_credential_status based on credential summary
        Follows MCP pattern: 'pending_input' if credentials needed, 'not_required' if not
        """
        return determine_env_credential_status(credential_summary)
    
    def _find_matching_credential(self, requirement, user_credentials: List[Dict]) -> Optional[Dict]:
        """
        Find matching user credential for a requirement
        Uses fuzzy matching on field names and types
        """
        for cred in user_credentials:
            # Exact field name match
            if cred.get("name", "").lower() == requirement.field_name.lower():
                return cred
            
            # Provider-based matching
            if (requirement.provider_name and 
                requirement.provider_name.lower() in cred.get("name", "").lower()):
                return cred
            
            # Type-based matching
            if (requirement.credential_type in cred.get("type", "") or
                requirement.credential_type in cred.get("name", "").lower()):
                return cred
        
        return None
    
    async def _save_workflow_to_db(self, workflow_data: Dict[str, Any]) -> Workflow:
        """
        Save workflow to database
        This would integrate with your existing workflow save logic
        """
        # This is a placeholder - implement based on your existing workflow save logic
        workflow = Workflow(
            id=workflow_data.get("id"),
            name=workflow_data.get("name", ""),
            description=workflow_data.get("description", ""),
            # Add other fields as needed
        )
        
        self.db.add(workflow)
        self.db.flush()  # Get the ID without committing
        
        return workflow
    
    async def _import_workflow_to_user(self, workflow_id: str, user_id: str) -> Dict[str, Any]:
        """
        Import workflow for user
        This would integrate with your existing workflow import logic
        """
        # This is a placeholder - implement based on your existing import logic
        return {
            "workflow_id": f"imported-{workflow_id}",
            "status": "success",
            "user_id": user_id
        }
    
    async def _get_workflow_data(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get workflow data for analysis
        This would fetch the actual workflow definition
        """
        # This is a placeholder - implement based on your existing workflow data retrieval
        return {
            "id": workflow_id,
            "start_nodes": [],
            "available_nodes": []
        }
