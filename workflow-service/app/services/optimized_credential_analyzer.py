"""
High-performance credential analyzer using pattern matching and caching
Analyzes workflow in single pass and generates precomputed summary
"""

from typing import Dict, List, Any, Set
from collections import defaultdict
import re
import json
from datetime import datetime
from app.models.workflow_builder.credential_analysis import (
    CredentialSummary, 
    CredentialRequirementSpec, 
    ProviderSummary,
    estimate_setup_time_for_type,
    get_provider_display_name,
    get_credential_type_display_name
)


class OptimizedCredentialAnalyzer:
    """
    High-performance credential analyzer using pattern matching and caching
    Analyzes workflow in single pass and generates precomputed summary
    """
    
    def __init__(self):
        # Precompiled regex patterns for faster matching
        self.credential_patterns = {
            'api_key': re.compile(r'(api_key|token|key)$', re.IGNORECASE),
            'oauth': re.compile(r'(oauth|auth_token|access_token)$', re.IGNORECASE),
            'password': re.compile(r'(password|pass|secret)$', re.IGNORECASE)
        }
        
        # Provider detection patterns (compiled once)
        self.provider_patterns = {
            'openai': re.compile(r'(openai|gpt|chatgpt)', re.IGNORECASE),
            'github': re.compile(r'(github|git|gh_)', re.IGNORECASE),
            'google': re.compile(r'(google|gmail|drive)', re.IGNORECASE),
            'slack': re.compile(r'(slack)', re.IGNORECASE),
            'discord': re.compile(r'(discord)', re.IGNORECASE),
            'anthropic': re.compile(r'(anthropic|claude)', re.IGNORECASE),
            'telegram': re.compile(r'(telegram|tg_)', re.IGNORECASE)
        }
    
    def analyze_workflow_optimized(self, workflow_data: Dict) -> CredentialSummary:
        """
        Optimized single-pass analysis of workflow credentials
        Returns precomputed summary for JSONB storage
        """
        # Single pass through all nodes
        credential_map = defaultdict(lambda: {
            'count': 0, 
            'nodes': [], 
            'required': False,
            'provider': 'generic',
            'type': 'api_key',
            'description': ''
        })
        
        all_nodes = (
            workflow_data.get("start_nodes", []) + 
            workflow_data.get("available_nodes", [])
        )
        
        # Vectorized processing of nodes
        for node in all_nodes:
            node_creds = self._extract_node_credentials_fast(node)
            
            for cred in node_creds:
                key = f"{cred['type']}:{cred['field']}"
                credential_map[key]['count'] += 1
                credential_map[key]['nodes'].append(node.get('id', 'unknown'))
                credential_map[key]['required'] |= cred['required']
                credential_map[key]['provider'] = cred['provider']
                credential_map[key]['type'] = cred['type']
                credential_map[key]['description'] = cred.get('description', '')
        
        # Generate optimized summary
        return self._generate_summary_optimized(credential_map)
    
    def _extract_node_credentials_fast(self, node: Dict) -> List[Dict]:
        """Fast credential extraction using precompiled patterns"""
        credentials = []
        inputs = node.get("inputs", {})
        
        for field_name, input_config in inputs.items():
            if not isinstance(input_config, dict):
                continue
                
            input_type = input_config.get("input_type", "")
            
            # Skip non-credential inputs
            if input_type not in ["credential", "oauth", "password", "api_key"]:
                # Check if field name suggests it's a credential
                is_credential = any(
                    pattern.search(field_name) 
                    for pattern in self.credential_patterns.values()
                )
                if not is_credential:
                    continue
            
            # Determine credential type
            cred_type = self._determine_credential_type(field_name, input_type)
            
            # Detect provider
            provider = self._detect_provider_fast(field_name, input_config)
            
            # Get description
            description = input_config.get('description', '') or self._generate_description(
                provider, cred_type, field_name
            )
            
            credentials.append({
                'type': cred_type,
                'field': field_name,
                'required': input_config.get('required', True),
                'provider': provider,
                'description': description
            })
        
        return credentials
    
    def _determine_credential_type(self, field_name: str, input_type: str) -> str:
        """Determine credential type from field name and input type"""
        # Direct mapping from input_type
        if input_type == "oauth":
            return "oauth"
        elif input_type == "password":
            return "password"
        elif input_type in ["credential", "api_key"]:
            return "api_key"
        
        # Pattern matching on field name
        field_lower = field_name.lower()
        
        if any(pattern in field_lower for pattern in ['oauth', 'auth_token', 'access_token']):
            return "oauth"
        elif any(pattern in field_lower for pattern in ['password', 'pass', 'secret']):
            return "password"
        else:
            return "api_key"  # Default
    
    def _detect_provider_fast(self, field_name: str, config: Dict) -> str:
        """Fast provider detection using precompiled patterns"""
        field_lower = field_name.lower()
        
        for provider, pattern in self.provider_patterns.items():
            if pattern.search(field_lower):
                return provider
        
        return 'generic'
    
    def _generate_description(self, provider: str, cred_type: str, field_name: str) -> str:
        """Generate human-readable description for credential"""
        provider_name = get_provider_display_name(provider)
        type_name = get_credential_type_display_name(cred_type)
        
        if provider != 'generic':
            return f"{provider_name} {type_name.lower()} for authentication"
        else:
            return f"{type_name} for {field_name}"
    
    def _generate_summary_optimized(self, credential_map: Dict) -> CredentialSummary:
        """Generate optimized credential summary for JSONB storage"""
        # Group by provider
        by_provider = defaultdict(lambda: {
            'count': 0,
            'types': set(),
            'required': False,
            'fields': []
        })
        
        credential_requirements = []
        
        for key, data in credential_map.items():
            cred_type, field_name = key.split(':', 1)
            provider = data['provider']
            
            # Add to provider summary
            by_provider[provider]['count'] += data['count']
            by_provider[provider]['types'].add(cred_type)
            by_provider[provider]['required'] |= data['required']
            by_provider[provider]['fields'].append(field_name)
            
            # Add to detailed requirements
            credential_requirements.append(CredentialRequirementSpec(
                credential_type=cred_type,
                field_name=field_name,
                is_required=data['required'],
                component_count=data['count'],
                provider_name=provider,
                description=data['description'] or self._generate_description(
                    provider, cred_type, field_name
                )
            ))
        
        # Convert sets to lists for JSON serialization
        provider_summary = {}
        for provider, data in by_provider.items():
            provider_summary[provider] = ProviderSummary(
                count=data['count'],
                types=list(data['types']),
                required=data['required'],
                fields=data['fields']
            )
        
        # Calculate setup time
        setup_time = self._estimate_setup_time(credential_requirements)
        
        return CredentialSummary(
            total_requirements=len(credential_requirements),
            by_provider=provider_summary,
            estimated_setup_time=setup_time,
            last_analyzed=datetime.utcnow(),
            credential_requirements=credential_requirements
        )
    
    def _estimate_setup_time(self, requirements: List[CredentialRequirementSpec]) -> int:
        """Estimate setup time in minutes based on credential types"""
        if not requirements:
            return 0
        
        setup_time = 0
        for req in requirements:
            setup_time += estimate_setup_time_for_type(req.credential_type)
        
        return min(setup_time, 60)  # Cap at 60 minutes
    
    def get_analysis_cache_key(self, workflow_data: Dict) -> str:
        """
        Generate cache key for workflow analysis
        Can be used for caching analysis results
        """
        # Create a hash of the workflow structure that affects credentials
        import hashlib
        
        # Extract only credential-relevant data
        relevant_data = {
            "start_nodes": [
                {
                    "id": node.get("id"),
                    "type": node.get("type"),
                    "inputs": {
                        k: v for k, v in node.get("inputs", {}).items()
                        if isinstance(v, dict) and v.get("input_type") in [
                            "credential", "oauth", "password", "api_key"
                        ]
                    }
                }
                for node in workflow_data.get("start_nodes", [])
            ],
            "available_nodes": [
                {
                    "id": node.get("id"),
                    "type": node.get("type"),
                    "inputs": {
                        k: v for k, v in node.get("inputs", {}).items()
                        if isinstance(v, dict) and v.get("input_type") in [
                            "credential", "oauth", "password", "api_key"
                        ]
                    }
                }
                for node in workflow_data.get("available_nodes", [])
            ]
        }
        
        # Create hash
        data_str = json.dumps(relevant_data, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def validate_workflow_data(self, workflow_data: Dict) -> List[str]:
        """
        Validate workflow data structure
        Returns list of validation errors
        """
        errors = []
        
        if not isinstance(workflow_data, dict):
            errors.append("Workflow data must be a dictionary")
            return errors
        
        # Check required fields
        if "start_nodes" not in workflow_data and "available_nodes" not in workflow_data:
            errors.append("Workflow must have either start_nodes or available_nodes")
        
        # Validate node structure
        for node_list_name in ["start_nodes", "available_nodes"]:
            nodes = workflow_data.get(node_list_name, [])
            if not isinstance(nodes, list):
                errors.append(f"{node_list_name} must be a list")
                continue
            
            for i, node in enumerate(nodes):
                if not isinstance(node, dict):
                    errors.append(f"{node_list_name}[{i}] must be a dictionary")
                    continue
                
                if "inputs" in node and not isinstance(node["inputs"], dict):
                    errors.append(f"{node_list_name}[{i}].inputs must be a dictionary")
        
        return errors
