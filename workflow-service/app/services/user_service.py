"""
User service client for credential management
Integrates with existing user-service for credential operations
"""

import httpx
import asyncio
from typing import Dict, List, Any, Optional
import logging
from app.utils.logger import setup_logger

logger = setup_logger("user-service-client")


class UserServiceClient:
    """
    Client for interacting with user-service for credential operations
    Leverages existing user-service credential infrastructure
    """
    
    def __init__(self, base_url: str = None):
        """Initialize user service client"""
        # Use environment variable or default
        self.base_url = base_url or "http://user-service:8000"
        self.timeout = 30.0
        logger.info(f"UserServiceClient initialized with base_url: {self.base_url}")
    
    async def get_user_credentials(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all credentials for a user
        Returns list of user's existing credentials
        """
        try:
            logger.info(f"Getting credentials for user: {user_id}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/users/{user_id}/credentials",
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    credentials = response.json().get("credentials", [])
                    logger.info(f"Retrieved {len(credentials)} credentials for user {user_id}")
                    return credentials
                elif response.status_code == 404:
                    logger.info(f"No credentials found for user {user_id}")
                    return []
                else:
                    logger.error(f"Failed to get user credentials: {response.status_code} - {response.text}")
                    response.raise_for_status()
                    
        except httpx.TimeoutException:
            logger.error(f"Timeout getting credentials for user {user_id}")
            raise Exception("Timeout connecting to user service")
        except Exception as e:
            logger.error(f"Error getting user credentials: {str(e)}")
            raise
    
    async def create_or_update_credentials(
        self, 
        user_id: str, 
        credential_mapping: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """
        Create or update user credentials based on mapping
        credential_mapping format: {field_name: {credential_id, credential_name}}
        """
        try:
            logger.info(f"Creating/updating {len(credential_mapping)} credentials for user {user_id}")
            
            results = []
            
            for field_name, mapping in credential_mapping.items():
                credential_id = mapping.get("credential_id")
                credential_name = mapping.get("credential_name")
                
                if credential_id == "new":
                    # Create new credential
                    result = await self._create_credential(user_id, field_name, credential_name)
                else:
                    # Update existing credential
                    result = await self._update_credential(user_id, credential_id, credential_name)
                
                results.append(result)
            
            logger.info(f"Successfully processed {len(results)} credentials for user {user_id}")
            return {
                "user_id": user_id,
                "processed_count": len(results),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error creating/updating credentials: {str(e)}")
            raise
    
    async def _create_credential(self, user_id: str, field_name: str, credential_value: str) -> Dict[str, Any]:
        """Create a new credential for user"""
        try:
            logger.info(f"Creating new credential {field_name} for user {user_id}")
            
            # Determine credential type from field name
            credential_type = self._determine_credential_type(field_name)
            
            payload = {
                "name": field_name,
                "type": credential_type,
                "value": credential_value,
                "description": f"Auto-created from workflow import: {field_name}"
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/users/{user_id}/credentials",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code in [200, 201]:
                    result = response.json()
                    logger.info(f"Created credential {field_name} for user {user_id}")
                    return result
                else:
                    logger.error(f"Failed to create credential: {response.status_code} - {response.text}")
                    response.raise_for_status()
                    
        except Exception as e:
            logger.error(f"Error creating credential {field_name}: {str(e)}")
            raise
    
    async def _update_credential(self, user_id: str, credential_id: str, credential_name: str) -> Dict[str, Any]:
        """Update an existing credential"""
        try:
            logger.info(f"Updating credential {credential_id} for user {user_id}")
            
            payload = {
                "name": credential_name
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.put(
                    f"{self.base_url}/api/v1/users/{user_id}/credentials/{credential_id}",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"Updated credential {credential_id} for user {user_id}")
                    return result
                else:
                    logger.error(f"Failed to update credential: {response.status_code} - {response.text}")
                    response.raise_for_status()
                    
        except Exception as e:
            logger.error(f"Error updating credential {credential_id}: {str(e)}")
            raise
    
    async def batch_check_user_coverage(
        self, 
        user_id: str, 
        credential_summary: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Batch check user's credential coverage against workflow requirements
        Optimized single query approach
        """
        try:
            logger.info(f"Checking credential coverage for user {user_id}")
            
            # Get user credentials
            user_credentials = await self.get_user_credentials(user_id)
            
            # Analyze coverage
            requirements = credential_summary.get("credential_requirements", [])
            total_requirements = len(requirements)
            available_credentials = 0
            detailed_status = []
            
            for req in requirements:
                field_name = req.get("field_name", "")
                
                # Find matching credential
                matching_cred = self._find_matching_credential(req, user_credentials)
                
                if matching_cred:
                    available_credentials += 1
                    status = {
                        "field_name": field_name,
                        "status": "available",
                        "credential_id": matching_cred["id"],
                        "credential_name": matching_cred["name"]
                    }
                else:
                    status = {
                        "field_name": field_name,
                        "status": "missing",
                        "credential_id": None,
                        "credential_name": None
                    }
                
                detailed_status.append(status)
            
            missing_credentials = total_requirements - available_credentials
            coverage_percentage = (available_credentials / total_requirements * 100) if total_requirements > 0 else 100
            
            coverage_report = {
                "total_requirements": total_requirements,
                "available_credentials": available_credentials,
                "missing_credentials": missing_credentials,
                "coverage_percentage": coverage_percentage,
                "detailed_status": detailed_status
            }
            
            logger.info(f"Coverage check complete: {coverage_percentage:.1f}% coverage")
            return coverage_report
            
        except Exception as e:
            logger.error(f"Error checking credential coverage: {str(e)}")
            raise
    
    def _determine_credential_type(self, field_name: str) -> str:
        """Determine credential type from field name"""
        field_lower = field_name.lower()
        
        if "oauth" in field_lower or "auth_token" in field_lower:
            return "oauth"
        elif "password" in field_lower or "pass" in field_lower:
            return "password"
        else:
            return "api_key"  # Default
    
    def _find_matching_credential(self, requirement: Dict, user_credentials: List[Dict]) -> Optional[Dict]:
        """
        Find matching user credential for a requirement
        Uses fuzzy matching on field names and types
        """
        field_name = requirement.get("field_name", "")
        provider_name = requirement.get("provider_name", "")
        credential_type = requirement.get("credential_type", "")
        
        for cred in user_credentials:
            cred_name = cred.get("name", "").lower()
            cred_type = cred.get("type", "").lower()
            
            # Exact field name match
            if cred_name == field_name.lower():
                return cred
            
            # Provider-based matching
            if provider_name and provider_name.lower() in cred_name:
                return cred
            
            # Type-based matching
            if credential_type and (credential_type in cred_type or credential_type in cred_name):
                return cred
            
            # Partial field name matching
            if field_name.lower() in cred_name or cred_name in field_name.lower():
                return cred
        
        return None
    
    async def delete_credential(self, user_id: str, credential_id: str) -> bool:
        """Delete a user credential"""
        try:
            logger.info(f"Deleting credential {credential_id} for user {user_id}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.delete(
                    f"{self.base_url}/api/v1/users/{user_id}/credentials/{credential_id}",
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code in [200, 204]:
                    logger.info(f"Deleted credential {credential_id} for user {user_id}")
                    return True
                else:
                    logger.error(f"Failed to delete credential: {response.status_code} - {response.text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error deleting credential {credential_id}: {str(e)}")
            return False
    
    async def health_check(self) -> bool:
        """Check if user service is healthy"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/health")
                return response.status_code == 200
        except:
            return False
