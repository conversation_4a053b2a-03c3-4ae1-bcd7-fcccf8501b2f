"""
Test suite for credential analysis models
Following TDD approach: Write tests first, then implement
"""

import pytest
from datetime import datetime
from typing import Dict, List
import json

# Import the models we'll implement
from app.models.workflow_builder.credential_analysis import (
    CredentialRequirementSpec,
    ProviderSummary,
    CredentialSummary,
    OptimizedImportRequest,
    CredentialCoverageReport
)


class TestCredentialRequirementSpec:
    """Test cases for CredentialRequirementSpec model"""
    
    def test_create_valid_credential_requirement(self):
        """Test creating a valid credential requirement"""
        # Act
        cred = CredentialRequirementSpec(
            credential_type="api_key",
            field_name="openai_api_key",
            is_required=True,
            component_count=5,
            description="OpenAI API key for AI components",
            provider_name="openai"
        )
        
        # Assert
        assert cred.credential_type == "api_key"
        assert cred.field_name == "openai_api_key"
        assert cred.is_required == True
        assert cred.component_count == 5
        assert cred.description == "OpenAI API key for AI components"
        assert cred.provider_name == "openai"

    def test_credential_requirement_defaults(self):
        """Test default values for credential requirement"""
        # Act
        cred = CredentialRequirementSpec(
            credential_type="oauth",
            field_name="google_oauth"
        )
        
        # Assert
        assert cred.is_required == True  # Default
        assert cred.component_count == 1  # Default
        assert cred.description is None  # Optional
        assert cred.provider_name is None  # Optional

    def test_credential_requirement_serialization(self):
        """Test JSON serialization of credential requirement"""
        # Arrange
        cred = CredentialRequirementSpec(
            credential_type="api_key",
            field_name="github_token",
            is_required=True,
            component_count=3,
            description="GitHub Personal Access Token",
            provider_name="github"
        )
        
        # Act
        json_data = cred.model_dump()
        
        # Assert
        expected = {
            "credential_type": "api_key",
            "field_name": "github_token",
            "is_required": True,
            "component_count": 3,
            "description": "GitHub Personal Access Token",
            "provider_name": "github"
        }
        assert json_data == expected


class TestProviderSummary:
    """Test cases for ProviderSummary model"""
    
    def test_create_provider_summary(self):
        """Test creating a provider summary"""
        # Act
        summary = ProviderSummary(
            count=15,
            types=["api_key", "oauth"],
            required=True,
            fields=["openai_api_key", "openai_org_id"]
        )
        
        # Assert
        assert summary.count == 15
        assert summary.types == ["api_key", "oauth"]
        assert summary.required == True
        assert summary.fields == ["openai_api_key", "openai_org_id"]

    def test_provider_summary_serialization(self):
        """Test JSON serialization of provider summary"""
        # Arrange
        summary = ProviderSummary(
            count=8,
            types=["oauth"],
            required=False,
            fields=["google_drive_oauth"]
        )
        
        # Act
        json_data = summary.model_dump()
        
        # Assert
        expected = {
            "count": 8,
            "types": ["oauth"],
            "required": False,
            "fields": ["google_drive_oauth"]
        }
        assert json_data == expected


class TestCredentialSummary:
    """Test cases for CredentialSummary model"""
    
    @pytest.fixture
    def sample_credential_requirements(self):
        """Sample credential requirements for testing"""
        return [
            CredentialRequirementSpec(
                credential_type="api_key",
                field_name="openai_api_key",
                is_required=True,
                component_count=15,
                provider_name="openai"
            ),
            CredentialRequirementSpec(
                credential_type="oauth",
                field_name="google_oauth",
                is_required=True,
                component_count=3,
                provider_name="google"
            )
        ]
    
    @pytest.fixture
    def sample_provider_summary(self):
        """Sample provider summary for testing"""
        return {
            "openai": ProviderSummary(
                count=15,
                types=["api_key"],
                required=True,
                fields=["openai_api_key"]
            ),
            "google": ProviderSummary(
                count=3,
                types=["oauth"],
                required=True,
                fields=["google_oauth"]
            )
        }

    def test_create_credential_summary(self, sample_credential_requirements, sample_provider_summary):
        """Test creating a credential summary"""
        # Act
        summary = CredentialSummary(
            total_requirements=2,
            by_provider=sample_provider_summary,
            estimated_setup_time=18,
            credential_requirements=sample_credential_requirements
        )

        # Assert
        assert summary.total_requirements == 2
        assert len(summary.by_provider) == 2
        assert summary.estimated_setup_time == 18
        assert len(summary.credential_requirements) == 2
        assert isinstance(summary.last_analyzed, datetime)

    def test_requires_authentication_property_true(self, sample_credential_requirements, sample_provider_summary):
        """Test requires_authentication property returns True when credentials exist"""
        # Arrange
        summary = CredentialSummary(
            total_requirements=2,
            by_provider=sample_provider_summary,
            estimated_setup_time=18,
            credential_requirements=sample_credential_requirements
        )
        
        # Act & Assert
        assert summary.requires_authentication == True

    def test_requires_authentication_property_false(self):
        """Test requires_authentication property returns False when no credentials"""
        # Arrange
        summary = CredentialSummary(
            total_requirements=0,
            by_provider={},
            estimated_setup_time=0,
            credential_requirements=[]
        )
        
        # Act & Assert
        assert summary.requires_authentication == False

    def test_credential_summary_serialization(self, sample_credential_requirements, sample_provider_summary):
        """Test JSON serialization of credential summary"""
        # Arrange
        summary = CredentialSummary(
            total_requirements=2,
            by_provider=sample_provider_summary,
            estimated_setup_time=18,
            credential_requirements=sample_credential_requirements
        )

        # Act
        json_data = summary.model_dump()

        # Assert
        assert json_data["total_requirements"] == 2
        assert "by_provider" in json_data
        assert json_data["estimated_setup_time"] == 18
        assert "credential_requirements" in json_data
        assert "last_analyzed" in json_data

    def test_credential_summary_defaults(self):
        """Test default values for credential summary"""
        # Act
        summary = CredentialSummary(
            total_requirements=0,
            by_provider={},
            estimated_setup_time=0
        )
        
        # Assert
        assert isinstance(summary.last_analyzed, datetime)
        assert summary.credential_requirements == []  # Default


class TestOptimizedImportRequest:
    """Test cases for OptimizedImportRequest model"""
    
    def test_create_analyze_request(self):
        """Test creating an analyze request"""
        # Act
        request = OptimizedImportRequest(action="analyze")
        
        # Assert
        assert request.action == "analyze"
        assert request.credential_mapping is None

    def test_create_import_request_with_mapping(self):
        """Test creating an import request with credential mapping"""
        # Arrange
        mapping = {
            "openai_api_key": {
                "credential_id": "cred_123",
                "credential_name": "My OpenAI Key"
            },
            "github_token": {
                "credential_id": "cred_456",
                "credential_name": "GitHub Token"
            }
        }
        
        # Act
        request = OptimizedImportRequest(
            action="import",
            credential_mapping=mapping
        )
        
        # Assert
        assert request.action == "import"
        assert request.credential_mapping == mapping
        assert len(request.credential_mapping) == 2

    def test_import_request_serialization(self):
        """Test JSON serialization of import request"""
        # Arrange
        mapping = {
            "api_key": {
                "credential_id": "cred_789",
                "credential_name": "Test Key"
            }
        }
        request = OptimizedImportRequest(
            action="import",
            credential_mapping=mapping
        )
        
        # Act
        json_data = request.model_dump()
        
        # Assert
        expected = {
            "action": "import",
            "credential_mapping": {
                "api_key": {
                    "credential_id": "cred_789",
                    "credential_name": "Test Key"
                }
            }
        }
        assert json_data == expected


class TestCredentialCoverageReport:
    """Test cases for CredentialCoverageReport model"""
    
    def test_create_coverage_report(self):
        """Test creating a credential coverage report"""
        # Arrange
        detailed_status = [
            {
                "field_name": "openai_api_key",
                "status": "available",
                "credential_id": "cred_123"
            },
            {
                "field_name": "github_token",
                "status": "missing",
                "credential_id": None
            }
        ]
        
        # Act
        report = CredentialCoverageReport(
            total_requirements=2,
            available_credentials=1,
            missing_credentials=1,
            coverage_percentage=50.0,
            detailed_status=detailed_status
        )
        
        # Assert
        assert report.total_requirements == 2
        assert report.available_credentials == 1
        assert report.missing_credentials == 1
        assert report.coverage_percentage == 50.0
        assert len(report.detailed_status) == 2

    def test_coverage_report_serialization(self):
        """Test JSON serialization of coverage report"""
        # Arrange
        report = CredentialCoverageReport(
            total_requirements=3,
            available_credentials=2,
            missing_credentials=1,
            coverage_percentage=66.67,
            detailed_status=[]
        )
        
        # Act
        json_data = report.model_dump()
        
        # Assert
        expected = {
            "total_requirements": 3,
            "available_credentials": 2,
            "missing_credentials": 1,
            "coverage_percentage": 66.67,
            "detailed_status": []
        }
        assert json_data == expected

    def test_full_coverage_report(self):
        """Test coverage report with 100% coverage"""
        # Act
        report = CredentialCoverageReport(
            total_requirements=2,
            available_credentials=2,
            missing_credentials=0,
            coverage_percentage=100.0,
            detailed_status=[]
        )
        
        # Assert
        assert report.coverage_percentage == 100.0
        assert report.missing_credentials == 0

    def test_zero_coverage_report(self):
        """Test coverage report with 0% coverage"""
        # Act
        report = CredentialCoverageReport(
            total_requirements=3,
            available_credentials=0,
            missing_credentials=3,
            coverage_percentage=0.0,
            detailed_status=[]
        )
        
        # Assert
        assert report.coverage_percentage == 0.0
        assert report.available_credentials == 0
