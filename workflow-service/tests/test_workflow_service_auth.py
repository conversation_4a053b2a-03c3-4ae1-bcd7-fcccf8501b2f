"""
Test suite for workflow service authentication functionality
Tests the integrated credential analysis in workflow saving process
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import Dict, Any

# Import the actual services
from app.services.workflow_auth_service import WorkflowAuthService
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import CredentialSummary, determine_env_credential_status
from app.services.workflow_auth_analyzer import analyze_and_update_workflow_auth


class TestWorkflowServiceAuth:
    """Test cases for workflow service authentication functionality"""

    @pytest.fixture
    def workflow_auth_service(self):
        """Create workflow auth service instance for testing"""
        return WorkflowAuthService()

    @pytest.fixture
    def workflow_service(self):
        """Create workflow service instance for testing"""
        return WorkflowAuthService()
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        return Mock()
    
    @pytest.fixture
    def sample_workflow_data(self):
        """Sample workflow data for testing"""
        return {
            "id": "workflow-123",
            "name": "AI Content Pipeline",
            "description": "Automated content generation workflow",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "openai_chat",
                    "inputs": {
                        "openai_api_key": {
                            "input_type": "credential",
                            "required": True,
                            "description": "OpenAI API key"
                        }
                    }
                }
            ],
            "available_nodes": []
        }
    
    @pytest.fixture
    def mock_credential_summary(self):
        """Mock credential summary for testing"""
        return {
            "total_requirements": 1,
            "by_provider": {
                "openai": {
                    "count": 1,
                    "types": ["api_key"],
                    "required": True,
                    "fields": ["openai_api_key"]
                }
            },
            "estimated_setup_time": 5,
            "analysis_version": 2,
            "last_analyzed": "2025-01-28T10:00:00Z",
            "credential_requirements": [
                {
                    "credential_type": "api_key",
                    "field_name": "openai_api_key",
                    "is_required": True,
                    "component_count": 1,
                    "provider_name": "openai"
                }
            ]
        }

    def test_integrated_workflow_auth_analysis(
        self,
        sample_workflow_data
    ):
        """Test the integrated workflow authentication analysis function"""
        # Act
        result = analyze_and_update_workflow_auth(sample_workflow_data)

        # Assert
        assert "available_nodes" in result
        assert "credential_summary" in result
        assert "env_credential_status" in result

        # Check that credential analysis was performed
        credential_summary = result["credential_summary"]
        assert credential_summary is not None
        assert "total_requirements" in credential_summary
        assert "credential_requirements" in credential_summary

        # Check status determination
        env_status = result["env_credential_status"]
        assert env_status in ["not_required", "pending_input"]

    @patch('app.services.optimized_credential_analyzer.OptimizedCredentialAnalyzer')
    async def test_save_workflow_no_credentials_required(
        self,
        mock_analyzer_class,
        workflow_service,
        mock_db_session
    ):
        """Test saving workflow that doesn't require credentials"""
        # Arrange
        workflow_data_no_auth = {
            "id": "workflow-456",
            "name": "Simple Text Processor",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "text_processor",
                    "inputs": {
                        "input_text": {
                            "input_type": "string",
                            "required": True
                        }
                    }
                }
            ],
            "available_nodes": []
        }
        
        mock_analyzer = Mock()
        mock_analyzer.analyze_workflow_optimized.return_value = {
            "total_requirements": 0,
            "by_provider": {},
            "estimated_setup_time": 0,
            "analysis_version": 2,
            "credential_requirements": []
        }
        mock_analyzer_class.return_value = mock_analyzer
        
        workflow_service.db = mock_db_session
        workflow_service.credential_analyzer = mock_analyzer
        
        mock_workflow = Mock()
        workflow_service._save_workflow_to_db = AsyncMock(return_value=mock_workflow)
        
        # Act
        result = await workflow_service.save_workflow_with_credential_analysis(workflow_data_no_auth)
        
        # Assert
        assert result.env_credential_status == "not_required"
        assert result.credential_summary["total_requirements"] == 0

    async def test_get_workflow_with_auth_summary(self, workflow_service, mock_db_session):
        """Test getting workflow with authentication summary"""
        # Arrange
        mock_workflow = Mock()
        mock_workflow.id = "workflow-123"
        mock_workflow.credential_summary = {"total_requirements": 1}
        mock_workflow.env_credential_status = "pending_input"
        
        workflow_service.db = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_workflow
        
        # Act
        result = await workflow_service.get_workflow_with_auth_summary("workflow-123")
        
        # Assert
        assert result == mock_workflow
        assert result.credential_summary["total_requirements"] == 1
        assert result.env_credential_status == "pending_input"

    async def test_get_workflow_with_auth_summary_not_found(self, workflow_service, mock_db_session):
        """Test getting non-existent workflow"""
        # Arrange
        workflow_service.db = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # Act
        result = await workflow_service.get_workflow_with_auth_summary("non-existent")
        
        # Assert
        assert result is None

    @patch('app.services.user_service.UserServiceClient')
    async def test_import_workflow_with_credentials(
        self,
        mock_user_service_class,
        workflow_service,
        mock_db_session
    ):
        """Test importing workflow with credential mapping"""
        # Arrange
        workflow_id = "workflow-123"
        user_id = "user-456"
        credential_mapping = {
            "openai_api_key": {
                "credential_id": "cred_123",
                "credential_name": "My OpenAI Key"
            }
        }
        
        mock_user_service = Mock()
        mock_user_service.create_or_update_credentials = AsyncMock()
        mock_user_service_class.return_value = mock_user_service
        
        workflow_service.db = mock_db_session
        workflow_service._import_workflow_to_user = AsyncMock(return_value={
            "workflow_id": "imported-workflow-789",
            "status": "success"
        })
        
        # Act
        result = await workflow_service.import_workflow_with_credentials(
            workflow_id, user_id, credential_mapping
        )
        
        # Assert
        mock_user_service.create_or_update_credentials.assert_called_once_with(
            user_id, credential_mapping
        )
        workflow_service._import_workflow_to_user.assert_called_once_with(
            workflow_id, user_id
        )
        assert result["workflow_id"] == "imported-workflow-789"

    async def test_import_workflow_with_credentials_empty_mapping(
        self,
        workflow_service,
        mock_db_session
    ):
        """Test importing workflow with empty credential mapping"""
        # Arrange
        workflow_id = "workflow-123"
        user_id = "user-456"
        credential_mapping = {}
        
        workflow_service.db = mock_db_session
        workflow_service._import_workflow_to_user = AsyncMock(return_value={
            "workflow_id": "imported-workflow-789",
            "status": "success"
        })
        
        # Act
        result = await workflow_service.import_workflow_with_credentials(
            workflow_id, user_id, credential_mapping
        )
        
        # Assert
        # Should still import workflow even with empty mapping
        workflow_service._import_workflow_to_user.assert_called_once_with(
            workflow_id, user_id
        )
        assert result["workflow_id"] == "imported-workflow-789"

    def test_determine_env_credential_status_with_credentials(self):
        """Test determining credential status when credentials are required"""
        # Arrange
        credential_summary = {
            "credential_requirements": [
                {
                    "credential_type": "api_key",
                    "field_name": "openai_api_key"
                }
            ]
        }

        # Act
        status = determine_env_credential_status(credential_summary)

        # Assert
        assert status == "pending_input"

    def test_determine_env_credential_status_no_credentials(self):
        """Test determining credential status when no credentials are required"""
        # Arrange
        credential_summary = {
            "credential_requirements": []
        }

        # Act
        status = determine_env_credential_status(credential_summary)

        # Assert
        assert status == "not_required"

    def test_determine_env_credential_status_none_summary(self):
        """Test determining credential status with None summary"""
        # Act
        status = determine_env_credential_status(None)

        # Assert
        assert status == "not_required"

    @patch('app.services.optimized_credential_analyzer.OptimizedCredentialAnalyzer')
    async def test_reanalyze_workflow_credentials(
        self,
        mock_analyzer_class,
        workflow_service,
        sample_workflow_data,
        mock_credential_summary,
        mock_db_session
    ):
        """Test reanalyzing workflow credentials (for updates)"""
        # Arrange
        mock_analyzer = Mock()
        mock_analyzer.analyze_workflow_optimized.return_value = mock_credential_summary
        mock_analyzer_class.return_value = mock_analyzer
        
        workflow_service.db = mock_db_session
        workflow_service.credential_analyzer = mock_analyzer
        
        mock_workflow = Mock()
        mock_workflow.id = "workflow-123"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_workflow
        
        # Act
        result = await workflow_service.reanalyze_workflow_credentials("workflow-123")
        
        # Assert
        mock_analyzer.analyze_workflow_optimized.assert_called_once()
        assert result.credential_summary == mock_credential_summary
        assert result.env_credential_status == "pending_input"
        mock_db_session.commit.assert_called_once()

    async def test_reanalyze_workflow_credentials_not_found(
        self,
        workflow_service,
        mock_db_session
    ):
        """Test reanalyzing credentials for non-existent workflow"""
        # Arrange
        workflow_service.db = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Workflow not found"):
            await workflow_service.reanalyze_workflow_credentials("non-existent")

    @patch('app.services.optimized_credential_analyzer.OptimizedCredentialAnalyzer')
    async def test_save_workflow_analysis_error_handling(
        self,
        mock_analyzer_class,
        workflow_service,
        sample_workflow_data,
        mock_db_session
    ):
        """Test error handling during credential analysis"""
        # Arrange
        mock_analyzer = Mock()
        mock_analyzer.analyze_workflow_optimized.side_effect = Exception("Analysis failed")
        mock_analyzer_class.return_value = mock_analyzer
        
        workflow_service.db = mock_db_session
        workflow_service.credential_analyzer = mock_analyzer
        
        # Act & Assert
        with pytest.raises(Exception, match="Analysis failed"):
            await workflow_service.save_workflow_with_credential_analysis(sample_workflow_data)

    async def test_batch_analyze_workflows(self, workflow_service, mock_db_session):
        """Test batch analysis of multiple workflows"""
        # Arrange
        workflow_ids = ["workflow-1", "workflow-2", "workflow-3"]
        
        mock_workflows = []
        for i, wf_id in enumerate(workflow_ids):
            mock_wf = Mock()
            mock_wf.id = wf_id
            mock_wf.credential_summary = None  # Needs analysis
            mock_workflows.append(mock_wf)
        
        workflow_service.db = mock_db_session
        mock_db_session.query.return_value.filter.return_value.all.return_value = mock_workflows
        
        workflow_service.reanalyze_workflow_credentials = AsyncMock()
        
        # Act
        results = await workflow_service.batch_analyze_workflows(workflow_ids)
        
        # Assert
        assert len(results) == 3
        assert workflow_service.reanalyze_workflow_credentials.call_count == 3
