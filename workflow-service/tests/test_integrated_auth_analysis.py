"""
Test suite for integrated workflow authentication analysis
Tests the actual implementation that's integrated into workflow saving
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any

# Import the actual integrated functions
from app.services.workflow_auth_analyzer import analyze_and_update_workflow_auth
from app.models.workflow_builder.credential_analysis import determine_env_credential_status


class TestIntegratedAuthAnalysis:
    """Test cases for integrated authentication analysis"""
    
    @pytest.fixture
    def sample_workflow_with_auth(self):
        """Sample workflow data that requires authentication"""
        return {
            "id": "workflow-123",
            "name": "AI Content Pipeline",
            "description": "Automated content generation workflow",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "openai_chat",
                    "inputs": {
                        "openai_api_key": {
                            "input_type": "credential",
                            "required": True,
                            "description": "OpenAI API key"
                        },
                        "query": {
                            "input_type": "string",
                            "required": True
                        }
                    }
                }
            ],
            "available_nodes": []
        }
    
    @pytest.fixture
    def sample_workflow_no_auth(self):
        """Sample workflow data that doesn't require authentication"""
        return {
            "id": "workflow-456",
            "name": "Simple Text Processor",
            "description": "Basic text processing workflow",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "text_processor",
                    "inputs": {
                        "input_text": {
                            "input_type": "string",
                            "required": True
                        }
                    }
                }
            ],
            "available_nodes": []
        }

    def test_analyze_workflow_with_authentication_requirements(self, sample_workflow_with_auth):
        """Test analyzing workflow that requires authentication"""
        # Act
        result = analyze_and_update_workflow_auth(sample_workflow_with_auth)
        
        # Assert
        assert "available_nodes" in result
        assert "credential_summary" in result
        assert "env_credential_status" in result
        
        # Check credential analysis results
        credential_summary = result["credential_summary"]
        assert credential_summary is not None
        assert credential_summary["total_requirements"] > 0
        assert len(credential_summary["credential_requirements"]) > 0
        
        # Check that OpenAI requirement was detected
        openai_req = next(
            (req for req in credential_summary["credential_requirements"] 
             if req["field_name"] == "openai_api_key"), 
            None
        )
        assert openai_req is not None
        assert openai_req["credential_type"] == "api_key"
        assert openai_req["provider_name"] == "openai"
        
        # Check status
        assert result["env_credential_status"] == "pending_input"

    def test_analyze_workflow_without_authentication_requirements(self, sample_workflow_no_auth):
        """Test analyzing workflow that doesn't require authentication"""
        # Act
        result = analyze_and_update_workflow_auth(sample_workflow_no_auth)
        
        # Assert
        assert "available_nodes" in result
        assert "credential_summary" in result
        assert "env_credential_status" in result
        
        # Check credential analysis results
        credential_summary = result["credential_summary"]
        assert credential_summary is not None
        assert credential_summary["total_requirements"] == 0
        assert len(credential_summary["credential_requirements"]) == 0
        
        # Check status
        assert result["env_credential_status"] == "not_required"

    def test_determine_env_credential_status_with_requirements(self):
        """Test credential status determination with requirements"""
        # Arrange
        credential_summary = {
            "credential_requirements": [
                {
                    "credential_type": "api_key",
                    "field_name": "openai_api_key",
                    "is_required": True
                }
            ]
        }
        
        # Act
        status = determine_env_credential_status(credential_summary)
        
        # Assert
        assert status == "pending_input"

    def test_determine_env_credential_status_without_requirements(self):
        """Test credential status determination without requirements"""
        # Arrange
        credential_summary = {
            "credential_requirements": []
        }
        
        # Act
        status = determine_env_credential_status(credential_summary)
        
        # Assert
        assert status == "not_required"

    def test_determine_env_credential_status_none_summary(self):
        """Test credential status determination with None summary"""
        # Act
        status = determine_env_credential_status(None)
        
        # Assert
        assert status == "not_required"

    def test_analyze_workflow_error_handling(self):
        """Test error handling in workflow analysis"""
        # Arrange - invalid workflow data
        invalid_workflow = {
            "invalid": "data"
        }
        
        # Act - should not crash, should return original data
        result = analyze_and_update_workflow_auth(invalid_workflow)
        
        # Assert - should return the original data (graceful degradation)
        assert result == invalid_workflow

    def test_analyze_workflow_with_multiple_auth_requirements(self):
        """Test analyzing workflow with multiple authentication requirements"""
        # Arrange
        workflow_data = {
            "id": "workflow-multi-auth",
            "name": "Multi-Auth Workflow",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "openai_chat",
                    "inputs": {
                        "openai_api_key": {
                            "input_type": "credential",
                            "required": True
                        }
                    }
                },
                {
                    "id": "node-2", 
                    "type": "github_api",
                    "inputs": {
                        "github_token": {
                            "input_type": "credential",
                            "required": True
                        }
                    }
                }
            ],
            "available_nodes": []
        }
        
        # Act
        result = analyze_and_update_workflow_auth(workflow_data)
        
        # Assert
        credential_summary = result["credential_summary"]
        assert credential_summary["total_requirements"] == 2
        assert len(credential_summary["credential_requirements"]) == 2
        
        # Check both requirements are present
        field_names = [req["field_name"] for req in credential_summary["credential_requirements"]]
        assert "openai_api_key" in field_names
        assert "github_token" in field_names
        
        # Check providers
        providers = [req["provider_name"] for req in credential_summary["credential_requirements"]]
        assert "openai" in providers
        assert "github" in providers
        
        assert result["env_credential_status"] == "pending_input"

    def test_backward_compatibility_with_legacy_auth_summary(self, sample_workflow_with_auth):
        """Test that the analysis maintains backward compatibility with legacy auth summary"""
        # Act
        result = analyze_and_update_workflow_auth(sample_workflow_with_auth)
        
        # Assert - should have both legacy and new analysis
        assert "available_nodes" in result  # Legacy format
        assert "credential_summary" in result  # New optimized format
        
        # Check that available_nodes contains auth summary (legacy)
        available_nodes = result["available_nodes"]
        auth_summary_node = next(
            (node for node in available_nodes if node.get("type") == "auth_summary"),
            None
        )
        # Note: This might be None if no legacy auth summary is added, which is fine
        # The important thing is that the new credential_summary is present
