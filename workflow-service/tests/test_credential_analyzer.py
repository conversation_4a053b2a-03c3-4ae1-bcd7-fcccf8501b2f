"""
Test suite for the optimized credential analyzer
Following TDD approach: Write tests first, then implement
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from typing import Dict, List, Any

# Import the classes we'll implement
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import (
    CredentialSummary,
    CredentialRequirementSpec,
    ProviderSummary
)


class TestOptimizedCredentialAnalyzer:
    """Test cases for OptimizedCredentialAnalyzer"""
    
    @pytest.fixture
    def analyzer(self):
        """Create analyzer instance for testing"""
        return OptimizedCredentialAnalyzer()
    
    @pytest.fixture
    def sample_workflow_with_env_keys(self):
        """Sample workflow with environment key requirements"""
        return {
            "id": "workflow-123",
            "name": "AI Content Pipeline",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "openai_chat",
                    "inputs": {
                        "openai_api_key": {
                            "input_type": "credential",
                            "required": True,
                            "description": "OpenAI API key for GPT models"
                        },
                        "model": {
                            "input_type": "dropdown",
                            "value": "gpt-4"
                        }
                    }
                }
            ],
            "available_nodes": [
                {
                    "id": "node-2", 
                    "type": "github_api",
                    "inputs": {
                        "github_token": {
                            "input_type": "credential",
                            "required": True,
                            "description": "GitHub Personal Access Token"
                        }
                    }
                }
            ]
        }
    
    @pytest.fixture
    def sample_workflow_with_oauth(self):
        """Sample workflow with OAuth requirements"""
        return {
            "id": "workflow-456",
            "name": "Google Drive Processor",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "google_drive",
                    "inputs": {
                        "google_oauth": {
                            "input_type": "oauth",
                            "required": True,
                            "description": "Google Drive OAuth access"
                        }
                    }
                }
            ],
            "available_nodes": []
        }
    
    @pytest.fixture
    def sample_workflow_no_auth(self):
        """Sample workflow with no authentication requirements"""
        return {
            "id": "workflow-789",
            "name": "Simple Text Processor",
            "start_nodes": [
                {
                    "id": "node-1",
                    "type": "text_processor",
                    "inputs": {
                        "input_text": {
                            "input_type": "string",
                            "required": True,
                            "description": "Text to process"
                        }
                    }
                }
            ],
            "available_nodes": []
        }

    def test_analyze_workflow_with_env_keys(self, analyzer, sample_workflow_with_env_keys):
        """Test analyzing workflow with environment key requirements"""
        # Act
        result = analyzer.analyze_workflow_optimized(sample_workflow_with_env_keys)
        
        # Assert
        assert isinstance(result, CredentialSummary)
        assert result.total_requirements == 2
        assert len(result.credential_requirements) == 2
        
        # Check OpenAI credential
        openai_cred = next((c for c in result.credential_requirements if c.field_name == "openai_api_key"), None)
        assert openai_cred is not None
        assert openai_cred.credential_type == "api_key"
        assert openai_cred.is_required == True
        assert openai_cred.provider_name == "openai"
        assert openai_cred.component_count == 1
        
        # Check GitHub credential
        github_cred = next((c for c in result.credential_requirements if c.field_name == "github_token"), None)
        assert github_cred is not None
        assert github_cred.credential_type == "api_key"
        assert github_cred.provider_name == "github"
        
        # Check provider summary
        assert "openai" in result.by_provider
        assert "github" in result.by_provider
        assert result.by_provider["openai"].count == 1
        assert result.by_provider["openai"].required == True
        assert "api_key" in result.by_provider["openai"].types

    def test_analyze_workflow_with_oauth(self, analyzer, sample_workflow_with_oauth):
        """Test analyzing workflow with OAuth requirements"""
        # Act
        result = analyzer.analyze_workflow_optimized(sample_workflow_with_oauth)
        
        # Assert
        assert result.total_requirements == 1
        assert len(result.credential_requirements) == 1
        
        oauth_cred = result.credential_requirements[0]
        assert oauth_cred.credential_type == "oauth"
        assert oauth_cred.field_name == "google_oauth"
        assert oauth_cred.provider_name == "google"
        assert oauth_cred.is_required == True

    def test_analyze_workflow_no_auth(self, analyzer, sample_workflow_no_auth):
        """Test analyzing workflow with no authentication requirements"""
        # Act
        result = analyzer.analyze_workflow_optimized(sample_workflow_no_auth)
        
        # Assert
        assert result.total_requirements == 0
        assert len(result.credential_requirements) == 0
        assert len(result.by_provider) == 0
        assert result.requires_authentication == False

    def test_provider_detection_patterns(self, analyzer):
        """Test provider detection using precompiled patterns"""
        # Test cases for provider detection
        test_cases = [
            ("openai_api_key", "openai"),
            ("gpt_token", "openai"),
            ("github_token", "github"),
            ("gh_access_token", "github"),
            ("google_api_key", "google"),
            ("gmail_token", "google"),
            ("slack_bot_token", "slack"),
            ("discord_webhook", "discord"),
            ("unknown_service_key", "generic")
        ]
        
        for field_name, expected_provider in test_cases:
            # Act
            provider = analyzer._detect_provider_fast(field_name, {})
            
            # Assert
            assert provider == expected_provider, f"Expected {expected_provider} for {field_name}, got {provider}"

    def test_credential_type_detection(self, analyzer):
        """Test credential type detection from field names and input types"""
        test_cases = [
            ("api_key", "credential", "api_key"),
            ("access_token", "credential", "api_key"),
            ("oauth_token", "oauth", "oauth"),
            ("password", "password", "password"),
            ("secret_key", "credential", "api_key")
        ]
        
        for field_name, input_type, expected_type in test_cases:
            # Create mock node
            node = {
                "inputs": {
                    field_name: {
                        "input_type": input_type,
                        "required": True
                    }
                }
            }
            
            # Act
            credentials = analyzer._extract_node_credentials_fast(node)
            
            # Assert
            assert len(credentials) == 1
            assert credentials[0]['type'] == expected_type

    def test_setup_time_estimation(self, analyzer):
        """Test setup time estimation based on credential types"""
        # Create mock credential requirements
        requirements = [
            CredentialRequirementSpec(
                credential_type="api_key",
                field_name="openai_key",
                is_required=True,
                component_count=1,
                provider_name="openai"
            ),
            CredentialRequirementSpec(
                credential_type="oauth",
                field_name="google_oauth",
                is_required=True,
                component_count=1,
                provider_name="google"
            ),
            CredentialRequirementSpec(
                credential_type="api_key",
                field_name="github_token",
                is_required=True,
                component_count=1,
                provider_name="github"
            )
        ]
        
        # Act
        setup_time = analyzer._estimate_setup_time(requirements)
        
        # Assert
        # Should be: 3 (api_key) + 10 (oauth) + 3 (api_key) = 16 minutes
        assert setup_time == 16

    def test_setup_time_capped_at_60_minutes(self, analyzer):
        """Test that setup time is capped at 60 minutes"""
        # Create many requirements to exceed 60 minutes
        requirements = [
            CredentialRequirementSpec(
                credential_type="oauth",
                field_name=f"oauth_{i}",
                is_required=True,
                component_count=1,
                provider_name="provider"
            )
            for i in range(10)  # 10 OAuth = 100 minutes
        ]
        
        # Act
        setup_time = analyzer._estimate_setup_time(requirements)
        
        # Assert
        assert setup_time == 60  # Should be capped

    def test_empty_workflow_analysis(self, analyzer):
        """Test analyzing empty workflow"""
        empty_workflow = {
            "id": "empty-workflow",
            "start_nodes": [],
            "available_nodes": []
        }
        
        # Act
        result = analyzer.analyze_workflow_optimized(empty_workflow)
        
        # Assert
        assert result.total_requirements == 0
        assert len(result.credential_requirements) == 0
        assert result.requires_authentication == False

    def test_analysis_metadata(self, analyzer, sample_workflow_with_env_keys):
        """Test that analysis includes proper metadata"""
        # Act
        result = analyzer.analyze_workflow_optimized(sample_workflow_with_env_keys)
        
        # Assert
        assert isinstance(result.last_analyzed, datetime)
        assert result.estimated_setup_time > 0

    @pytest.mark.parametrize("component_count,expected_description", [
        (1, "OpenAI api_key for 1 components"),
        (5, "OpenAI api_key for 5 components"),
        (15, "OpenAI api_key for 15 components")
    ])
    def test_credential_description_generation(self, analyzer, component_count, expected_description):
        """Test credential description generation with different component counts"""
        # This test will help us verify the description format
        # Implementation will be added when we create the analyzer
        pass
