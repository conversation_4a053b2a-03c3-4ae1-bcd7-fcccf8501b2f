"""
Test suite for workflow authentication endpoints
Following TDD approach: Write tests first, then implement
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status
import json

# Import the app and dependencies we'll implement
from app.main import app
from app.models.workflow_builder.credential_analysis import Credential<PERSON>ummary, CredentialRequirementSpec


class TestWorkflowAuthEndpoints:
    """Test cases for workflow authentication endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_workflow_with_auth(self):
        """Mock workflow that requires authentication"""
        return {
            "id": "workflow-123",
            "name": "AI Content Pipeline",
            "credential_summary": {
                "total_requirements": 2,
                "by_provider": {
                    "openai": {
                        "count": 15,
                        "types": ["api_key"],
                        "required": True,
                        "fields": ["openai_api_key"]
                    }
                },
                "estimated_setup_time": 15,
                "analysis_version": 2,
                "last_analyzed": "2025-01-28T10:00:00Z",
                "credential_requirements": [
                    {
                        "credential_type": "api_key",
                        "field_name": "openai_api_key",
                        "is_required": True,
                        "component_count": 15,
                        "provider_name": "openai"
                    }
                ]
            },
            "env_credential_status": "pending_input"
        }
    
    @pytest.fixture
    def mock_workflow_no_auth(self):
        """Mock workflow that doesn't require authentication"""
        return {
            "id": "workflow-456",
            "name": "Simple Text Processor",
            "credential_summary": None,
            "env_credential_status": "not_required"
        }

    @pytest.fixture
    def mock_user_credentials(self):
        """Mock user credentials for testing"""
        return [
            {
                "id": "cred_123",
                "name": "My OpenAI Key",
                "type": "api_key",
                "created_at": "2025-01-20T10:00:00Z"
            },
            {
                "id": "cred_456", 
                "name": "GitHub Token",
                "type": "api_key",
                "created_at": "2025-01-21T10:00:00Z"
            }
        ]

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_get_workflow_auth_summary_with_auth_required(self, mock_get_workflow, client, mock_workflow_with_auth):
        """Test GET /workflows/{id}/auth-summary for workflow requiring auth"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_with_auth
        
        # Act
        response = client.get("/api/v1/workflows/workflow-123/auth-summary")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["env_credential_status"] == "pending_input"
        assert data["credential_summary"] is not None
        assert data["credential_summary"]["total_requirements"] == 2
        assert len(data["credential_summary"]["credential_requirements"]) == 1

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_get_workflow_auth_summary_no_auth_required(self, mock_get_workflow, client, mock_workflow_no_auth):
        """Test GET /workflows/{id}/auth-summary for workflow not requiring auth"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_no_auth
        
        # Act
        response = client.get("/api/v1/workflows/workflow-456/auth-summary")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["env_credential_status"] == "not_required"
        assert data["credential_summary"] is None

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_get_workflow_auth_summary_workflow_not_found(self, mock_get_workflow, client):
        """Test GET /workflows/{id}/auth-summary for non-existent workflow"""
        # Arrange
        mock_get_workflow.return_value = None
        
        # Act
        response = client.get("/api/v1/workflows/non-existent/auth-summary")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "not found" in data["detail"].lower()

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    @patch('app.services.workflow_service.WorkflowService.import_workflow_with_credentials')
    @patch('app.services.user_service.UserServiceClient.batch_check_user_coverage')
    def test_import_workflow_with_auth_analyze_action(
        self, 
        mock_check_coverage,
        mock_import_workflow,
        mock_get_workflow, 
        client, 
        mock_workflow_with_auth,
        mock_user_credentials
    ):
        """Test POST /workflows/{id}/import-with-auth with analyze action"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_with_auth
        mock_check_coverage.return_value = {
            "total_requirements": 2,
            "available_credentials": 1,
            "missing_credentials": 1,
            "coverage_percentage": 50.0,
            "detailed_status": []
        }
        
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["action"] == "analyzed"
        assert "credential_summary" in data
        assert "user_coverage" in data
        assert data["user_coverage"]["coverage_percentage"] == 50.0
        
        # Verify import was not called for analyze action
        mock_import_workflow.assert_not_called()

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    @patch('app.services.workflow_service.WorkflowService.import_workflow_with_credentials')
    @patch('app.services.user_service.UserServiceClient.batch_check_user_coverage')
    def test_import_workflow_with_auth_import_action(
        self,
        mock_check_coverage,
        mock_import_workflow,
        mock_get_workflow,
        client,
        mock_workflow_with_auth
    ):
        """Test POST /workflows/{id}/import-with-auth with import action"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_with_auth
        mock_check_coverage.return_value = {
            "total_requirements": 2,
            "available_credentials": 2,
            "missing_credentials": 0,
            "coverage_percentage": 100.0,
            "detailed_status": []
        }
        mock_import_workflow.return_value = {
            "workflow_id": "imported-workflow-789",
            "status": "success"
        }
        
        request_data = {
            "action": "import",
            "credential_mapping": {
                "openai_api_key": {
                    "credential_id": "cred_123",
                    "credential_name": "My OpenAI Key"
                }
            }
        }
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["action"] == "imported"
        assert data["workflow_id"] == "imported-workflow-789"
        assert "credential_summary" in data
        assert "user_coverage" in data
        
        # Verify import was called
        mock_import_workflow.assert_called_once()

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_import_workflow_with_auth_workflow_not_found(self, mock_get_workflow, client):
        """Test POST /workflows/{id}/import-with-auth for non-existent workflow"""
        # Arrange
        mock_get_workflow.return_value = None
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/non-existent/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_import_workflow_with_auth_no_credential_summary(self, mock_get_workflow, client):
        """Test POST /workflows/{id}/import-with-auth for workflow without credential analysis"""
        # Arrange
        workflow_without_analysis = {
            "id": "workflow-123",
            "credential_summary": None,
            "env_credential_status": "not_required"
        }
        mock_get_workflow.return_value = workflow_without_analysis
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "credential analysis not available" in data["detail"].lower()

    def test_import_workflow_with_auth_invalid_action(self, client):
        """Test POST /workflows/{id}/import-with-auth with invalid action"""
        # Arrange
        request_data = {"action": "invalid_action"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_import_workflow_with_auth_missing_auth_header(self, client):
        """Test POST /workflows/{id}/import-with-auth without authentication"""
        # Arrange
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    @patch('app.services.workflow_service.WorkflowService.import_workflow_with_credentials')
    def test_import_workflow_with_auth_import_failure(
        self,
        mock_import_workflow,
        mock_get_workflow,
        client,
        mock_workflow_with_auth
    ):
        """Test POST /workflows/{id}/import-with-auth when import fails"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_with_auth
        mock_import_workflow.side_effect = Exception("Import failed")
        
        request_data = {
            "action": "import",
            "credential_mapping": {}
        }
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    def test_get_workflow_auth_summary_invalid_workflow_id(self, mock_get_workflow, client):
        """Test GET /workflows/{id}/auth-summary with invalid workflow ID format"""
        # Arrange
        mock_get_workflow.side_effect = ValueError("Invalid workflow ID")
        
        # Act
        response = client.get("/api/v1/workflows/invalid-id-format/auth-summary")
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @patch('app.services.workflow_service.WorkflowService.get_workflow_by_id')
    @patch('app.services.user_service.UserServiceClient.batch_check_user_coverage')
    def test_import_workflow_with_auth_coverage_check_failure(
        self,
        mock_check_coverage,
        mock_get_workflow,
        client,
        mock_workflow_with_auth
    ):
        """Test POST /workflows/{id}/import-with-auth when coverage check fails"""
        # Arrange
        mock_get_workflow.return_value = mock_workflow_with_auth
        mock_check_coverage.side_effect = Exception("Coverage check failed")
        
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
