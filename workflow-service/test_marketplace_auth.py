#!/usr/bin/env python3
"""
Test script for marketplace authentication functionality
Uses the test workflows table for safe testing
"""

import sys
import os
import json
from datetime import datetime

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database.database import get_db
from app.services.workflow_auth_test_service import WorkflowAuthTestService
from app.models.workflow_test import WorkflowCredentialMigrationTest


def create_sample_workflow_data():
    """Create sample workflow data for testing"""
    return {
        "name": "AI Content Generator Test",
        "description": "Test workflow with OpenAI and GitHub authentication",
        "workflow_url": "/test/ai-content-generator",
        "builder_url": "/test/ai-content-generator/builder",
        "owner_id": "test-user-123",
        "owner_type": "USER",
        "visibility": "PUBLIC",
        "start_nodes": [
            {
                "id": "node-1",
                "type": "openai_chat",
                "inputs": {
                    "openai_api_key": {
                        "input_type": "credential",
                        "required": True,
                        "description": "OpenAI API key"
                    },
                    "query": {
                        "input_type": "string",
                        "required": True,
                        "value": "Generate content about AI"
                    }
                }
            }
        ],
        "available_nodes": [
            {
                "id": "node-2", 
                "type": "github_api",
                "inputs": {
                    "github_token": {
                        "input_type": "credential",
                        "required": True,
                        "description": "GitHub personal access token"
                    },
                    "repository": {
                        "input_type": "string",
                        "required": True
                    }
                }
            }
        ]
    }


def test_credential_analysis():
    """Test credential analysis functionality"""
    print("🧪 Testing Marketplace Authentication System")
    print("=" * 50)
    
    # Get database session
    db = next(get_db())
    service = WorkflowAuthTestService(db)
    
    try:
        # 1. Create test workflow
        print("1️⃣ Creating test workflow with authentication requirements...")
        workflow_data = create_sample_workflow_data()
        test_workflow = service.create_test_workflow(workflow_data)
        print(f"   ✅ Created test workflow: {test_workflow.id}")
        print(f"   📊 Credential status: {test_workflow.env_credential_status}")
        
        # 2. Display credential analysis
        if test_workflow.credential_summary:
            summary = test_workflow.credential_summary
            print(f"   🔐 Total requirements: {summary.get('total_requirements', 0)}")
            print(f"   ⏱️  Estimated setup time: {summary.get('estimated_setup_time', 0)} minutes")
            
            if 'credential_requirements' in summary:
                print("   📋 Required credentials:")
                for req in summary['credential_requirements']:
                    print(f"      - {req.get('field_name')}: {req.get('credential_type')} ({req.get('provider_name', 'generic')})")
        
        # 3. Test marketplace retrieval
        print("\n2️⃣ Testing marketplace workflow retrieval...")
        marketplace_workflows = service.get_marketplace_test_workflows(limit=5)
        print(f"   ✅ Found {len(marketplace_workflows)} marketplace test workflows")
        
        for workflow in marketplace_workflows:
            print(f"   📄 {workflow['name']} - Auth: {workflow['requires_authentication']}")
        
        # 4. Test workflow update
        print("\n3️⃣ Testing workflow credential analysis update...")
        updated_data = workflow_data.copy()
        updated_data['available_nodes'].append({
            "id": "node-3",
            "type": "slack_api", 
            "inputs": {
                "slack_token": {
                    "input_type": "credential",
                    "required": True,
                    "description": "Slack bot token"
                }
            }
        })
        
        success = service.analyze_and_update_test_workflow(test_workflow.id, updated_data)
        if success:
            print("   ✅ Successfully updated workflow with new credential requirements")
            
            # Get updated workflow
            updated_workflow = service.get_test_workflow_with_auth_summary(test_workflow.id)
            if updated_workflow and updated_workflow['credential_summary']:
                new_total = updated_workflow['credential_summary'].get('total_requirements', 0)
                print(f"   📊 Updated total requirements: {new_total}")
        
        # 5. Performance test
        print("\n4️⃣ Testing performance...")
        start_time = datetime.now()
        for i in range(10):
            service.get_test_workflow_with_auth_summary(test_workflow.id)
        end_time = datetime.now()
        
        avg_time = (end_time - start_time).total_seconds() * 1000 / 10
        print(f"   ⚡ Average retrieval time: {avg_time:.2f}ms")
        
        # 6. Cleanup option
        print("\n5️⃣ Cleanup options:")
        print("   🗑️  To cleanup test workflows, run: service.cleanup_test_workflows()")
        print(f"   📊 Current test workflows count: {db.query(WorkflowCredentialMigrationTest).count()}")
        
        print("\n✅ All tests completed successfully!")
        print("🎉 Marketplace authentication system is working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


def cleanup_test_data():
    """Cleanup all test data"""
    print("🧹 Cleaning up test data...")
    
    db = next(get_db())
    service = WorkflowAuthTestService(db)
    
    try:
        count = service.cleanup_test_workflows()
        print(f"✅ Cleaned up {count} test workflows")
    except Exception as e:
        print(f"❌ Cleanup failed: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "cleanup":
        cleanup_test_data()
    else:
        test_credential_analysis()
        
        print("\n" + "=" * 50)
        print("💡 Usage:")
        print("   python test_marketplace_auth.py          # Run tests")
        print("   python test_marketplace_auth.py cleanup  # Cleanup test data")
