#!/usr/bin/env python3
"""
Test script for Marketplace Authentication API Gateway endpoints
Tests the complete flow: workflow analysis -> credential mapping -> import
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any

# Configuration
API_GATEWAY_BASE_URL = "http://localhost:8000"  # Adjust port as needed
WORKFLOW_SERVICE_BASE_URL = "http://localhost:8001"  # Adjust port as needed

# Test data
SAMPLE_WORKFLOW_DATA = {
    "name": "AI Content Generator with GitHub Integration",
    "description": "Test workflow requiring OpenAI and GitHub authentication",
    "workflow_url": "/test/ai-content-generator",
    "builder_url": "/test/ai-content-generator/builder",
    "owner_id": "test-user-123",
    "owner_type": "USER",
    "visibility": "PUBLIC",
    "start_nodes": [
        {
            "id": "openai-node",
            "type": "openai_chat",
            "inputs": {
                "openai_api_key": {
                    "input_type": "credential",
                    "required": True,
                    "description": "OpenAI API key for content generation"
                },
                "query": {
                    "input_type": "string",
                    "required": True,
                    "value": "Generate a blog post about AI"
                },
                "model": {
                    "input_type": "string",
                    "required": True,
                    "value": "gpt-4"
                }
            }
        }
    ],
    "available_nodes": [
        {
            "id": "github-node",
            "type": "github_api",
            "inputs": {
                "github_token": {
                    "input_type": "credential",
                    "required": True,
                    "description": "GitHub personal access token"
                },
                "repository": {
                    "input_type": "string",
                    "required": True,
                    "value": "my-repo/content"
                },
                "action": {
                    "input_type": "string",
                    "required": True,
                    "value": "create_file"
                }
            }
        },
        {
            "id": "slack-node",
            "type": "slack_api",
            "inputs": {
                "slack_token": {
                    "input_type": "credential",
                    "required": True,
                    "description": "Slack bot token for notifications"
                },
                "channel": {
                    "input_type": "string",
                    "required": True,
                    "value": "#general"
                }
            }
        }
    ]
}

# Mock authentication token (replace with real token in actual testing)
TEST_AUTH_TOKEN = "Bearer test-token-123"

class MarketplaceAuthTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": TEST_AUTH_TOKEN,
            "Content-Type": "application/json"
        })
        self.test_workflow_id = None
    
    def create_test_workflow(self) -> str:
        """Create a test workflow in the workflow service"""
        print("🔧 Step 1: Creating test workflow...")
        
        try:
            # Direct call to workflow service to create test workflow
            response = self.session.post(
                f"{WORKFLOW_SERVICE_BASE_URL}/workflows",
                json=SAMPLE_WORKFLOW_DATA
            )
            
            if response.status_code == 201:
                workflow_data = response.json()
                workflow_id = workflow_data.get("id")
                print(f"   ✅ Test workflow created: {workflow_id}")
                return workflow_id
            else:
                print(f"   ❌ Failed to create workflow: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
        except requests.exceptions.ConnectionError:
            print("   ⚠️  Workflow service not running, using mock workflow ID")
            return "test-workflow-123"
        except Exception as e:
            print(f"   ❌ Error creating workflow: {str(e)}")
            return None
    
    def test_auth_summary_endpoint(self, workflow_id: str) -> Dict:
        """Test the authentication summary endpoint"""
        print("🔍 Step 2: Testing authentication summary endpoint...")
        
        try:
            url = f"{API_GATEWAY_BASE_URL}/marketplace/workflows/{workflow_id}/auth-summary"
            response = self.session.get(url)
            
            print(f"   📡 GET {url}")
            print(f"   📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ Authentication summary retrieved successfully!")
                print(f"   🔐 Requires Authentication: {data.get('requires_authentication', False)}")
                print(f"   📋 Credential Status: {data.get('env_credential_status', 'unknown')}")
                
                if data.get('credential_summary'):
                    summary = data['credential_summary']
                    print(f"   📊 Total Requirements: {summary.get('total_requirements', 0)}")
                    print(f"   ⏱️  Setup Time: {summary.get('estimated_setup_time', 0)} minutes")
                    
                    if 'credential_requirements' in summary:
                        print("   📋 Required Credentials:")
                        for req in summary['credential_requirements']:
                            provider = req.get('provider_name', 'generic')
                            cred_type = req.get('credential_type', 'unknown')
                            field = req.get('field_name', 'unknown')
                            print(f"      - {field}: {cred_type} ({provider})")
                
                return data
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return {}
                
        except requests.exceptions.ConnectionError:
            print("   ❌ API Gateway not running")
            return {}
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {}
    
    def test_import_with_auth_analyze(self, workflow_id: str) -> Dict:
        """Test the import with authentication endpoint (analyze action)"""
        print("🔬 Step 3: Testing import with auth - analyze action...")
        
        try:
            url = f"{API_GATEWAY_BASE_URL}/marketplace/workflows/{workflow_id}/import-with-auth"
            payload = {
                "action": "analyze"
            }
            
            response = self.session.post(url, json=payload)
            
            print(f"   📡 POST {url}")
            print(f"   📦 Payload: {json.dumps(payload, indent=2)}")
            print(f"   📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ Analysis completed successfully!")
                print(f"   🎯 Action: {data.get('action', 'unknown')}")
                
                if data.get('credential_summary'):
                    summary = data['credential_summary']
                    print(f"   📊 Analysis Results:")
                    print(f"      - Total Requirements: {summary.get('total_requirements', 0)}")
                    print(f"      - Setup Time: {summary.get('estimated_setup_time', 0)} minutes")
                
                if data.get('user_coverage'):
                    coverage = data['user_coverage']
                    print(f"   👤 User Coverage:")
                    print(f"      - Has Required Credentials: {coverage.get('has_all_required', False)}")
                    print(f"      - Missing Count: {coverage.get('missing_count', 0)}")
                
                return data
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return {}
                
        except requests.exceptions.ConnectionError:
            print("   ❌ API Gateway not running")
            return {}
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {}
    
    def test_import_with_auth_import(self, workflow_id: str) -> Dict:
        """Test the import with authentication endpoint (import action)"""
        print("📥 Step 4: Testing import with auth - import action...")
        
        try:
            url = f"{API_GATEWAY_BASE_URL}/marketplace/workflows/{workflow_id}/import-with-auth"
            payload = {
                "action": "import",
                "credential_mapping": {
                    "openai_api_key": {
                        "credential_id": "cred-openai-123",
                        "credential_name": "My OpenAI Key"
                    },
                    "github_token": {
                        "credential_id": "cred-github-456", 
                        "credential_name": "My GitHub Token"
                    },
                    "slack_token": {
                        "credential_id": "cred-slack-789",
                        "credential_name": "My Slack Bot Token"
                    }
                }
            }
            
            response = self.session.post(url, json=payload)
            
            print(f"   📡 POST {url}")
            print(f"   📦 Payload: {json.dumps(payload, indent=2)}")
            print(f"   📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ Import completed successfully!")
                print(f"   🎯 Action: {data.get('action', 'unknown')}")
                print(f"   🆔 Imported Workflow ID: {data.get('workflow_id', 'unknown')}")
                
                return data
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return {}
                
        except requests.exceptions.ConnectionError:
            print("   ❌ API Gateway not running")
            return {}
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {}
    
    def run_complete_test(self):
        """Run the complete marketplace authentication test flow"""
        print("🚀 Starting Marketplace Authentication API Test")
        print("=" * 60)
        
        start_time = time.time()
        
        # Step 1: Create test workflow
        workflow_id = self.create_test_workflow()
        if not workflow_id:
            print("❌ Cannot proceed without workflow ID")
            return
        
        self.test_workflow_id = workflow_id
        
        # Step 2: Test auth summary
        auth_summary = self.test_auth_summary_endpoint(workflow_id)
        
        # Step 3: Test analyze action
        analyze_result = self.test_import_with_auth_analyze(workflow_id)
        
        # Step 4: Test import action
        import_result = self.test_import_with_auth_import(workflow_id)
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"⏱️  Total Duration: {duration:.2f} seconds")
        print(f"🆔 Test Workflow ID: {workflow_id}")
        print(f"✅ Auth Summary: {'✓' if auth_summary else '✗'}")
        print(f"✅ Analyze Action: {'✓' if analyze_result else '✗'}")
        print(f"✅ Import Action: {'✓' if import_result else '✗'}")
        
        if auth_summary and analyze_result:
            print("\n🎉 Marketplace Authentication API is working correctly!")
        else:
            print("\n⚠️  Some endpoints may need attention")
        
        print("\n💡 Next Steps:")
        print("   1. Start API Gateway: cd api-gateway && poetry run uvicorn app.main:app --port 8000")
        print("   2. Start Workflow Service: cd workflow-service && poetry run uvicorn app.main:app --port 8001")
        print("   3. Update authentication token in script")
        print("   4. Run script again for full testing")


def main():
    """Main test function"""
    tester = MarketplaceAuthTester()
    tester.run_complete_test()


if __name__ == "__main__":
    main()
