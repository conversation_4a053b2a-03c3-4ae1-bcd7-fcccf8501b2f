{"data": [{"id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "name": "Google Forms", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Forms_Logo-removebg-preview%20%281%29.png/1752823679-Google_Forms_Logo-removebg-preview1.png", "description": "Google Forms MCP ", "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "user_ids": null, "owner_type": "user", "config": [{"url": "https://google-form-mcp-server-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": ["forms", "google", "google forms"], "status": "active", "created_at": "2025-07-18T07:28:29.603297", "updated_at": "2025-07-21T04:42:59.901868", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "get_google_form", "description": "Get a Google Form by ID", "input_schema": {"properties": {"form_id": {"title": "Form Id", "type": "string"}}, "required": ["form_id"], "title": "GetGoogleForm", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_google_form", "description": "Create a new Google Form", "input_schema": {"properties": {"title": {"title": "Title", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}}, "required": ["title"], "title": "CreateGoogleForm", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "get_google_form_responses", "description": "Get responses for a Google Form", "input_schema": {"properties": {"form_id": {"title": "Form Id", "type": "string"}}, "required": ["form_id"], "title": "GetGoogleFormResponses", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "update_google_form", "description": "Update a Google Form with new questions", "input_schema": {"$defs": {"ChoiceQuestion": {"properties": {"type": {"enum": ["RADIO", "CHECKBOX", "DROP_DOWN"], "title": "Type", "type": "string"}, "options": {"items": {"$ref": "#/$defs/Option"}, "title": "Options", "type": "array"}, "shuffle": {"default": false, "title": "Shuffle", "type": "boolean"}}, "required": ["type", "options"], "title": "ChoiceQuestion", "type": "object"}, "CreateItemRequest": {"properties": {"item": {"$ref": "#/$defs/Item"}, "location": {"$ref": "#/$defs/Location"}}, "required": ["item", "location"], "title": "CreateItemRequest", "type": "object"}, "DateQuestion": {"properties": {"includeTime": {"default": false, "title": "Includetime", "type": "boolean"}, "includeYear": {"default": false, "title": "Includeyear", "type": "boolean"}}, "title": "DateQuestion", "type": "object"}, "DeleteItemRequest": {"properties": {"location": {"$ref": "#/$defs/Location"}}, "required": ["location"], "title": "DeleteItemRequest", "type": "object"}, "FileUploadQuestion": {"properties": {"folderId": {"title": "Folderid", "type": "string"}, "types": {"items": {"type": "string"}, "title": "Types", "type": "array"}, "maxFiles": {"title": "Max<PERSON>les", "type": "integer"}, "maxFileSize": {"title": "Maxfilesize", "type": "string"}}, "required": ["folderId", "types", "maxFiles", "maxFileSize"], "title": "FileUploadQuestion", "type": "object"}, "Item": {"properties": {"title": {"title": "Title", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "questionItem": {"anyOf": [{"$ref": "#/$defs/QuestionItem"}, {"type": "null"}], "default": null}}, "required": ["title"], "title": "<PERSON><PERSON>", "type": "object"}, "Location": {"properties": {"index": {"title": "Index", "type": "integer"}}, "required": ["index"], "title": "Location", "type": "object"}, "MoveItemRequest": {"properties": {"original_location": {"$ref": "#/$defs/Location"}, "new_location": {"$ref": "#/$defs/Location"}}, "required": ["original_location", "new_location"], "title": "MoveItemRequest", "type": "object"}, "Option": {"properties": {"value": {"title": "Value", "type": "string"}}, "required": ["value"], "title": "Option", "type": "object"}, "Question": {"properties": {"required": {"default": false, "title": "Required", "type": "boolean"}, "textQuestion": {"anyOf": [{"$ref": "#/$defs/TextQuestion"}, {"type": "null"}], "default": null}, "choiceQuestion": {"anyOf": [{"$ref": "#/$defs/ChoiceQuestion"}, {"type": "null"}], "default": null}, "scaleQuestion": {"anyOf": [{"$ref": "#/$defs/ScaleQuestion"}, {"type": "null"}], "default": null}, "dateQuestion": {"anyOf": [{"$ref": "#/$defs/DateQuestion"}, {"type": "null"}], "default": null}, "timeQuestion": {"anyOf": [{"$ref": "#/$defs/TimeQuestion"}, {"type": "null"}], "default": null}, "fileUploadQuestion": {"anyOf": [{"$ref": "#/$defs/FileUploadQuestion"}, {"type": "null"}], "default": null}}, "title": "Question", "type": "object"}, "QuestionItem": {"properties": {"question": {"$ref": "#/$defs/Question"}}, "required": ["question"], "title": "QuestionItem", "type": "object"}, "Request": {"properties": {"createItem": {"anyOf": [{"$ref": "#/$defs/CreateItemRequest"}, {"type": "null"}], "default": null}, "updateFormInfo": {"anyOf": [{"$ref": "#/$defs/UpdateFormInfoRequest"}, {"type": "null"}], "default": null}, "updateItem": {"anyOf": [{"$ref": "#/$defs/UpdateItemRequest"}, {"type": "null"}], "default": null}, "deleteItem": {"anyOf": [{"$ref": "#/$defs/DeleteItemRequest"}, {"type": "null"}], "default": null}, "moveItem": {"anyOf": [{"$ref": "#/$defs/MoveItemRequest"}, {"type": "null"}], "default": null}}, "title": "Request", "type": "object"}, "ScaleQuestion": {"properties": {"low": {"title": "Low", "type": "integer"}, "high": {"title": "High", "type": "integer"}, "lowLabel": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Lowlabel"}, "highLabel": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Highlabel"}}, "required": ["low", "high"], "title": "ScaleQuestion", "type": "object"}, "TextQuestion": {"properties": {"paragraph": {"default": false, "title": "Paragraph", "type": "boolean"}}, "title": "TextQuestion", "type": "object"}, "TimeQuestion": {"properties": {"duration": {"default": false, "title": "Duration", "type": "boolean"}}, "title": "TimeQuestion", "type": "object"}, "UpdateFormInfoRequest": {"properties": {"info": {"additionalProperties": true, "title": "Info", "type": "object"}, "updateMask": {"title": "Updatemask", "type": "string"}}, "required": ["info", "updateMask"], "title": "UpdateFormInfoRequest", "type": "object"}, "UpdateItemRequest": {"properties": {"item": {"$ref": "#/$defs/Item"}, "location": {"$ref": "#/$defs/Location"}, "updateMask": {"title": "Updatemask", "type": "string"}}, "required": ["item", "location", "updateMask"], "title": "UpdateItemRequest", "type": "object"}}, "properties": {"form_id": {"title": "Form Id", "type": "string"}, "requests": {"description": "A list of requests to update the form. See the Google Forms API documentation for more details.", "items": {"$ref": "#/$defs/Request"}, "title": "Requests", "type": "array"}}, "required": ["form_id", "requests"], "title": "UpdateGoogleForm", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "validate_google_credentials", "description": "Validate Google OAuth credentials and check API access", "input_schema": {"properties": {}, "title": "ValidateGoogleCredentials", "type": "object"}, "output_schema": null, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "communication", "env_credential_status": "pending_input", "oauth_details": {"provider": "google", "tool_name": "google_form"}}, {"id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "name": "MetaAds", "logo": null, "description": "MCP server acting as an interface to the Facebook Ads, enabling programmatic access to Facebook Ads data and management features.", "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "user_ids": null, "owner_type": "user", "config": [{"url": "https://meta-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": ["ads"], "status": "active", "created_at": "2025-07-10T06:58:12.782791", "updated_at": "2025-07-22T14:29:44.379915", "image_name": null, "category": "marketing", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_meta_campaign", "description": "Create a Meta Ads campaign with specified configuration", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaCampaignObjective": {"description": "Meta Ads Campaign Objectives - Updated to match Meta API 2024+", "enum": ["OUTCOME_AWARENESS", "OUTCOME_ENGAGEMENT", "OUTCOME_LEADS", "OUTCOME_SALES", "OUTCOME_TRAFFIC", "OUTCOME_APP_PROMOTION", "BRAND_AWARENESS", "LINK_CLICKS", "POST_ENGAGEMENT", "LEAD_GENERATION", "APP_INSTALLS", "CONVERSIONS"], "title": "MetaCampaignObjective", "type": "string"}, "MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "Schema for creating a Meta Ads Campaign", "properties": {"name": {"description": "Campaign name", "title": "Name", "type": "string"}, "objective": {"$ref": "#/$defs/MetaCampaignObjective", "description": "Campaign objective"}, "status": {"$ref": "#/$defs/MetaCampaignStatus", "default": "PAUSED", "description": "Campaign status"}, "buying_type": {"default": "AUCTION", "description": "Buying type for the campaign", "title": "Buying Type", "type": "string"}, "bid_strategy": {"anyOf": [{"$ref": "#/$defs/MetaBidStrategy"}, {"type": "null"}], "default": null, "description": "Bidding strategy"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign start time (ISO format)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign end time (ISO format)", "title": "End Time"}, "special_ad_categories": {"description": "Special ad categories (required by Meta API)", "items": {"type": "string"}, "title": "Special Ad Categories", "type": "array"}}, "required": ["name", "objective"], "title": "MetaCampaignCreate", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_meta_campaign", "description": "Update an existing Meta Ads campaign with new configuration", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "<PERSON><PERSON><PERSON> for updating a Meta Ads Campaign", "properties": {"campaign_id": {"description": "Campaign ID to update", "title": "Campaign Id", "type": "string"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign name", "title": "Name"}, "status": {"anyOf": [{"$ref": "#/$defs/MetaCampaignStatus"}, {"type": "null"}], "default": null, "description": "New campaign status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "New daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "New lifetime budget in cents", "title": "Lifetime Budget"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign start time (ISO format)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign end time (ISO format)", "title": "End Time"}, "bid_strategy": {"anyOf": [{"$ref": "#/$defs/MetaBidStrategy"}, {"type": "null"}], "default": null, "description": "New bidding strategy"}, "special_ad_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "New special ad categories", "title": "Special Ad Categories"}}, "required": ["campaign_id"], "title": "MetaCampaignUpdate", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_campaign_status", "description": "Update the status of a Meta Ads campaign (ACTIVE, PAUSED, etc.)", "input_schema": {"$defs": {"MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "<PERSON><PERSON><PERSON> for updating campaign status only", "properties": {"campaign_id": {"description": "Campaign ID to update", "title": "Campaign Id", "type": "string"}, "status": {"$ref": "#/$defs/MetaCampaignStatus", "description": "New campaign status"}}, "required": ["campaign_id", "status"], "title": "MetaCampaignStatusUpdate", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_details", "description": "Get detailed information about a Meta Ads campaign", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting campaign details", "properties": {"campaign_id": {"description": "Campaign ID to retrieve", "title": "Campaign Id", "type": "string"}, "fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Specific fields to retrieve", "title": "Fields"}}, "required": ["campaign_id"], "title": "MetaCampaignGet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_accounts", "description": "Get ad accounts accessible by a user", "input_schema": {"description": "<PERSON><PERSON>a for getting ad accounts", "properties": {"user_id": {"default": "me", "description": "Meta user ID or 'me' for the current user", "title": "User Id", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of accounts to return", "title": "Limit"}}, "title": "GetAdAccountsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_account_info", "description": "Get detailed information about a specific ad account", "input_schema": {"description": "Schema for getting account info", "properties": {}, "title": "GetAccountInfoRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_account_pages", "description": "Get pages associated with a Meta Ads account", "input_schema": {"description": "Schema for getting account pages", "properties": {}, "title": "GetAccountPagesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaigns", "description": "Get campaigns for a Meta Ads account with optional filtering", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting campaigns", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of campaigns to return", "title": "Limit"}, "status_filter": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Filter by status (empty for all, or 'ACTIVE', 'PAUSED', etc.)", "title": "Status Filter"}}, "title": "GetCampaignsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_details", "description": "Get detailed information about a specific campaign", "input_schema": {"description": "Schema for getting campaign details (MCP version)", "properties": {"campaign_id": {"description": "Meta Ads campaign ID", "title": "Campaign Id", "type": "string"}}, "required": ["campaign_id"], "title": "McpMetaAdsCampaignDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_adsets", "description": "Get ad sets for a Meta Ads account with optional filtering by campaign", "input_schema": {"description": "Sc<PERSON>a for getting ad sets", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of ad sets to return", "title": "Limit"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional campaign ID to filter by", "title": "Campaign Id"}}, "title": "GetAdSetsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_adset_details", "description": "Get detailed information about a specific ad set", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting ad set details", "properties": {"adset_id": {"description": "Meta Ads ad set ID", "title": "Adset Id", "type": "string"}}, "required": ["adset_id"], "title": "GetAdSetDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_adset", "description": "Create a new ad set in a Meta Ads account", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad set", "properties": {"campaign_id": {"description": "Meta Ads campaign ID this ad set belongs to", "title": "Campaign Id", "type": "string"}, "name": {"description": "Ad set name", "title": "Name", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad set status", "title": "Status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "countries": {"description": "List of target countries (e.g., ['US', 'CA'])", "items": {}, "title": "Countries", "type": "array"}, "publisher_platforms": {"description": "List of publisher platforms (e.g., ['facebook', 'instagram'])", "items": {}, "title": "Publisher Platforms", "type": "array"}, "facebook_positions": {"description": "List of Facebook positions (e.g., ['feed', 'right_hand_column'])", "items": {}, "title": "Facebook Positions", "type": "array"}, "optimization_goal": {"description": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "title": "Optimization Goal", "type": "string"}, "billing_event": {"description": "How you're charged (e.g., 'IMPRESSIONS')", "title": "Billing Event", "type": "string"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents)", "title": "<PERSON><PERSON>"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST')", "title": "Bid Strategy"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Start time (ISO 8601)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "End time (ISO 8601)", "title": "End Time"}}, "required": ["campaign_id", "name", "countries", "publisher_platforms", "facebook_positions", "optimization_goal", "billing_event"], "title": "CreateAdSetRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_adset", "description": "Update an ad set with new settings including frequency caps", "input_schema": {"description": "Schema for updating an ad set", "properties": {"adset_id": {"description": "Meta Ads ad set ID", "title": "Adset Id", "type": "string"}, "frequency_control_specs": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "description": "List of frequency control specifications", "title": "Frequency Control Specs"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST_WITH_BID_CAP')", "title": "Bid Strategy"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents for USD)", "title": "<PERSON><PERSON>"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Update ad set status (ACTIVE, PAUSED, etc.)", "title": "Status"}, "targeting": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "description": "Targeting specifications including targeting_automation", "title": "Targeting"}}, "required": ["adset_id"], "title": "UpdateAdSetRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ads", "description": "Get ads for a Meta Ads account with optional filtering", "input_schema": {"description": "Schema for getting ads", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of ads to return", "title": "Limit"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional campaign ID to filter by", "title": "Campaign Id"}, "adset_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional ad set ID to filter by", "title": "Adset Id"}}, "title": "GetAdsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_details", "description": "Get detailed information about a specific ad", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting ad details", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_ad", "description": "Update an ad with new settings", "input_schema": {"description": "<PERSON><PERSON>a for updating an ad", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Update ad status (ACTIVE, PAUSED, etc.)", "title": "Status"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents for USD)", "title": "<PERSON><PERSON>"}}, "required": ["ad_id"], "title": "UpdateAdRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_creatives", "description": "Get creative details for a specific ad", "input_schema": {"description": "<PERSON><PERSON>a for getting ad creatives", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdCreativesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_ad_creative", "description": "Create a new ad creative using an uploaded image hash", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad creative", "example": {"call_to_action": {"type": "LEARN_MORE"}, "description": "Sleek. Powerful. Easy to Use.", "image_hash": "<uploaded_image_hash>", "link": "<landing-page-link>", "message": "This is the primary text for the ad. Discover why our new product is changing the game for everyone!", "name": "The Best New Product is Here!", "page_id": "<page_id>"}, "properties": {"name": {"description": "Creative name", "title": "Name", "type": "string"}, "page_id": {"description": "Page ID for the ad creative", "title": "Page Id", "type": "string"}, "image_hash": {"description": "Image hash for the ad creative", "title": "Image Hash", "type": "string"}, "link": {"description": "Landing page URL for the ad creative", "title": "Link", "type": "string"}, "message": {"description": "Primary text for the ad creative", "title": "Message", "type": "string"}, "image_name": {"description": "Name of the image", "title": "Image Name", "type": "string"}, "description": {"description": "Description of the ad creative", "title": "Description", "type": "string"}, "call_to_action": {"additionalProperties": true, "description": "Call to action for the ad creative", "title": "Call To Action", "type": "object"}}, "required": ["name", "page_id", "image_hash", "link", "message", "image_name", "description", "call_to_action"], "title": "CreateAdCreativeRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upload_ad_image", "description": "Upload an image to use in Meta Ads creatives", "input_schema": {"description": "Schema for uploading an ad image", "properties": {"image_url": {"description": "Image file URL for upload", "title": "Image Url", "type": "string"}}, "required": ["image_url"], "title": "UploadAdImageRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_image", "description": "Get, download, and visualize a Meta ad image in one step", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting an ad image", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdImageRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_insights", "description": "Get performance insights for a campaign, ad set, ad or account", "input_schema": {"description": "<PERSON><PERSON>a for getting insights", "properties": {"object_id": {"description": "ID of the campaign, ad set, ad or account", "title": "Object Id", "type": "string"}, "time_range": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "maximum", "description": "Time range for insights", "title": "Time Range"}, "breakdown": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional breakdown dimension (e.g., age, gender, country)", "title": "Breakdown"}, "level": {"description": "Level of aggregation (ad, adset, campaign, account)", "title": "Level", "type": "string"}}, "required": ["object_id", "level"], "title": "GetInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_budget_schedule", "description": "Create a budget schedule for a Meta Ads campaign", "input_schema": {"description": "Schema for creating a budget schedule", "properties": {"campaign_id": {"description": "Meta Ads campaign ID", "title": "Campaign Id", "type": "string"}, "budget_value": {"description": "Amount of budget increase", "title": "Budget Value", "type": "integer"}, "budget_value_type": {"description": "Type of budget value ('ABSOLUTE' or 'MULTIPLIER')", "title": "Budget Value Type", "type": "string"}, "time_start": {"description": "Unix timestamp for when the high demand period should start", "title": "Time Start", "type": "integer"}, "time_end": {"description": "Unix timestamp for when the high demand period should end", "title": "Time End", "type": "integer"}}, "required": ["campaign_id", "budget_value", "budget_value_type", "time_start", "time_end"], "title": "CreateBudgetScheduleRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_custom_audiences", "description": "Get custom audiences including email lists, purchase data, and website visitor audiences", "input_schema": {"description": "Schema for getting custom audiences", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of audiences to return", "title": "Limit"}}, "title": "CustomAudiencesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_lookalike_audiences", "description": "Get lookalike audiences that are similar to your existing customers", "input_schema": {"description": "Schema for getting lookalike audiences", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of audiences to return", "title": "Limit"}}, "title": "LookalikeAudiencesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_audience_insights", "description": "Get detailed insights and analytics for a specific audience including size estimates and demographics", "input_schema": {"description": "Schema for getting audience insights", "properties": {"audience_id": {"description": "Audience ID to get insights for", "title": "Audience Id", "type": "string"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights (e.g., 'last_30d', 'last_7d', 'last_90d')", "title": "Date Preset"}}, "required": ["audience_id"], "title": "AudienceInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_insights", "description": "Get historical campaign performance data including customer engagement metrics and conversion data", "input_schema": {"description": "Schema for getting campaign insights with customer data", "properties": {"campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Specific campaign ID (optional)", "title": "Campaign Id"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "level": {"default": "campaign", "description": "Level of insights (campaign, adset, ad)", "title": "Level", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "CampaignInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_account_insights", "description": "Get comprehensive ad account insights including customer behavior and demographic breakdowns", "input_schema": {"description": "Sc<PERSON>a for getting ad account insights", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions (e.g., ['age', 'gender', 'country'])", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "AdAccountInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_conversion_data", "description": "Get detailed conversion tracking data to understand customer actions and purchase behavior", "input_schema": {"description": "Schema for getting conversion data", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for conversion data", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "action_breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Action breakdown types", "title": "Action Breakdown"}, "level": {"default": "account", "description": "Level of conversion data (account, campaign, adset, ad)", "title": "Level", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "ConversionDataRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pixel_events", "description": "Get Facebook Pixel events data to track customer interactions and website behavior", "input_schema": {"description": "Schema for getting pixel events data", "properties": {"pixel_id": {"description": "Facebook Pixel ID", "title": "Pixel Id", "type": "string"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for pixel events", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "required": ["pixel_id"], "title": "PixelEventsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_customer_segments", "description": "Get customer segments and audience categorization data for targeted marketing", "input_schema": {"description": "Schema for getting customer segments", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "segment_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Type of segment to filter by", "title": "Segment Type"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of segments to return", "title": "Limit"}}, "title": "CustomerSegmentsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_audience_demographics", "description": "Get detailed demographic information about your audiences including age, gender, location, and interests", "input_schema": {"description": "Schema for getting audience demographics", "properties": {"audience_id": {"description": "Audience ID to get demographics for", "title": "Audience Id", "type": "string"}, "fields": {"description": "Demographic fields to include", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions for demographics", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "required": ["audience_id"], "title": "AudienceDemographicsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "gather_ad_requirements", "description": "Comprehensive tool to gather all requirements for creating Meta Ads campaigns, ad sets, and creatives", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaBillingEvent": {"description": "Meta Ads Billing Events", "enum": ["APP_INSTALLS", "CLICKS", "IMPRESSIONS", "LINK_CLICKS", "NONE", "OFFER_CLAIMS", "PAGE_LIKES", "POST_ENGAGEMENT", "THRUPLAY", "LANDING_PAGE_VIEWS", "PURCHASE", "LISTING_INTERACTION"], "title": "MetaBillingEvent", "type": "string"}, "MetaCallToActionType": {"description": "Meta Ads Call to Action Types", "enum": ["OPEN_LINK", "LIKE_PAGE", "SHOP_NOW", "PLAY_GAME", "INSTALL_APP", "USE_APP", "INSTALL_MOBILE_APP", "USE_MOBILE_APP", "BOOK_TRAVEL", "LISTEN_MUSIC", "WATCH_VIDEO", "LEARN_MORE", "SIGN_UP", "DOWNLOAD", "GET_QUOTE", "CONTACT_US", "APPLY_NOW", "BUY_NOW", "GET_OFFER", "GET_OFFER_VIEW", "BUY_TICKETS", "UPDATE_APP", "GET_DIRECTIONS", "BUY", "DONATE", "SUBSCRIBE", "SAY_THANKS", "SELL_NOW", "SHARE", "DONATE_NOW", "GET_STARTED", "VISIT_PAGES_FEED", "CALL_NOW", "EXPLORE_MORE", "CONFIRM", "JOIN_CHANNEL", "CALL", "REFER_FRIENDS", "MESSAGE_PAGE", "MOBILE_DOWNLOAD", "SAVE", "FOLLOW_PAGE", "WHATSAPP_MESSAGE", "FOLLOW_USER"], "title": "MetaCallToActionType", "type": "string"}, "MetaOptimizationGoal": {"description": "Meta Ads Optimization Goals", "enum": ["NONE", "APP_INSTALLS", "BRAND_AWARENESS", "CLICKS", "ENGAGED_USERS", "EVENT_RESPONSES", "IMPRESSIONS", "LEAD_GENERATION", "LINK_CLICKS", "OFFER_CLAIMS", "OFFSITE_CONVERSIONS", "PAGE_ENGAGEMENT", "PAGE_LIKES", "POST_ENGAGEMENT", "QUALITY_CALL", "REACH", "SOCIAL_IMPRESSIONS", "VIDEO_VIEWS", "THRUPLAY", "LANDING_PAGE_VIEWS", "VALUE", "CONVERSATIONS", "IN_APP_VALUE"], "title": "MetaOptimizationGoal", "type": "string"}}, "description": "Comprehensive schema for gathering ad creation requirements", "properties": {"name": {"description": "Name of the product or brand", "title": "Name", "type": "string"}, "objective": {"description": "Primary product or brand objective for the ad campaign", "title": "Objective", "type": "string"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "geo_locations_countries": {"description": "List of countries to target", "items": {"type": "string"}, "title": "Geo Locations Countries", "type": "array"}, "publisher_platforms": {"description": "List of platforms to target", "items": {"type": "string"}, "title": "Publisher Platforms", "type": "array"}, "facebook_positions": {"description": "List of positions to target on Facebook", "items": {"type": "string"}, "title": "Facebook Positions", "type": "array"}, "optimization_goal": {"$ref": "#/$defs/MetaOptimizationGoal", "description": "What to optimize for"}, "billing_event": {"$ref": "#/$defs/MetaBillingEvent", "description": "What you're charged for"}, "bid_strategy": {"$ref": "#/$defs/MetaBidStrategy", "description": "Bidding strategy"}, "page_id": {"description": "Facebook page ID for the ad", "title": "Page Id", "type": "string"}, "ad_text_message": {"description": "Main text/message of the ad", "title": "Ad Text Message", "type": "string"}, "ad_headline": {"description": "Headline for the ad", "title": "Ad Headline", "type": "string"}, "ad_description": {"description": "Description text for the ad", "title": "Ad Description", "type": "string"}, "call_to_action": {"$ref": "#/$defs/MetaCallToActionType", "description": "Call to action button type"}, "landing_page_url": {"description": "URL where users will be directed", "title": "Landing Page Url", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "URL of the image to use", "title": "Image Url"}}, "required": ["name", "objective", "geo_locations_countries", "publisher_platforms", "facebook_positions", "optimization_goal", "billing_event", "bid_strategy", "page_id", "ad_text_message", "ad_headline", "ad_description", "call_to_action", "landing_page_url"], "title": "AdRequirementGatheringRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_ad", "description": "Create a new ad with an existing creative", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad", "example": {"adset_id": "<adset_id>", "creative_id": "<creative_id>", "name": "Ad 1", "status": "PAUSED"}, "properties": {"name": {"description": "Ad name", "title": "Name", "type": "string"}, "adset_id": {"description": "Ad set ID where this ad will be placed", "title": "Adset Id", "type": "string"}, "creative_id": {"description": "Creative ID to be used for this ad", "title": "Creative Id", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad status", "title": "Status"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Optional bid amount (in cents)", "title": "<PERSON><PERSON>"}, "tracking_specs": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Optional tracking specifications", "title": "Tracking Specs"}}, "required": ["name", "adset_id", "creative_id"], "title": "CreateAdRequest", "type": "object"}, "output_schema": {}, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "social_media", "env_credential_status": "pending_input", "oauth_details": null}, {"id": "b519579c-0698-4928-933e-dfc6bad33f09", "name": "mcp-fetch", "logo": null, "description": "Model context for fetching content from the website along with images", "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "user_ids": null, "owner_type": "user", "config": null, "git_url": "https://github.com/prathamagarwal07/mcp-fetch", "git_branch": "main", "deployment_status": "pending", "visibility": "private", "tags": null, "status": "active", "created_at": "2025-07-09T14:01:24.307334", "updated_at": "2025-07-09T14:01:24.307459", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": []}, "is_added": false, "is_quick_tool": false, "env_keys": null, "component_category": "communication", "env_credential_status": "not_required", "oauth_details": null}, {"id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "name": "Jira & Confluence", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/jira.png/1751622867-jira.png", "description": "Jira & Confluence mcp for managing the tickets and pages", "owner_id": "54c1059e-c891-4163-bbe6-1e5ad75601e1", "user_ids": null, "owner_type": "user", "config": [{"url": "https://jira-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-07-04T05:58:16.021482", "updated_at": "2025-07-23T08:03:45.050573", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "update_issue", "description": "Update fields and/or change the status of a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue (e.g., 'TEST-48')."}, "summary": {"type": "string", "description": "New title (optional)."}, "description": {"type": "string", "description": "New plain text description (optional)."}, "priority": {"type": "string", "description": "New priority name (optional)."}, "assignee": {"type": "string", "description": "Assignee email or '' to unassign (optional)."}, "storyPoints": {"type": "number", "description": "New story point value (optional)."}, "sprintId": {"type": "number", "description": "Sprint ID to assign issue (optional)."}, "transitionTo": {"type": "string", "description": "Target status name (optional)."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {"properties": {"ghvvghv": {"type": "string", "description": "vghgvh", "title": "ghvvghv"}}}, "annotations": null}, {"name": "create_issue", "description": "Create a new Jira issue.", "input_schema": {"type": "object", "properties": {"summary": {"type": "string", "description": "A concise title for the issue."}, "description": {"type": "string", "description": "A detailed plain text description of the issue."}, "issueType": {"type": "string", "description": "Type of issue (e.g., 'Task')."}, "projectKey": {"type": "string", "description": "Jira project key (e.g., 'TEST')."}, "priority": {"type": "string", "description": "Priority of the issue (e.g., 'High', 'Medium', 'Low'). Case-insensitive."}, "assignee": {"type": "string", "description": "Email, username, or account ID of the user to assign the issue to (optional)."}, "storyPoints": {"type": "number", "description": "Optional story point estimate for the issue (e.g., 3, 5, 8)."}, "sprintId": {"type": "number", "description": "Optional Sprint ID to add the issue to an active sprint."}}, "required": ["summary", "issueType", "projectKey"]}, "output_schema": null, "annotations": null}, {"name": "get_issue", "description": "Get a Jira issue by key.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Jira issue key (e.g., 'TEST-1')"}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": null, "annotations": null}, {"name": "list_projects", "description": "List all Jira projects accessible to the user.", "input_schema": {"type": "object", "properties": {}}, "output_schema": null, "annotations": null}, {"name": "delete_issue", "description": "Delete a Jira issue or subtask", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to delete"}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": null, "annotations": null}, {"name": "search_issues", "description": "Search for issues in a project using JQL", "input_schema": {"type": "object", "properties": {"projectKey": {"type": "string", "description": "Project key (e.g., \"MRR\")"}, "jql": {"type": "string", "description": "JQL filter statement (e.g., status = 'To Do' AND priority = 'High')"}}, "required": ["projectKey", "jql"]}, "output_schema": {"properties": {"ghvvvh": {"type": "string", "description": "hjbbhjb", "title": "ghvvvh"}}}, "annotations": null}, {"name": "attach_file", "description": "Attaches a file to an existing Jira issue from a local path or a URL.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to attach the file to (e.g., 'TEST-34')."}, "filepath": {"type": "string", "description": "Local file path of the file to attach. Optional if 'file_url' is provided."}, "file_url": {"type": "string", "description": "URL of the file to attach. Optional if 'filepath' is provided."}}, "required": ["issue<PERSON><PERSON>"], "oneOf": [{"required": ["filepath"]}, {"required": ["file_url"]}]}, "output_schema": null, "annotations": null}, {"name": "transition_issue", "description": "Transitions a Jira issue to a new status (e.g., 'In Progress', 'Done').", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to transition (e.g., 'TEST-34')."}, "statusName": {"type": "string", "description": "The name of the transition action. IMPORTANT: This is the name of the action button you click in the Jira UI. It is case-sensitive and must match exactly. Common examples: 'Done', 'In Progress', 'To Do', 'Backlog'. Check the issue in Jira to see the available actions from its current status."}}, "required": ["issue<PERSON><PERSON>", "statusName"]}, "output_schema": {"properties": {"ghvvh": {"type": "string", "description": "hjjh", "title": "ghvvh"}}}, "annotations": null}, {"name": "list_available_transitions", "description": "Lists all possible transition actions for a given Jira issue from its current status.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to check (e.g., 'TEST-34')."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": null, "annotations": null}, {"name": "add_comment", "description": "Add a comment to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to comment on (e.g., 'TEST-123')."}, "comment": {"type": "string", "description": "The plain text content of the comment."}}, "required": ["issue<PERSON><PERSON>", "comment"]}, "output_schema": null, "annotations": null}, {"name": "add_comment_with_attachment", "description": "Add a comment with a file attachment to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue."}, "comment": {"type": "string", "description": "The plain text content of the comment."}, "filename": {"type": "string", "description": "Name of the file to be attached."}, "filepath": {"type": "string", "description": "Local path to the file to attach."}}, "required": ["issue<PERSON><PERSON>", "comment", "filename", "filepath"]}, "output_schema": null, "annotations": null}, {"name": "attach_content", "description": "Creates a file from provided content and attaches it to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to attach the content to."}, "filename": {"type": "string", "description": "The name of the attachment file as it will appear in Jira."}, "content": {"type": "string", "description": "The content to include in the attachment."}, "encoding": {"type": "string", "description": "Encoding of the content. Use 'base64' for binary data, 'none' for plain text.", "enum": ["none", "base64"], "default": "none"}}, "required": ["issue<PERSON><PERSON>", "filename", "content"]}, "output_schema": {"properties": {"hgvvh": {"type": "string", "description": "jhbbhj", "title": "hgvvh"}}}, "annotations": null}, {"name": "create_issue_link", "description": "Create a link between two existing Jira issues.", "input_schema": {"type": "object", "properties": {"inwardIssueKey": {"type": "string", "description": "The key of the issue that the link points TO. For a 'Blocks' link, this is the issue that IS BLOCKED."}, "outwardIssueKey": {"type": "string", "description": "The key of the issue that the link points FROM. For a 'Blocks' link, this is the issue that IS BLOCKING."}, "linkType": {"type": "string", "description": "The name of the link type (e.g., 'Blocks', 'Relates', 'Duplicates'). This is case-sensitive and must match a valid link type in your Jira instance."}}, "required": ["inwardIssueKey", "outwardIssueKey", "linkType"]}, "output_schema": null, "annotations": null}, {"name": "get_user", "description": "Get a user's account ID by email address", "input_schema": {"type": "object", "properties": {"email": {"type": "string", "description": "User's email address"}}, "required": ["email"]}, "output_schema": null, "annotations": null}, {"name": "get_my_permissions", "description": "Checks the abilities of the current API key by listing the permissions of the user account that owns the key. This is how you check the 'scopes' or 'limits' of your API token.", "input_schema": {"type": "object", "properties": {"projectKey": {"type": "string", "description": "Optional. The key of a project to check permissions for (e.g., 'TEST'). If you omit this, it will only check global permissions (like 'JIRA Administrator')."}}, "required": []}, "output_schema": null, "annotations": null}, {"name": "update_start_end_date_time", "description": "Update the start and end date of a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to update (e.g., 'TEST-48')."}, "startDate": {"type": "string", "description": "New start date in YYYY-MM-DD format (optional)."}, "endDate": {"type": "string", "description": "New end date in YYYY-MM-DD format (optional)."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {"properties": {"ghgvhgh": {"type": "string", "description": "jvghvhb", "title": "ghgvhgh"}}}, "annotations": null}, {"name": "add_worklog", "description": "Adds a worklog to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to add the worklog to (e.g., 'TEST-48')."}, "timeSpent": {"type": "string", "description": "The amount of time spent, e.g., '1w 2d 3h 4m'."}, "comment": {"type": "string", "description": "A comment to add to the worklog (optional)."}}, "required": ["issue<PERSON><PERSON>", "timeSpent"]}, "output_schema": null, "annotations": null}, {"name": "list_sprints", "description": "List all sprints for a given Jira board.", "input_schema": {"type": "object", "properties": {"boardId": {"type": "string", "description": "The ID of the board to get sprints from."}}, "required": ["boardId"]}, "output_schema": null, "annotations": null}, {"name": "update_sprint_goal", "description": "Update the goal of a sprint.", "input_schema": {"type": "object", "properties": {"sprintId": {"type": "string", "description": "The ID of the sprint to update."}, "goal": {"type": "string", "description": "The new goal for the sprint."}}, "required": ["sprintId", "goal"]}, "output_schema": {"properties": {"hgvvhv": {"type": "string", "description": "jhvgvhvhv", "title": "hgvvhv"}}}, "annotations": null}, {"name": "list_boards", "description": "List all Jira boards accessible to the user.", "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": null, "annotations": null}, {"name": "get_all_sprints", "description": "Get all sprints across all Jira boards accessible to the user.", "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": null, "annotations": null}, {"name": "get_sprint_by_id", "description": "Get details of a specific Jira sprint by its ID.", "input_schema": {"type": "object", "properties": {"sprintId": {"type": "string", "description": "The ID of the sprint to retrieve."}}, "required": ["sprintId"]}, "output_schema": null, "annotations": null}, {"name": "get_confluence_spaces", "description": "Get all Confluence spaces.", "input_schema": {"type": "object", "properties": {"limit": {"type": "integer", "description": "Maximum number of spaces to return", "default": 30}}}, "output_schema": null, "annotations": null}, {"name": "get_confluence_page", "description": "Get a Confluence page by ID.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "create_confluence_page", "description": "Create a new Confluence page.", "input_schema": {"type": "object", "properties": {"spaceId": {"type": "string", "description": "Confluence space ID. Either spaceId or spaceKey is required."}, "spaceKey": {"type": "string", "description": "Confluence space key. Either spaceId or spaceKey is required."}, "title": {"type": "string", "description": "Page title"}, "body": {"type": "string", "description": "Page content in storage format"}}, "required": ["title", "body"]}, "output_schema": null, "annotations": null}, {"name": "confluence_search", "description": "Search Confluence content using simple terms or CQL (Confluence Query Language).", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query or CQL expression"}}, "required": ["query"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_page_children", "description": "Get the list of child pages for a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_page_ancestors", "description": "Retrieve the ancestor (parent) pages of a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_comments", "description": "Get all comments associated with a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_update_page", "description": "Update the content or metadata of an existing Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}, "title": {"type": "string", "description": "New page title"}, "body": {"type": "string", "description": "New page content in storage format"}}, "required": ["pageId"]}, "output_schema": {"properties": {"Abc": {"type": "string", "description": "xyz", "title": "Abc"}}}, "annotations": null}, {"name": "confluence_delete_page", "description": "Delete a Confluence page permanently.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": {"properties": {"hjvh": {"type": "string", "description": "jhbbjh", "title": "hjvh"}}}, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "notifications_alerts", "env_credential_status": "pending_input", "oauth_details": {"provider": "jira", "tool_name": "jira"}}, {"id": "796faf92-17eb-4c8f-bd22-185418425862", "name": "Website Generator", "logo": null, "description": "Website Generator MCP Server.", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": null, "owner_type": "user", "config": [{"image_name": "nikhil-patil-ri_website-generator", "type": "stdio"}], "git_url": "https://github.com/<PERSON><PERSON>-Patil-RI/website-generator", "git_branch": "main", "deployment_status": "completed", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-07-03T11:41:52.575486", "updated_at": "2025-07-24T16:59:29.371058", "image_name": "nikhil-patil-ri_website-generator", "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "repo_setup", "description": "\n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. <PERSON>lone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    ", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "deploy_to_amplify": {"default": false, "title": "Deploy To Amplify", "type": "boolean"}}, "required": ["project_name"], "title": "repo_setupArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "repo_setupOutput", "type": "object"}, "annotations": null}, {"name": "create_file", "description": "\n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    ", "input_schema": {"properties": {"file_name": {"title": "File Name", "type": "string"}, "file_path": {"title": "File Path", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["file_name", "file_path", "content"], "title": "create_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "create_fileOutput", "type": "object"}, "annotations": null}, {"name": "push_changes", "description": "\n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    ", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}}, "required": ["project_name"], "title": "push_changesArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "push_changesOutput", "type": "object"}, "annotations": null}, {"name": "read_file", "description": "\n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    ", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}}, "required": ["file_path"], "title": "read_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "read_fileOutput", "type": "object"}, "annotations": null}, {"name": "list_files", "description": "\n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    ", "input_schema": {"properties": {"directory_path": {"title": "Directory Path", "type": "string"}}, "required": ["directory_path"], "title": "list_filesArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "list_filesOutput", "type": "object"}, "annotations": null}, {"name": "update_file", "description": "\n    Update the contents of an existing file.\n\n    This tool overwrites the entire content of an existing file with new content.\n    The file must already exist - use create_file to create new files.\n\n    Args:\n        file_path: Path to the file to update (can be relative or absolute)\n        new_content: New content to write to the file\n\n    Returns:\n        Success message or error message\n    ", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}, "new_content": {"title": "New Content", "type": "string"}}, "required": ["file_path", "new_content"], "title": "update_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "update_fileOutput", "type": "object"}, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": [{"key": "GITHUB_TOKEN", "description": "Github Personal access token"}], "component_category": null, "env_credential_status": "pending_input", "oauth_details": null}, {"id": "f506eb7c-a648-4873-8055-e2273be08478", "name": "Code-Runner-Mcp", "logo": null, "description": "Used to execute the code and returns the output. Now only supports Javascript. \nhttps://smithery.ai/server/@dravidsajinraj-iex/code-runner-mcp ", "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "user_ids": null, "owner_type": "user", "config": [{"url": "https://server.smithery.ai/@dravid<PERSON>jinraj-iex/code-runner-mcp/mcp?api_key=2bf9a5dd-cc32-424f-a0c3-cdc9031c3799&profile=mechanical-snail-g1RifI", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": ["code", "code executor", "code runner"], "status": "active", "created_at": "2025-07-02T10:35:41.180971", "updated_at": "2025-07-18T07:38:50.069861", "image_name": null, "category": "engineering", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "execute_code", "description": "Execute JavaScript or Python code securely with comprehensive error handling and security measures", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "input": {"type": "string", "description": "Input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"success": {"type": "boolean"}, "output": {"type": "string"}, "errorOutput": {"type": "string"}, "returnValue": {"type": "string"}, "executionTime": {"type": "number"}, "memoryUsed": {"type": "number"}, "language": {"type": "string"}}, "required": ["success", "output", "errorOutput", "returnValue", "executionTime", "memoryUsed", "language"], "additionalProperties": false}, "annotations": null}, {"name": "execute_code_with_variables", "description": "Execute JavaScript or Python code with dynamic input variables that can be defined and passed as key-value pairs", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "variables": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "string"}], "description": "Dynamic input variables as key-value pairs. Can be a JSON object or a JSON string (e.g., {\"name\": \"<PERSON>\", \"age\": 25, \"items\": [1,2,3]} or \"{\\\"name\\\": \\\"John\\\", \\\"age\\\": 25}\")"}, "input": {"type": "string", "description": "Additional input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "get_capabilities", "description": "Get information about supported languages and execution capabilities", "input_schema": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "validate_code", "description": "Validate code for security and syntax issues without executing it", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language"}, "code": {"type": "string", "description": "Code to validate"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": null, "env_credential_status": "pending_input", "oauth_details": null}, {"id": "a4dbc53f-af77-416f-9849-c82f0411695b", "name": "Webflow", "logo": null, "description": "Webflow Server", "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "user_ids": null, "owner_type": "user", "config": [{"url": "https://server.smithery.ai/@webflow/mcp-server/mcp?profile=mechanical-snail-g1RifI&api_key=2bf9a5dd-cc32-424f-a0c3-cdc9031c3799", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": ["webflow", "blogs", "blog publisher"], "status": "active", "created_at": "2025-06-26T13:28:29.755749", "updated_at": "2025-07-10T07:53:15.635198", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "collection_fields_create_option", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "const": "Option"}, "displayName": {"type": "string"}, "helpText": {"type": "string"}, "metadata": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}}, "required": ["options"], "additionalProperties": false}}, "required": ["type", "displayName", "metadata"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_create_reference", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "enum": ["MultiReference", "Reference"]}, "displayName": {"type": "string"}, "helpText": {"type": "string"}, "metadata": {"type": "object", "properties": {"collectionId": {"type": "string"}}, "required": ["collectionId"], "additionalProperties": false}}, "required": ["type", "displayName", "metadata"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_update", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "field_id": {"type": "string"}, "request": {"type": "object", "properties": {"isRequired": {"type": "boolean"}, "displayName": {"type": "string"}, "helpText": {"type": "string"}}, "additionalProperties": false}}, "required": ["collection_id", "field_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_create_item_live", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_update_items_live", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}}]}}, "required": ["id"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_list_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "offset": {"type": "number"}, "limit": {"type": "number"}, "name": {"type": "string"}, "slug": {"type": "string"}, "sortBy": {"type": "string", "enum": ["lastPublished", "name", "slug"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}, "required": ["collection_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_create_item", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_update_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["id", "fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_publish_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "itemIds": {"type": "array", "items": {"type": "string"}}}, "required": ["collection_id", "itemIds"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "sites_list", "description": null, "input_schema": {"type": "object"}, "output_schema": null, "annotations": null}, {"name": "sites_get", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"sites": {"type": "array", "description": "List of Webflow sites", "title": "sites"}}}, "annotations": null}, {"name": "sites_publish", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "customDomains": {"type": "array", "items": {"type": "string"}}, "publishToWebflowSubdomain": {"type": "boolean", "default": false}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_list", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "localeId": {"type": "string"}, "limit": {"type": "number"}, "offset": {"type": "number"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_get_metadata", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}}, "required": ["page_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_update_page_settings", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "body": {"type": "object", "properties": {"id": {"type": "string"}, "siteId": {"type": "string"}, "title": {"type": "string"}, "slug": {"type": "string"}, "parentId": {"type": "string"}, "collectionId": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "lastUpdated": {"type": "string", "format": "date-time"}, "archived": {"type": "boolean"}, "draft": {"type": "boolean"}, "canBranch": {"type": "boolean"}, "isBranch": {"type": "boolean"}, "isMembersOnly": {"type": "boolean"}, "seo": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "additionalProperties": false}, "openGraph": {"type": "object", "properties": {"title": {"type": "string"}, "titleCopied": {"type": "boolean"}, "description": {"type": "string"}, "descriptionCopied": {"type": "boolean"}}, "additionalProperties": false}, "localeId": {"type": "string"}, "publishedPath": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["page_id", "body"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_get_content", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "limit": {"type": "number"}, "offset": {"type": "number"}}, "required": ["page_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_update_static_content", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "nodes": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"nodeId": {"type": "string"}, "text": {"type": "string"}}, "required": ["nodeId", "text"], "additionalProperties": false}, {"type": "object", "properties": {"nodeId": {"type": "string"}, "propertyOverrides": {"type": "array", "items": {"type": "object", "properties": {"propertyId": {"type": "string"}, "text": {"type": "string"}}, "required": ["propertyId", "text"], "additionalProperties": false}}}, "required": ["nodeId", "propertyOverrides"], "additionalProperties": false}]}}}, "required": ["page_id", "localeId", "nodes"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_list", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_get", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}}, "required": ["collection_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_create", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "request": {"type": "object", "properties": {"displayName": {"type": "string"}, "singularName": {"type": "string"}, "slug": {"type": "string"}}, "required": ["displayName", "singularName"], "additionalProperties": false}}, "required": ["site_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_create_static", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "enum": ["Color", "DateTime", "Email", "File", "Image", "Link", "MultiImage", "Number", "Phone", "PlainText", "RichText", "Switch", "Video"]}, "displayName": {"type": "string"}, "helpText": {"type": "string"}}, "required": ["type", "displayName"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "social_media", "env_credential_status": "pending_input", "oauth_details": null}, {"id": "06df097c-d38f-443b-a57b-886a9b14b1b2", "name": "mem0-mcp", "logo": null, "description": "Store and retrieve user-specific memories to maintain context and make informed decisions based on past interactions. Utilize a simple API to add and search memories with relevance scoring, enhancing ", "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "user_ids": null, "owner_type": "user", "config": [{"url": "https://server.smithery.ai/@mem0ai/mem0-memory-mcp/mcp?api_key=6495e262-6eb8-4845-bbb8-ca2db907d7ea&profile=weekly-limpet-QXWEEZ", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": ["Memory Storage"], "status": "active", "created_at": "2025-06-26T12:11:24.317116", "updated_at": "2025-07-17T10:17:25.912151", "image_name": null, "category": "engineering", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "add-memory", "description": "Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevent information whcih can be useful in the future conversation. This can also be called when the user asks you to remember something.", "input_schema": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to store in memory"}, "userId": {"type": "string", "description": "User ID for memory storage. If not provided explicitly, use a generic user ID like, 'mem0-mcp-user'"}}, "required": ["content", "userId"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}, "annotations": null}, {"name": "search-memories", "description": "Search through stored memories. This method is called ANYTIME the user asks anything.", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query. This is the query that the user has asked for. Example: 'What did I tell you about the weather last week?' or 'What did I tell you about my friend <PERSON>?'"}, "userId": {"type": "string", "description": "User ID for memory storage. If not provided explicitly, use a generic user ID like, 'mem0-mcp-user"}}, "required": ["query", "userId"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "database", "env_credential_status": "pending_input", "oauth_details": null}, {"id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "name": "Google Sheets", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Sheets_Logo_512px.png/1750857479-Google_Sheets_Logo_512px.png", "description": "This MCP server integrates with your Google Sheets, to enable creating and modifying spreadsheets.", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": null, "owner_type": "user", "config": [{"url": "https://google-sheets-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-06-25T13:16:18.995231", "updated_at": "2025-07-25T05:36:11.945370", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_column", "description": "Create a new column in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Column Index"}, "values": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Values"}}, "required": ["spreadsheet_id"], "title": "CreateColumn", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_cell", "description": "Update a cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_cell", "description": "Fetch the contents of a specific cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}}, "required": ["spreadsheet_id", "cell"], "title": "GetCell", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_empty_row", "description": "Find the first empty row in a worksheet starting from a specific row", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Start Row"}, "search_column": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "A", "title": "Search Column"}}, "required": ["spreadsheet_id"], "title": "FindEmptyRow", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "set_formula", "description": "Set a formula in a specific cell of a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "formula": {"title": "Formula", "type": "string"}}, "required": ["spreadsheet_id", "cell", "formula"], "title": "SetForm<PERSON>", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "insert_row", "description": "Insert a single row at the specified index, shifting existing rows down", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"title": "Row Index", "type": "integer"}}, "required": ["spreadsheet_id", "row", "row_index"], "title": "InsertRow", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "insert_multiple_rows", "description": "Insert multiple rows at the specified index, shifting existing rows down", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "rows": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Rows", "type": "array"}, "start_row_index": {"title": "Start Row Index", "type": "integer"}}, "required": ["spreadsheet_id", "rows", "start_row_index"], "title": "InsertMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "count_column_values", "description": "Count the total number of values in a specific column", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column": {"title": "Column", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Value"}, "include_empty": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Empty"}}, "required": ["spreadsheet_id", "column"], "title": "CountCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_row", "description": "Find one or more rows by a column and value", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "add_multiple_rows", "description": "Add multiple rows of data to a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "rows": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Rows", "type": "array"}, "start_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Start Row"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "rows"], "title": "AddMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_single_row", "description": "Add a single row of data to Google Sheets", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "clear_cell", "description": "Delete the content of a specific cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}}, "required": ["spreadsheet_id", "cell"], "title": "ClearCell", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "clear_rows", "description": "Delete the content of a row or rows in a spreadsheet. Deleted rows will appear as blank rows", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"title": "Start Row", "type": "integer"}, "end_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "End Row"}}, "required": ["spreadsheet_id", "start_row"], "title": "ClearRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_spreadsheet", "description": "Create a blank spreadsheet or duplicate an existing spreadsheet", "input_schema": {"properties": {"title": {"title": "Title", "type": "string"}, "worksheet_titles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Worksheet Titles"}}, "required": ["title"], "title": "CreateSpreadsheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "insert_anchored_note", "description": "Insert a note on a spreadsheet cell", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "note_text": {"title": "Note Text", "type": "string"}}, "required": ["spreadsheet_id", "cell", "note_text"], "title": "InsertAnchoredNote", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_rows", "description": "Deletes the specified rows from a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"title": "Start Row", "type": "integer"}, "end_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "End Row"}}, "required": ["spreadsheet_id", "start_row"], "title": "DeleteRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "insert_comment", "description": "Insert a comment into a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "comment_text": {"title": "Comment Text", "type": "string"}}, "required": ["spreadsheet_id", "cell", "comment_text"], "title": "InsertComment", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_worksheets", "description": "Get a list of all worksheets in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}}, "required": ["spreadsheet_id"], "title": "ListWorksheets", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_multiple_rows", "description": "Update multiple rows in a spreadsheet defined by a range", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "range_notation": {"title": "Range Notation", "type": "string"}, "values": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Values", "type": "array"}}, "required": ["spreadsheet_id", "range_notation", "values"], "title": "UpdateMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_worksheet", "description": "Delete a specific worksheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"title": "Worksheet Name", "type": "string"}}, "required": ["spreadsheet_id", "worksheet_name"], "title": "DeleteWorksheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_spreadsheet_by_id", "description": "Returns the spreadsheet at the given ID", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "include_grid_data": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Grid Data"}}, "required": ["spreadsheet_id"], "title": "GetSpreadsheetById", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_values_in_range", "description": "Get all values or values from a range of cells using A1 notation", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "range_notation": {"title": "Range Notation", "type": "string"}, "value_render_option": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "FORMATTED_VALUE", "title": "Value Render Option"}}, "required": ["spreadsheet_id", "range_notation"], "title": "GetValuesInRange", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_worksheet", "description": "Create a blank worksheet with a title", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "title": {"title": "Title", "type": "string"}, "row_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1000, "title": "Row Count"}, "column_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 26, "title": "Column Count"}}, "required": ["spreadsheet_id", "title"], "title": "CreateWorksheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upsert_row", "description": "Upsert a row of data in a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "key_column": {"title": "Key Column", "type": "string"}, "key_value": {"title": "Key Value", "type": "string"}}, "required": ["spreadsheet_id", "row", "key_column", "key_value"], "title": "UpsertRow", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "copy_worksheet", "description": "Copy an existing worksheet to another Google Sheets file", "input_schema": {"properties": {"source_spreadsheet_id": {"title": "Source Spreadsheet Id", "type": "string"}, "source_worksheet_name": {"title": "Source Worksheet Name", "type": "string"}, "destination_spreadsheet_id": {"title": "Destination Spreadsheet Id", "type": "string"}, "destination_worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Destination Worksheet Name"}}, "required": ["source_spreadsheet_id", "source_worksheet_name", "destination_spreadsheet_id"], "title": "CopyWorksheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_row", "description": "Update a row in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row_index": {"title": "Row Index", "type": "integer"}, "values": {"items": {"type": "string"}, "title": "Values", "type": "array"}}, "required": ["spreadsheet_id", "row_index", "values"], "title": "UpdateRow", "type": "object"}, "output_schema": null, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "cloud_storage", "env_credential_status": "pending_input", "oauth_details": {"provider": "google", "tool_name": "google_sheets"}}, {"id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "name": "Gmail", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/gmail.png/**********-gmail.png", "description": "The server provides tools to retrieve, read, send, view, and remove emails.", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": null, "owner_type": "user", "config": [{"url": "https://gmail-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-06-25T09:53:04.457157", "updated_at": "2025-07-24T06:16:10.874952", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "archive_email", "description": "Archive an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}}, "required": ["message_id"], "title": "ArchiveEmail", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "delete_email", "description": "Send an email message to the trash", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}}, "required": ["message_id"], "title": "DeleteEmail", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_draft", "description": "Create a draft email message", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "CreateDraft", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_draft_reply", "description": "Create a draft reply to an existing email", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "body": {"title": "Body", "type": "string"}, "include_original": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Include Original"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["message_id", "body"], "title": "CreateDraftReply", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_label", "description": "Create a new label in Gmail", "input_schema": {"properties": {"label_name": {"title": "Label Name", "type": "string"}, "label_list_visibility": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "labelShow", "title": "Label List Visibility"}, "message_list_visibility": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "show", "title": "Message List Visibility"}}, "required": ["label_name"], "title": "CreateLabel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "remove_label_from_email", "description": "Remove a label from an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["message_id"], "title": "RemoveLabelFromEmail", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "remove_label_from_conversation", "description": "Remove a specified label from all emails within a conversation", "input_schema": {"properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["conversation_id"], "title": "RemoveLabelFromConversation", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "reply_to_email", "description": "Send a reply to an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "body": {"title": "Body", "type": "string"}, "include_original": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Include Original"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["message_id", "body"], "title": "ReplyToEmail", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "find_email", "description": "Find an email message based on search query", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["query"], "title": "FindEmail", "type": "object"}, "output_schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful"}, "message": {"type": "string", "description": "Human-readable message about the result"}, "query": {"type": "string", "description": "Search query that was used to fetch the emails"}, "total_results": {"type": "integer", "description": "Total number of emails found"}, "emails": {"type": "array", "description": "List of emails matching the query", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique ID of the email"}, "threadId": {"type": "string", "description": "ID of the thread the email belongs to"}, "labelIds": {"type": "array", "description": "Labels applied to the email", "items": {"type": "string"}}, "snippet": {"type": "string", "description": "Preview snippet of the email content"}, "headers": {"type": "object", "description": "Standard email headers", "properties": {"to": {"type": "string", "description": "Recipient email address"}, "from": {"type": "string", "description": "Sender email address"}, "subject": {"type": "string", "description": "Subject of the email"}, "Date": {"type": "string", "description": "Date the email was sent"}}, "required": ["subject", "Date", "From", "to"]}, "internalDate": {"type": "string", "description": "Internal timestamp of the email (epoch millis as string)"}}, "required": ["id", "threadId", "labelIds", "snippet", "headers", "internalDate"]}}}, "required": ["success", "message", "emails", "query", "total_results"]}, "annotations": null}, {"name": "find_or_send_email", "description": "Find a specific message or create and send a new one if not found", "input_schema": {"properties": {"search_query": {"title": "Search Query", "type": "string"}, "to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["search_query", "to", "subject", "body"], "title": "FindOrSendEmail", "type": "object"}, "output_schema": {"properties": {"Design": {"type": "string", "description": "Design Mail", "title": "Design"}}}, "annotations": null}, {"name": "add_label_to_email", "description": "Add a label to an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["message_id"], "title": "AddLabelToEmail", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "send_email", "description": "Create and send a new email message", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "SendEmail", "type": "object"}, "output_schema": {"properties": {"Design": {"type": "string", "description": "Design Mail\n", "title": "Design"}}}, "annotations": null}]}, "is_added": true, "is_quick_tool": false, "env_keys": null, "component_category": "notifications_alerts", "env_credential_status": "pending_input", "oauth_details": {"provider": "google", "tool_name": "gmail"}}], "metadata": {"total": 30, "totalPages": 3, "currentPage": 1, "pageSize": 10, "hasNextPage": true, "hasPreviousPage": false}}