import React from 'react';

/**
 * Utility functions to handle modal cleanup and prevent overlay issues
 */

/**
 * Comprehensive cleanup function that addresses all potential causes of unclickable pages
 */
export function comprehensiveModalCleanup(): void {
  console.log('Starting comprehensive modal cleanup...');
  
  let cleanedCount = 0;

  // 1. Clean up Radix UI dialog overlays
  const dialogOverlays = document.querySelectorAll('[data-radix-dialog-overlay]');
  dialogOverlays.forEach(overlay => {
    if (overlay && !overlay.hasAttribute('data-state-open')) {
      overlay.remove();
      cleanedCount++;
    }
  });

  // 2. Clean up Radix UI alert dialog overlays
  const alertDialogOverlays = document.querySelectorAll('[data-radix-alert-dialog-overlay]');
  alertDialogOverlays.forEach(overlay => {
    if (overlay && !overlay.hasAttribute('data-state-open')) {
      overlay.remove();
      cleanedCount++;
    }
  });

  // 3. Clean up any other potential overlay elements
  const potentialOverlays = document.querySelectorAll('[role="dialog"], [role="alertdialog"]');
  potentialOverlays.forEach(overlay => {
    if (overlay && !overlay.hasAttribute('data-state-open') && !overlay.hasAttribute('aria-hidden')) {
      const computedStyle = window.getComputedStyle(overlay);
      if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
        overlay.remove();
        cleanedCount++;
      }
    }
  });

  // 4. Remove any fixed positioned elements that might be blocking interactions
  const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
  fixedElements.forEach(element => {
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'fixed' && 
        computedStyle.top === '0px' && 
        computedStyle.left === '0px' && 
        computedStyle.width === '100%' && 
        computedStyle.height === '100%' &&
        !element.hasAttribute('data-state-open')) {
      element.remove();
      cleanedCount++;
    }
  });

  // 5. Remove any elements with high z-index that might be blocking
  const highZIndexElements = document.querySelectorAll('*');
  highZIndexElements.forEach(element => {
    const computedStyle = window.getComputedStyle(element);
    const zIndex = parseInt(computedStyle.zIndex);
    if (zIndex > 1000 && 
        computedStyle.position === 'fixed' && 
        !element.hasAttribute('data-state-open') &&
        !element.closest('[data-radix-dialog-content]') &&
        !element.closest('[data-radix-alert-dialog-content]')) {
      element.remove();
      cleanedCount++;
    }
  });

  // 6. Clean up body styles that might be causing issues
  const body = document.body;
  const originalOverflow = body.style.overflow;
  const originalPaddingRight = body.style.paddingRight;
  const originalPointerEvents = body.style.pointerEvents;

  // Reset body styles
  body.style.overflow = '';
  body.style.paddingRight = '';
  body.style.pointerEvents = '';

  // 7. Remove any data-radix-scroll-area-viewport elements that might be blocking
  const scrollAreaViewports = document.querySelectorAll('[data-radix-scroll-area-viewport]');
  scrollAreaViewports.forEach(viewport => {
    const computedStyle = window.getComputedStyle(viewport);
    if (computedStyle.position === 'fixed' && !viewport.hasAttribute('data-state-open')) {
      viewport.remove();
      cleanedCount++;
    }
  });

  // 8. Clean up any portal containers that might be lingering
  const portalContainers = document.querySelectorAll('[data-radix-portal]');
  portalContainers.forEach(container => {
    if (!container.hasAttribute('data-state-open') && container.children.length === 0) {
      container.remove();
      cleanedCount++;
    }
  });

  // 9. Force a reflow to ensure changes take effect
  body.offsetHeight;

  if (cleanedCount > 0) {
    console.log(`Comprehensive modal cleanup: Removed ${cleanedCount} elements and reset body styles`);
  } else {
    console.log('Comprehensive modal cleanup: No elements to clean up');
  }

  // 10. Log the current state for debugging
  console.log('Current body styles after cleanup:', {
    overflow: body.style.overflow,
    paddingRight: body.style.paddingRight,
    pointerEvents: body.style.pointerEvents
  });
}

/**
 * Clean up any lingering modal overlays that might be blocking user interactions
 */
export function cleanupModalOverlays(): void {
  comprehensiveModalCleanup();
}

/**
 * Function to ensure page interactivity on every click
 * This can be called to prevent any blocking elements from interfering
 */
export function ensurePageInteractivity(): void {
  // Check if there are any blocking elements
  const blockingElements = document.querySelectorAll('[data-radix-dialog-overlay], [data-radix-alert-dialog-overlay]');
  const hasBlockingElements = Array.from(blockingElements).some(element => 
    !element.hasAttribute('data-state-open')
  );
  
  if (hasBlockingElements) {
    console.log('Blocking elements detected, performing cleanup...');
    comprehensiveModalCleanup();
  }
}

/**
 * Set up global event listeners to handle modal cleanup
 */
export function setupModalCleanupListeners(): () => void {
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      comprehensiveModalCleanup();
    }
  };

  const handleClick = (event: MouseEvent) => {
    // If clicking on a backdrop-like element, clean up
    const target = event.target as Element;
    if (target && 
        target.hasAttribute('data-radix-dialog-overlay') || 
        target.hasAttribute('data-radix-alert-dialog-overlay')) {
      // Small delay to ensure the modal close animation completes
      setTimeout(() => {
        comprehensiveModalCleanup();
      }, 100);
    }
    
    // Ensure page interactivity on every click
    ensurePageInteractivity();
  };

  // Specific handler for Cancel button clicks
  const handleCancelClick = (event: MouseEvent) => {
    const target = event.target as Element;
    if (target && target.textContent?.toLowerCase().includes('cancel')) {
      console.log('Cancel button clicked, triggering cleanup...', {
        buttonText: target.textContent,
        element: target,
        timestamp: new Date().toISOString()
      });
      // Immediate cleanup
      comprehensiveModalCleanup();
      // Additional cleanup after animation
      setTimeout(() => {
        console.log('Performing delayed cleanup after Cancel button click...');
        comprehensiveModalCleanup();
      }, 100);
    }
  };

  // Add event listeners
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('click', handleClick);
  document.addEventListener('click', handleCancelClick, true); // Use capture phase

  // Return cleanup function
  return () => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('click', handleClick);
    document.removeEventListener('click', handleCancelClick, true);
  };
}

/**
 * Hook to use modal cleanup in React components
 */
export function useModalCleanup() {
  React.useEffect(() => {
    const cleanup = setupModalCleanupListeners();
    
    // Add mutation observer to monitor for blocking elements
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check if any added nodes might be blocking
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              const computedStyle = window.getComputedStyle(element);
              
              // Check if this element might be blocking interactions
              if (computedStyle.position === 'fixed' && 
                  computedStyle.zIndex && parseInt(computedStyle.zIndex) > 1000 &&
                  !element.hasAttribute('data-state-open')) {
                console.log('Mutation observer detected potential blocking element:', element);
                // Don't remove immediately, just log for now
              }
            }
          });
        }
      });
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-state', 'style']
    });
    
    return () => {
      cleanup();
      observer.disconnect();
    };
  }, []);

  return { cleanupModalOverlays: comprehensiveModalCleanup };
}

/**
 * Force cleanup and restore page interactivity
 */
export function forceRestorePageInteractivity(): void {
  comprehensiveModalCleanup();
  console.log('Modal cleanup completed - page interactivity restored');
}

/**
 * Enhanced cleanup specifically for workflow list modals
 */
export function cleanupWorkflowModals(): void {
  console.log('Starting workflow modal cleanup...');
  
  // First, try to close any open dialogs properly
  const openDialogs = document.querySelectorAll('[data-state="open"]');
  console.log(`Found ${openDialogs.length} open dialogs`);
  
  openDialogs.forEach(dialog => {
    const closeButton = dialog.querySelector('[data-radix-dialog-close], [data-radix-alert-dialog-cancel]');
    if (closeButton) {
      console.log('Triggering close button click');
      (closeButton as HTMLElement).click();
    }
  });

  // Wait a bit for the close animation, then clean up
  setTimeout(() => {
    console.log('Performing final cleanup...');
    comprehensiveModalCleanup();
  }, 200);
}

/**
 * Test function to verify cleanup is working
 */
export function testModalCleanup(): void {
  console.log('Testing modal cleanup...');
  
  // Check for any lingering overlays
  const overlays = document.querySelectorAll('[data-radix-dialog-overlay], [data-radix-alert-dialog-overlay]');
  console.log(`Found ${overlays.length} overlay elements`);
  
  overlays.forEach((overlay, index) => {
    const state = overlay.getAttribute('data-state');
    const zIndex = window.getComputedStyle(overlay).zIndex;
    console.log(`Overlay ${index}: state=${state}, z-index=${zIndex}`);
  });
  
  // Check body styles
  const body = document.body;
  console.log('Body styles before cleanup:', {
    overflow: body.style.overflow,
    paddingRight: body.style.paddingRight,
    pointerEvents: body.style.pointerEvents
  });
  
  // Perform cleanup
  comprehensiveModalCleanup();
  
  // Check again after cleanup
  setTimeout(() => {
    const remainingOverlays = document.querySelectorAll('[data-radix-dialog-overlay], [data-radix-alert-dialog-overlay]');
    console.log(`After cleanup: ${remainingOverlays.length} overlay elements remaining`);
    
    console.log('Body styles after cleanup:', {
      overflow: body.style.overflow,
      paddingRight: body.style.paddingRight,
      pointerEvents: body.style.pointerEvents
    });
  }, 300);
} 