import { create } from "zustand";

interface ZoomState {
  zoomLevel: number;
  setZoomLevel: (zoom: number) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
}

export const useZoomStore = create<ZoomState>((set, get) => ({
  zoomLevel: 100,
  
  setZoomLevel: (zoom: number) => {
    set({ zoomLevel: Math.round(zoom * 100) });
  },
  
  zoomIn: () => {
    const currentZoom = get().zoomLevel / 100;
    const newZoom = Math.min(currentZoom * 1.2, 4); // Max zoom 400%
    set({ zoomLevel: Math.round(newZoom * 100) });
  },
  
  zoomOut: () => {
    const currentZoom = get().zoomLevel / 100;
    const newZoom = Math.max(currentZoom / 1.2, 0.1); // Min zoom 10%
    set({ zoomLevel: Math.round(newZoom * 100) });
  },
  
  resetZoom: () => {
    set({ zoomLevel: 100 });
  },
})); 