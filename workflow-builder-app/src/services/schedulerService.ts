import { workflowApi } from '@/utils/axios';
import { API_ENDPOINTS } from '@/lib/apiConfig';

export interface InputValue {
  field_name: string;
  field_value: any;
  field_type: string;
}

export interface SchedulerCreate {
  name: string;
  workflow_id: string;
  frequency: 'every_minute' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  time?: string; // HH:mm format for daily, weekly, monthly
  days_of_week?: string[]; // For weekly
  days_of_month?: number[]; // For monthly
  cron_expression?: string; // For custom
  timezone?: string;
  is_active?: boolean;
  scheduler_metadata?: Record<string, any>;
  input_values?: InputValue[];
}

export interface SchedulerUpdate {
  name?: string;
  frequency?: 'every_minute' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  time?: string;
  days_of_week?: string[];
  days_of_month?: number[];
  cron_expression?: string;
  timezone?: string;
  is_active?: boolean;
  workflow_id?: string;
  scheduler_metadata?: Record<string, any>;
  input_values?: InputValue[];
}

export interface Scheduler {
  id: string;
  name: string;
  workflow_id: string;
  frequency: string;
  time?: string;
  days_of_week?: string[];
  days_of_month?: number[];
  cron_expression?: string;
  timezone: string;
  is_active: boolean;
  scheduler_metadata?: Record<string, any>;
  input_values?: InputValue[];
  created_at: string;
  updated_at: string;
  last_run_at?: string;
  next_run_at?: string;
  user_id: string;
}

export const schedulerService = {
  async createScheduler(data: SchedulerCreate): Promise<Scheduler> {
    const response = await workflowApi.post(API_ENDPOINTS.SCHEDULERS.CREATE, data);
    return response.data;
  },

  async getSchedulers(): Promise<Scheduler[]> {
    const response = await workflowApi.get(API_ENDPOINTS.SCHEDULERS.LIST);
    return response.data;
  },

  async getScheduler(id: string): Promise<Scheduler> {
    const response = await workflowApi.get(API_ENDPOINTS.SCHEDULERS.GET(id));
    return response.data;
  },

  async updateScheduler(id: string, data: SchedulerUpdate): Promise<Scheduler> {
    const response = await workflowApi.put(API_ENDPOINTS.SCHEDULERS.UPDATE(id), data);
    return response.data;
  },

  async deleteScheduler(id: string): Promise<void> {
    await workflowApi.delete(API_ENDPOINTS.SCHEDULERS.DELETE(id));
  }
};
