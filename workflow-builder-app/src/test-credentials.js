// Simple script to test the mock credential implementation
// Run this in the browser console to verify that the mock credential implementation works

// Import the mock credential functions
import {
  mockFetchCredentials,
  mockCreateCredential,
  mockDeleteCredential,
} from "./lib/mock-credential-service";

// Test creating a credential
async function testCreateCredential() {
  try {
    const credential = await mockCreateCredential({
      name: "Test API Key",
      type: "api_key",
      value: "sk-test-api-key-123456",
    });
    console.log("Created credential:", credential);
    return credential;
  } catch (error) {
    console.error("Error creating credential:", error);
  }
}

// Test fetching credentials
async function testFetchCredentials() {
  try {
    const result = await mockFetchCredentials();
    console.log("Fetched credentials:", result);
    return result;
  } catch (error) {
    console.error("Error fetching credentials:", error);
  }
}

// Test deleting a credential
async function testDeleteCredential(id) {
  try {
    const result = await mockDeleteCredential(id);
    console.log("Deleted credential:", result);
    return result;
  } catch (error) {
    console.error("Error deleting credential:", error);
  }
}

// Run the tests
async function runTests() {
  console.log("--- Testing Mock Credential Implementation ---");

  // First, fetch existing credentials
  console.log("1. Fetching existing credentials:");
  const initialCredentials = await testFetchCredentials();

  // Create a new credential
  console.log("2. Creating a new credential:");
  const newCredential = await testCreateCredential();

  // Fetch credentials again to verify the new credential was added
  console.log("3. Fetching credentials after creation:");
  await testFetchCredentials();

  // Delete the credential
  if (newCredential) {
    console.log(`4. Deleting credential with ID ${newCredential.id}:`);
    await testDeleteCredential(newCredential.id);

    // Fetch credentials again to verify the credential was deleted
    console.log("5. Fetching credentials after deletion:");
    await testFetchCredentials();
  }

  console.log("--- Tests completed ---");
}

// Export the test functions
export { runTests, testCreateCredential, testFetchCredentials, testDeleteCredential };
