/**
 * CSS styles for AgenticAI tool connections and visual distinctions
 */

/* AgenticAI node with connected tools */
.agentic-ai-has-tools {
  /* Border and background removed - keep original styling */
  /* Only keep tool count badge and other non-visual indicators */
}

/* Dark mode support for AgenticAI with tools */
.dark .agentic-ai-has-tools {
  /* Border and background removed - keep original styling */
}

/* Tool count indicator badge */
.tool-count-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f59e0b;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* Dark mode support for tool count badge */
.dark .tool-count-badge {
  border-color: #1f2937;
}

/* Tool handle styling */
.tool-handle {
  background-color: #f59e0b !important;
  border: 2px solid #d97706 !important;
  position: relative;
  transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.tool-handle:hover {
  background-color: #d97706 !important;
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.6);
}

/* Tool handle icon */
.tool-handle::before {
  content: "🔧";
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Components connected as tools */
.connected-as-tool {
  border: 2px dashed #f59e0b !important;
  /* Background removed - keep consistent with theme */
  position: relative;
}

/* Dark mode support for connected as tool */
.dark .connected-as-tool {
  /* Background removed - keep consistent with theme */
}

/* Tool connection indicator badge for connected components */
.connected-as-tool::after {
  content: "🔧";
  position: absolute;
  top: -8px;
  left: -8px;
  background-color: #f59e0b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* Dark mode support for tool connection indicator */
.dark .connected-as-tool::after {
  border-color: #1f2937;
}

/* Tool handle label styling */
.tool-handle-label {
  background-color: #f59e0b;
  color: white;
  border: 1px solid #d97706;
  font-size: 9px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.tool-handle-label::before {
  content: "🔧";
  font-size: 8px;
}

/* Regular handle styling (for contrast) */
.regular-handle {
  background-color: #3b82f6 !important;
  border: 2px solid #1e40af !important;
  transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.regular-handle:hover {
  background-color: #1e40af !important;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

/* Animation for tool connection state changes */
.tool-connection-transition {
  transition: all 0.3s ease-in-out;
}

/* Pulse animation for newly connected tools */
@keyframes tool-connection-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

.tool-connection-pulse {
  animation: tool-connection-pulse 2s infinite;
}

/* Hover effects for tool-related elements */
.agentic-ai-has-tools:hover {
  /* Box shadow removed - keep original styling */
}

.connected-as-tool:hover {
  /* Background removed - keep consistent with theme */
  border-color: #d97706 !important;
}

/* Enhanced tool handle states */
.tool-handle-hover {
  background-color: #f59e0b !important;
  box-shadow: 0 0 12px rgba(245, 158, 11, 0.7);
}

.tool-handle-focus {
  background-color: #f59e0b !important;
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

.tool-handle-connected {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
}

.tool-handle-disabled {
  background-color: #6b7280 !important;
  cursor: not-allowed;
  opacity: 0.5;
}

.tool-handle-error {
  background-color: #ef4444 !important;
  border-color: #ef4444 !important;
  animation: pulse 2s infinite;
}

/* Tool handle container */
.tool-handle-container {
  position: relative;
  display: flex;
  align-items: center;
}

.tool-handle-right {
  justify-content: flex-end;
}

/* Enhanced tool handle labels */
.tool-handle-label-connected {
  color: #10b981;
  border-color: #10b981;
}

/* Handle sections */
.handles-container {
  position: relative;
  width: 100%;
}

.handles-scrollable {
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #f59e0b transparent;
}

.handles-scrollable::-webkit-scrollbar {
  width: 4px;
}

.handles-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.handles-scrollable::-webkit-scrollbar-thumb {
  background-color: #f59e0b;
  border-radius: 2px;
}

/* Available tool slots */
.tool-slot-available {
  position: relative;
  margin: 2px 0;
}

.tool-slot-available .border-dashed {
  border-style: dashed;
  border-width: 2px;
  border-color: #f59e0b;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.tool-slot-available:hover .border-dashed {
  opacity: 1;
}

/* Focus states for accessibility */
.tool-handle:focus,
.tool-handle:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .agentic-ai-has-tools {
    border-width: 3px !important;
  }
  
  .connected-as-tool {
    border-width: 3px !important;
  }
  
  .tool-count-badge,
  .connected-as-tool::after {
    border-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tool-connection-transition,
  .tool-connection-pulse,
  .tool-handle,
  .regular-handle {
    animation: none !important;
    transition: none !important;
  }
}

/* Ensure handles never move from their position */
.tool-handle,
.regular-handle {
  transform: none !important;
  will-change: background-color, box-shadow;
}

/* Prevent any transform animations on handles */
.tool-handle *,
.regular-handle * {
  transform: none !important;
}

/* Static positioning for handle containers */
.tool-handle-container {
  transform: none !important;
  transition: none !important;
}

/* Ensure proper vertical alignment of handles with labels */
.tool-handle-container,
.workflow-node .group {
  align-items: center !important;
}

/* Override any conflicting positioning */
.tool-handle,
.regular-handle {
  top: auto !important;
  transform: none !important;
}

/* Force square connectors to become circles */
.react-flow__handle[data-handleid] {
  border-radius: 50% !important;
  width: 30px !important;
  height: 30px !important;
  border: 1px solid transparent !important;
  background: linear-gradient(#1E1E1E, #1E1E1E) padding-box, linear-gradient(90deg, #AE00D0 0%, #7B5AFF 101.13%) border-box !important;
}

/* Override default ReactFlow handle styling */
.custom-handle {
  border: 1px solid transparent !important;
  background: linear-gradient(#1E1E1E, #1E1E1E) padding-box, linear-gradient(90deg, #AE00D0 0%, #7B5AFF 101.13%) border-box !important;
  width: 30px !important;
  height: 30px !important;
  border-radius: 50% !important;
  z-index: 300 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
  box-shadow: none !important;
  line-height: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.custom-handle::before {
  display: none !important;
}

.custom-handle::after {
  display: none !important;
}

/* Hide any default ReactFlow handle backgrounds */
.react-flow__handle {
  background: transparent !important;
  border-radius: 50% !important;
}

.react-flow__handle::before {
  display: none !important;
}

.react-flow__handle::after {
  display: none !important;
}
