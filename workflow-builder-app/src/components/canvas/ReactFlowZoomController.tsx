"use client";

import React, { useEffect } from "react";
import { useReactFlow, Viewport } from "reactflow";
import { useZoomStore } from "@/store/zoomStore";

export const ReactFlowZoomController: React.FC = () => {
  const { setViewport, getZoom } = useReactFlow();
  const { zoomLevel, setZoomLevel } = useZoomStore();

  // Listen to zoom store changes and apply to ReactFlow
  useEffect(() => {
    const targetZoom = zoomLevel / 100;
    const currentZoom = getZoom();
    
    // Only update if the zoom levels are significantly different
    if (Math.abs(targetZoom - currentZoom) > 0.01) {
      setViewport({
        x: 0,
        y: 0,
        zoom: targetZoom,
      }, { duration: 200 });
    }
  }, [zoomLevel, setViewport, getZoom]);

  // Listen to ReactFlow zoom changes and update store
  useEffect(() => {
    const updateStoreZoom = () => {
      const currentZoom = getZoom();
      const storeZoom = zoomLevel / 100;
      
      // Only update store if ReactFlow zoom changed significantly
      if (Math.abs(currentZoom - storeZoom) > 0.01) {
        setZoomLevel(currentZoom);
      }
    };

    // Update immediately
    updateStoreZoom();

    // Set up interval to check for zoom changes
    const interval = setInterval(updateStoreZoom, 100);

    return () => clearInterval(interval);
  }, [getZoom, zoomLevel, setZoomLevel]);

  // This component doesn't render anything
  return null;
}; 