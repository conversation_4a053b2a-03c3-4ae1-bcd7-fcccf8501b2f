"use client";

import React, { useState, useCallback, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import Image from "next/image";
import { getExploreWorkflowsUrl } from "../../../lib/env";
import {
  LayoutDashboard,
  Globe,
  Key,
  FolderOpen,
  Clock,
  Store,
  ExternalLink,
  User,
  Settings,
} from "lucide-react";

interface ParentSidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  external?: boolean;
}

const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    href: "/",
  },
  {
    id: "global-variable",
    label: "Global variable",
    icon: Globe,
          href: "/global-variable",
  },
  {
    id: "credentials",
    label: "Credentials",
    icon: Key,
    href: "/credentials",
  },
  {
    id: "project",
    label: "Project",
    icon: FolderOpen,
          href: "/projects",
  },

  // {
  //   id: "profile",
  //   label: "Profile",
  //   icon: User,
  //   href: "/profile",
  // },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
    href: "/settings",
  },
  {
    id: "marketplace",
    label: "Explore Workflows",
    icon: Store,
    href: getExploreWorkflowsUrl(),
    external: true,
  },
];

const accountItems: MenuItem[] = [
  {
    id: "account",
    label: "My Account",
    icon: User,
    href: "/profile",
  },
];

export const ParentSidebar: React.FC<ParentSidebarProps> = React.memo(({
  collapsed = false,
  onToggleCollapse,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if dark mode is active
  const isDarkMode = theme === "dark";

  // Memoized active menu item calculation
  const activeMenuItem = useMemo(() => {
    if (pathname === "/") return "dashboard";
    if (pathname.startsWith("/global-variable")) return "global-variable";
    if (pathname.startsWith("/credentials")) return "credentials";
    if (pathname.startsWith("/projects")) return "project";
    if (pathname.startsWith("/settings/marketplace")) return "marketplace";
    if (pathname === "/profile") return "abc";
    if (pathname === "/settings") return "settings";
    if (pathname.startsWith("/settings")) return "account";
    return "dashboard";
  }, [pathname]);

  // Memoized active account item calculation
  const activeAccountItem = useMemo(() => {
    if (pathname === "/profile") return "account";
    if (pathname.startsWith("/settings")) return "account";
    return "";
  }, [pathname]);

  // Memoized menu item click handler
  const handleMenuItemClick = useCallback((item: MenuItem) => {
    if (item.external) {
      window.open(item.href, '_blank');
    } else {
      router.push(item.href);
    }
  }, [router]);

  return (
    <aside
      className={`bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 relative flex h-full shrink-0 flex-col overflow-hidden shadow-md transition-all duration-300 ${
        collapsed ? "w-16" : "w-64"
      }`}
    >
      {/* Header with Logo */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-800 min-h-[60px]">
        <div className="flex items-center">
          {!collapsed && (
            <Image
              src={isDarkMode ? "/company-logo-dark.svg" : "/company-logo-light.svg"}
              alt="RUH WORKFLOW Logo"
              width={120}
              height={40}
              className="w-30 h-10"
            />
          )}
        </div>
        <button
          onClick={onToggleCollapse}
          className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Image
            src={isDarkMode ? "/panel-left-close_dark.svg" : "/panel-left-close_light.svg"}
            alt="Toggle sidebar"
            width={24}
            height={24}
            className="w-6 h-6"
          />
        </button>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-3 py-4">
        <div className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeMenuItem === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleMenuItemClick(item)}
                className={`w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                  isActive
                    ? "bg-gray-100 dark:bg-gray-800 text-black dark:text-white"
                    : "text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-900"
                }`}
              >
                <div className="flex items-center gap-3">
                  <Icon className="h-5 w-5" />
                  {!collapsed && (
                    <span className="font-[Satoshi]">{item.label}</span>
                  )}
                </div>
                {!collapsed && item.external && (
                  <ExternalLink className="h-4 w-4" />
                )}
              </button>
            );
          })}
        </div>
      </nav>

      {/* Account Items */}
      <div className="bg-[#F4F4F5] dark:bg-[#2E2E2E] px-3 py-2">
        <div className="space-y-1">
          {accountItems.map((item) => {
            const isActive = activeAccountItem === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleMenuItemClick(item)}
                className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-transparent text-black dark:text-white hover:bg-[#E5E7EB] dark:hover:bg-[#3F3F46]`}
              >
                <div className="flex items-center gap-3">
                  <Image
                    src="/avatar.png"
                    alt="User Avatar"
                    width={24}
                    height={24}
                    className="h-6 w-6 rounded-full"
                  />
                  {!collapsed && (
                    <span className="font-[Satoshi]">{item.label}</span>
                  )}
                </div>
                {!collapsed && (
                  <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 4l5 5-5 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </button>
            );
          })}
        </div>
      </div>
    </aside>
  );
});

ParentSidebar.displayName = 'ParentSidebar'; 