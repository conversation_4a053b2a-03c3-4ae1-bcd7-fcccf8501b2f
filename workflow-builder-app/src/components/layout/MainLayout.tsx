"use client";

import React, { useState, useMemo, useCallback } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ParentSidebar } from "./ParentSidebar";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { Plus, Moon, Sun, ChevronDown, FileEdit, File, FileUp, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { createEmptyWorkflow } from "@/app/(features)/workflows/api";
import { toast } from "sonner";
import { useModalCleanup } from "@/utils/modalCleanup";

interface MainLayoutProps {
  children: React.ReactNode;
}

// Define the page configurations
const pageConfigs: Record<string, {
  title: string;
  buttonText: string;
  buttonAction: (() => void) | null;
  hasDropdown: boolean;
}> = {
  "/": {
    title: "Dashboard",
    buttonText: "Create new workflow",
    buttonAction: null, // Will be handled by dropdown
    hasDropdown: true
  },
      "/global-variable": {
    title: "Global variable",
    buttonText: "Create new global variable",
    buttonAction: () => {
      // Dispatch custom event to open global variable dialog
      window.dispatchEvent(new CustomEvent('openGlobalVariableDialog'));
    },
    hasDropdown: false
  },
  "/credentials": {
    title: "Credentials",
    buttonText: "Create new credential",
    buttonAction: () => {
      // Dispatch custom event to open credential dialog
      window.dispatchEvent(new CustomEvent('openCredentialDialog'));
    },
    hasDropdown: false
  },
      "/projects": {
    title: "Project",
    buttonText: "Create new project",
    buttonAction: () => {
      // Handle create project action
      console.log("Create new project");
    },
    hasDropdown: false
  },
  "/settings/triggers": {
    title: "Triggers and scheduler",
    buttonText: "Create new trigger",
    buttonAction: () => {
      // Handle create trigger action
      console.log("Create new trigger");
    },
    hasDropdown: false
  },
  "/settings/marketplace": {
            title: "Explore Workflows",
    buttonText: "Browse marketplace",
    buttonAction: () => {
      // Handle marketplace action
      console.log("Browse marketplace");
    },
    hasDropdown: false
  },
  "/profile": {
    title: "Profile",
    buttonText: "Edit Profile",
    buttonAction: () => {
      // Handle edit profile action
      console.log("Edit profile");
    },
    hasDropdown: false
  },
  "/settings": {
    title: "Settings",
    buttonText: "Save Settings",
    buttonAction: () => {
      // Handle save settings action
      console.log("Save settings");
    },
    hasDropdown: false
  }
};

export const MainLayout: React.FC<MainLayoutProps> = React.memo(({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);

  // Add modal cleanup hook for global cleanup
  useModalCleanup();

  // Handle hydration
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Add global click handler to ensure page interactivity
  React.useEffect(() => {
    const handleGlobalClick = () => {
      // Import and call ensurePageInteractivity on every click
      import('@/utils/modalCleanup').then(({ ensurePageInteractivity }) => {
        ensurePageInteractivity();
      });
    };

    // Add click listener to the entire document
    document.addEventListener('click', handleGlobalClick, true);
    
    return () => {
      document.removeEventListener('click', handleGlobalClick, true);
    };
  }, []);

  // Add keyboard shortcut to force restore page interactivity
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Shift+R to force restore page interactivity
      if (event.ctrlKey && event.shiftKey && event.key === 'R') {
        event.preventDefault();
        import('@/utils/modalCleanup').then(({ forceRestorePageInteractivity }) => {
          forceRestorePageInteractivity();
          toast.success('Page interactivity restored');
        });
      }
      
      // Ctrl+Shift+T to test modal cleanup
      if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        import('@/utils/modalCleanup').then(({ testModalCleanup }) => {
          testModalCleanup();
          toast.success('Modal cleanup test completed - check console');
        });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Theme toggle handler
  const handleToggleTheme = useCallback(() => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
  }, [theme, setTheme]);

  // Determine if dark mode is active
  const isDarkMode = theme === "dark";

  const handleToggleSidebar = useCallback(() => {
    setSidebarCollapsed(!sidebarCollapsed);
  }, [sidebarCollapsed]);

  // Memoized page configuration calculation
  const currentConfig = useMemo(() => {
    // Find the matching route
    for (const [route, config] of Object.entries(pageConfigs)) {
      if (pathname === route || pathname.startsWith(route + "/")) {
        return config;
      }
    }
    // Default to dashboard if no match found
    return pageConfigs["/"];
  }, [pathname]);

  // Handle creating a new workflow
  const handleCreateWorkflow = useCallback(async () => {
    try {
      setIsCreatingWorkflow(true);
      const newWorkflow = await createEmptyWorkflow();
      console.log("Created new workflow:", newWorkflow);
      // Redirect to canvas page with the new workflow ID
      router.push(`/workflows/${newWorkflow.workflow_id}/edit`);
    } catch (err: any) {
      console.error("Failed to create workflow:", err);
      
      // Check if it's a duplicate name error
      if (err.message && err.message.includes("already exists")) {
        toast.error(err.message);
      } else {
        toast.error("Failed to create a new workflow. Please try again.");
      }
      setIsCreatingWorkflow(false);
    }
  }, [router]);

  // Handle creating workflow from template (placeholder)
  const handleCreateFromTemplate = useCallback(() => {
    // TODO: Implement template selection functionality
    console.log("Create from template clicked - functionality to be implemented");
    toast.info("Template functionality coming soon!");
  }, []);

  // Handle importing workflow file (placeholder)
  const handleImportFile = useCallback(() => {
    // TODO: Implement file import functionality
    console.log("Import file clicked - functionality to be implemented");
    toast.info("File import functionality coming soon!");
  }, []);

  // Memoized action button renderer
  const renderActionButton = useCallback(() => {
    if (currentConfig.hasDropdown) {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              disabled={isCreatingWorkflow}
              className="bg-brand-primary hover:bg-brand-primary/90 text-white flex items-center gap-2"
            >
              {isCreatingWorkflow ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  {currentConfig.buttonText}
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem
              onClick={handleCreateWorkflow}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <FileEdit className="h-4 w-4" />
              <span>Create from blank</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleCreateFromTemplate}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <File className="h-4 w-4" />
              <span>Create from template</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleImportFile}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <FileUp className="h-4 w-4" />
              <span>Import file</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
         } else {
       return (
         <Button
           onClick={currentConfig.buttonAction || undefined}
           className="bg-brand-primary hover:bg-brand-primary/90 text-white flex items-center gap-2"
         >
           <Plus className="h-4 w-4" />
           {currentConfig.buttonText}
         </Button>
       );
     }
  }, [currentConfig, isCreatingWorkflow, handleCreateWorkflow, handleCreateFromTemplate, handleImportFile]);

  return (
    <div className="flex h-screen bg-background">
      {/* Parent Sidebar */}
      <ParentSidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleSidebar}
      />
      
      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Dynamic Header */}
        <div className={`p-3 shadow-md border-b border-gray-200 dark:border-gray-800 px-4 md:px-6 ${
          isDarkMode ? 'bg-card/30' : 'bg-[#FAFAFA]'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white font-[Satoshi]">
                {currentConfig.title}
              </h1>
            </div>
            <div className="flex items-center gap-3">
              {/* Theme Toggle */}
              <button
                type="button"
                className={`relative inline-flex h-8 sm:h-10 w-12 sm:w-16 items-center rounded-lg transition-colors duration-200 px-3 sm:px-4 ${
                  isDarkMode ? 'bg-[#3F3F46]' : 'bg-[#E5E7EB]'
                }`}
                onClick={handleToggleTheme}
                title="Toggle theme"
              >
                {/* Toggle Thumb - Square/Rectangular */}
                <div
                  className={`absolute h-6 w-6 sm:h-8 sm:w-8 transform rounded-sm shadow-md transition-transform duration-200 ${
                    isDarkMode ? 'bg-[#1E1E1E]' : 'bg-[#FFFFFF]'
                  }`}
                  style={{
                    left: isDarkMode ? '50%' : '0%',
                  }}
                />
                {/* Sun Icon - Left half */}
                <div
                  className="absolute flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center transition-transform duration-200"
                  style={{ left: '0%' }}
                >
                  <Sun className={`h-3 w-3 sm:h-4 sm:w-4 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-black'}`} />
                </div>
                {/* Moon Icon - Right half */}
                <div
                  className="absolute flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center transition-transform duration-200"
                  style={{ left: '50%' }}
                >
                  <Moon className={`h-3 w-3 sm:h-4 sm:w-4 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-black'}`} />
                </div>
              </button>
              
              {/* Action Button */}
              {renderActionButton()}
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </main>
    </div>
  );
});

MainLayout.displayName = 'MainLayout'; 