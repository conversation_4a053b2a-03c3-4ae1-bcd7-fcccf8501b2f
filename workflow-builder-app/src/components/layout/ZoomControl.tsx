"use client";

import React from "react";
import { Minus, Plus } from "lucide-react";
import { useZoomStore } from "@/store/zoomStore";

export const ZoomControl: React.FC = () => {
  const { zoomLevel, zoomIn, zoomOut } = useZoomStore();

  const handleZoomIn = () => {
    zoomIn();
  };

  const handleZoomOut = () => {
    zoomOut();
  };

  return (
    <div className="flex items-center">
      {/* Left separator */}
      <div className="w-px h-6 bg-border mx-1 sm:mx-2" />
      
      {/* Minus button */}
      <button
        onClick={handleZoomOut}
        className="px-1 sm:px-2 py-1 hover:bg-secondary/80 transition-colors duration-200 flex items-center justify-center text-secondary-foreground"
        title="Zoom out"
      >
        <Minus className="h-3 w-3" />
      </button>
      
      {/* Zoom percentage display */}
      <div className="px-1 sm:px-2 py-1 text-xs font-medium min-w-[2.5rem] sm:min-w-[3rem] text-center text-secondary-foreground">
        {zoomLevel}%
      </div>
      
      {/* Plus button */}
      <button
        onClick={handleZoomIn}
        className="px-1 sm:px-2 py-1 hover:bg-secondary/80 transition-colors duration-200 flex items-center justify-center text-secondary-foreground"
        title="Zoom in"
      >
        <Plus className="h-3 w-3" />
      </button>
      
      {/* Right separator */}
      <div className="w-px h-6 bg-border mx-1 sm:mx-2" />
    </div>
  );
}; 