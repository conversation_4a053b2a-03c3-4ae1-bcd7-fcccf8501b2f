import React from 'react';
import { CommonButton } from './common-button';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
  isLoading?: boolean;
  onConfirm: () => void;
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  isLoading = false,
  onConfirm
}: ConfirmationDialogProps) {
  if (!open) return null;

  const handleCancel = () => {
    if (!isLoading) {
      onOpenChange(false);
    }
  };

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <div className="fixed inset-0 backdrop-blur-sm bg-black/50 flex items-center justify-center z-[60] p-4">
      <div className="w-full max-w-md bg-brand-card border border-brand-stroke rounded-xl p-6 shadow-2xl animate-in fade-in-0 zoom-in-95 duration-200">
        {/* Header */}
        <div className="mb-6">
          <h3 
            className="text-xl font-semibold text-brand-primary-font mb-2"
            style={{ fontFamily: 'var(--font-satoshi)' }}
          >
            {title}
          </h3>
          <p 
            className="text-sm text-brand-secondary-font leading-relaxed"
            style={{ fontFamily: 'var(--font-satoshi)' }}
          >
            {description}
          </p>
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-end">
          <CommonButton
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="min-w-[80px]"
          >
            {cancelText}
          </CommonButton>
          <CommonButton
            variant={variant === 'destructive' ? 'destructive' : 'primary'}
            onClick={handleConfirm}
            isLoading={isLoading}
            loadingText="Processing..."
            className="min-w-[80px]"
          >
            {confirmText}
          </CommonButton>
        </div>
      </div>
    </div>
  );
}
