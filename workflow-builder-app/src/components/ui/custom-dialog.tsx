import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { cleanupWorkflowModals } from "@/utils/modalCleanup";

const CustomDialog = DialogPrimitive.Root;

const CustomDialogTrigger = DialogPrimitive.Trigger;

const CustomDialogPortal = DialogPrimitive.Portal;

const CustomDialogClose = DialogPrimitive.Close;

const CustomDialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",
      className,
    )}
    {...props}
  />
));
CustomDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      // Close the dialog by triggering the close button
      const closeButton = document.querySelector('[data-radix-dialog-close]');
      if (closeButton) {
        (closeButton as HTMLElement).click();
      }
    }
  };

  const handleClose = () => {
    // Clean up any lingering overlays after a short delay
    setTimeout(() => {
      cleanupWorkflowModals();
    }, 100);
  };

  // Add effect to monitor for any close actions and trigger cleanup
  React.useEffect(() => {
    const handleAnyClose = () => {
      // Immediate cleanup
      cleanupWorkflowModals();
      // Additional cleanup after animation
      setTimeout(() => {
        cleanupWorkflowModals();
      }, 100);
    };

    // Listen for any close events
    document.addEventListener('click', (e) => {
      const target = e.target as Element;
      if (target && (
        target.closest('[data-radix-dialog-close]') ||
        target.closest('[data-radix-alert-dialog-cancel]') ||
        target.textContent?.toLowerCase().includes('cancel')
      )) {
        handleAnyClose();
      }
    });

    return () => {
      // Cleanup will be handled by the event listener
    };
  }, []);

  return (
    <CustomDialogPortal>
      <CustomDialogOverlay onClick={handleBackdropClick} />
      <DialogPrimitive.Content
        ref={ref}
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",
          className,
        )}
        onCloseAutoFocus={(event) => {
          event.preventDefault();
        }}
        onEscapeKeyDown={handleClose}
        {...props}
      >
        {children}
        <DialogPrimitive.Close 
          className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none"
          onClick={handleClose}
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    </CustomDialogPortal>
  );
});
CustomDialogContent.displayName = DialogPrimitive.Content.displayName;

const CustomDialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("flex flex-col space-y-1.5 text-center sm:text-left", className)} {...props} />
);
CustomDialogHeader.displayName = "CustomDialogHeader";

const CustomDialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className)}
    {...props}
  />
);
CustomDialogFooter.displayName = "CustomDialogFooter";

const CustomDialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn("text-lg leading-none font-semibold tracking-tight", className)}
    {...props}
  />
));
CustomDialogTitle.displayName = DialogPrimitive.Title.displayName;

const CustomDialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-muted-foreground text-sm", className)}
    {...props}
  />
));
CustomDialogDescription.displayName = DialogPrimitive.Description.displayName;

export {
  CustomDialog,
  CustomDialogPortal,
  CustomDialogOverlay,
  CustomDialogClose,
  CustomDialogTrigger,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogFooter,
  CustomDialogTitle,
  CustomDialogDescription,
}; 