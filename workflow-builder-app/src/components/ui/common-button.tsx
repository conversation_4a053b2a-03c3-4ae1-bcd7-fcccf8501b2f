import React from 'react';
import { cn } from '@/lib/utils';

interface CommonButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'destructive' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function CommonButton({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  loadingText = 'Loading...',
  children,
  className,
  disabled,
  ...props
}: CommonButtonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-purple-600 dark:bg-[#AE00D0] hover:bg-purple-700 dark:hover:bg-purple-500 disabled:bg-purple-400 dark:disabled:bg-purple-700 text-white border-none';
      case 'destructive':
        return 'bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white border-none';
      case 'secondary':
        return 'bg-gray-200 dark:bg-[#4B4B4D] hover:bg-gray-300 dark:hover:bg-gray-600 disabled:bg-gray-100 dark:disabled:bg-[#2E2E2E] text-gray-700 dark:text-white border-none';
      case 'outline':
        return 'bg-transparent border border-gray-300 dark:border-[#4B4B4D] text-gray-700 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800';
      case 'ghost':
        return 'bg-transparent border-none text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800';
      default:
        return 'bg-purple-600 dark:bg-[#AE00D0] hover:bg-purple-700 dark:hover:bg-purple-500 disabled:bg-purple-400 dark:disabled:bg-purple-700 text-white border-none';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-xs h-8';
      case 'lg':
        return 'px-6 py-3 text-base h-12';
      case 'md':
      default:
        return 'px-4 py-2 text-sm h-10';
    }
  };

  const baseClasses = 'rounded-md font-satoshi font-semibold flex items-center justify-center gap-2 transition-all duration-200 outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2';
  const disabledClasses = (disabled || isLoading) ? 'cursor-not-allowed opacity-50' : 'cursor-pointer';

  return (
    <button
      {...props}
      disabled={disabled || isLoading}
      className={cn(
        baseClasses,
        getVariantClasses(),
        getSizeClasses(),
        disabledClasses,
        className
      )}
    >
      {isLoading ? loadingText : children}
    </button>
  );
}
