import React from 'react';
import { Scheduler } from '@/services/schedulerService';

interface SchedulerListProps {
  schedulers: Scheduler[];
  isLoading: boolean;
  onEdit: (scheduler: Scheduler) => void;
  onDelete: (scheduler: Scheduler) => void;
  onCreate: () => void;
  workflowName?: string;
}

export const SchedulerList: React.FC<SchedulerListProps> = ({
  schedulers,
  isLoading,
  onEdit,
  onDelete,
  onCreate,
  workflowName
}) => {
  const formatFrequency = (scheduler: Scheduler) => {
    switch (scheduler.frequency) {
      case 'every_minute': return 'Every minute';
      case 'hourly': return 'Hourly';
      case 'daily': return `Daily at ${scheduler.time}`;
      case 'weekly': return `Weekly on ${scheduler.days_of_week?.join(', ')} at ${scheduler.time}`;
      case 'monthly': return `Monthly on day ${scheduler.days_of_month?.join(', ')} at ${scheduler.time}`;
      case 'custom': return `Custom: ${scheduler.cron_expression}`;
      default: return scheduler.frequency;
    }
  };

  const formatLastUpdate = (dateString: string | null) => {
    if (!dateString) return 'never';
    
    const updateDate = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - updateDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    if (diffDays < 30) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    
    return updateDate.toLocaleDateString();
  };

  return (
    <div className="w-full flex flex-col gap-4">


      {/* List */}
      {isLoading ? (
        <div className="w-full h-[200px] flex items-center justify-center text-gray-500 dark:text-[#6F6F6F]">
          Loading schedulers...
        </div>
      ) : schedulers.length === 0 ? (
        <div className="w-full h-[200px] flex flex-col items-center justify-center text-gray-500 dark:text-[#6F6F6F] text-center">
          <p className="text-base mb-2">No schedulers found</p>
          <p className="text-sm">Create your first scheduler to automate this workflow</p>
        </div>
      ) : (
        <div className="w-full flex flex-col gap-3">
          {schedulers.map((scheduler) => (
            <div
              key={scheduler.id}
              className="flex justify-between items-center p-5 bg-white dark:bg-[#1E1E1E] border border-gray-200 dark:border-[#4B4B4D] rounded-lg"
            >
              <div className="flex items-center gap-4">
                <img 
                  src="/workflow-logo-white.svg" 
                  alt="Workflow Logo" 
                  width="43" 
                  height="43"
                  className="dark:hidden"
                />
                <img 
                  src="/workflow-logo.svg" 
                  alt="Workflow Logo" 
                  width="43" 
                  height="43"
                  className="hidden dark:block"
                />
                <div className="flex flex-col gap-2">
                  <div className="font-satoshi font-bold text-xl text-gray-800 dark:text-white">
                    {scheduler.name}
                  </div>
                  <div className="font-satoshi font-medium text-base text-gray-500 dark:text-[#6F6F6F]">
                    {formatFrequency(scheduler)} | Last update {formatLastUpdate(scheduler.updated_at)} | {scheduler.timezone}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => onEdit(scheduler)}
                  className="bg-transparent border-none cursor-pointer p-0 text-gray-600 dark:text-white hover:text-purple-600 dark:hover:text-purple-400"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button
                  onClick={() => onDelete(scheduler)}
                  className="bg-transparent border-none cursor-pointer p-0 text-gray-600 dark:text-white hover:text-red-600 dark:hover:text-red-400"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};