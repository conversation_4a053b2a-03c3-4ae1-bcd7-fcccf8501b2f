import React, { useState, useEffect, useMemo } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Workflow, Settings, ExternalLink } from "lucide-react";
import { fetchMCPsByIds, fetchWorkflowsByIds } from "@/lib/api";

interface AgentDetailsPanelProps {
  node: Node<WorkflowNodeData>;
}

interface WorkflowInfo {
  id: string;
  name: string;
  description: string;
}

interface MCPInfo {
  id: string;
  name: string;
  description: string;
}

/**
 * Component for displaying agent workflows and MCPs in the inspector
 */
export function AgentDetailsPanel({ node }: AgentDetailsPanelProps) {
  const [workflows, setWorkflows] = useState<WorkflowInfo[]>([]);
  const [mcps, setMCPs] = useState<MCPInfo[]>([]);
  const [loadingWorkflows, setLoadingWorkflows] = useState(false);
  const [loadingMCPs, setLoadingMCPs] = useState(false);
  const [workflowError, setWorkflowError] = useState<string | null>(null);
  const [mcpError, setMCPError] = useState<string | null>(null);

  // Extract agent info from the node and memoize the arrays to prevent infinite re-renders
  const agentInfo = node.data.definition?.agent_info;
  const workflowIds = useMemo(() => agentInfo?.workflow_ids || [], [agentInfo?.workflow_ids]);
  const mcpServerIds = useMemo(() => agentInfo?.mcp_server_ids || [], [agentInfo?.mcp_server_ids]);

  // Check if this is an agent node (including employee agents)
  const isAgentNode = node.data.type === "agent" || node.data.type === "employee" || node.data.originalType?.startsWith("agent-");

  // Fetch workflows when component mounts or workflow IDs change
  useEffect(() => {
    if (workflowIds.length > 0) {
      setLoadingWorkflows(true);
      setWorkflowError(null);

      fetchWorkflowsByIds(workflowIds)
        .then((response) => {
          if (response.success && response.workflows) {
            const workflowData = response.workflows.map((workflow: any) => ({
              id: workflow.id,
              name: workflow.name,
              description: workflow.description || "No description available",
            }));
            setWorkflows(workflowData);
          } else {
            setWorkflowError("Failed to fetch workflows");
            setWorkflows([]);
          }
        })
        .catch((error) => {
          console.error("Error fetching workflows:", error);
          setWorkflowError("Error fetching workflows");
          setWorkflows([]);
        })
        .finally(() => {
          setLoadingWorkflows(false);
        });
    } else {
      setWorkflows([]);
    }
  }, [JSON.stringify(workflowIds)]);

  // Fetch MCPs when component mounts or MCP IDs change
  useEffect(() => {
    if (mcpServerIds.length > 0) {
      setLoadingMCPs(true);
      setMCPError(null);

      fetchMCPsByIds(mcpServerIds)
        .then((response) => {
          if (response.success && response.mcps) {
            const mcpData = response.mcps.map((mcp: any) => ({
              id: mcp.id,
              name: mcp.name,
              description: mcp.description || "No description available",
            }));
            setMCPs(mcpData);
          } else {
            setMCPError("Failed to fetch MCPs");
            setMCPs([]);
          }
        })
        .catch((error) => {
          console.error("Error fetching MCPs:", error);
          setMCPError("Error fetching MCPs");
          setMCPs([]);
        })
        .finally(() => {
          setLoadingMCPs(false);
        });
    } else {
      setMCPs([]);
    }
  }, [JSON.stringify(mcpServerIds)]);

  // Don't render if this is not an agent node
  if (!isAgentNode || !agentInfo) {
    return null;
  }

  return (
    <ScrollArea className="h-full flex-grow overflow-auto p-4">
      <div className="space-y-4">
        {/* Agent Information */}
        <div>
          <h3 className="mb-3 text-sm font-medium font-[Satoshi]">Agent Information</h3>
          <div className="space-y-2 text-xs font-[Satoshi]">
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">Name</span>
              <span className="font-medium">{agentInfo.name}</span>
            </div>
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">ID</span>
              <span className="font-mono text-[10px] font-medium">
                {agentInfo.id}
              </span>
            </div>
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">Category</span>
              <span className="font-medium">
                {agentInfo.agent_category || "Unknown"}
              </span>
            </div>
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">Model</span>
              <span className="font-medium">
                {agentInfo.model_name || "Unknown"}
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Workflows Section */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Workflow className="h-4 w-4 text-blue-600" />
            <h3 className="text-sm font-medium">Associated Workflows</h3>
            <Badge variant="secondary" className="text-xs">
              {workflowIds.length}
            </Badge>
          </div>

          {loadingWorkflows ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading workflows...</span>
            </div>
          ) : workflowError ? (
            <div className="text-sm text-red-600 py-2">{workflowError}</div>
          ) : workflows.length > 0 ? (
            <div className="space-y-2">
              {workflows.map((workflow) => (
                <Card key={workflow.id} className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">{workflow.name}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {workflow.description}
                      </p>
                    </div>
                    <ExternalLink className="h-3 w-3 text-muted-foreground ml-2 flex-shrink-0" />
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-sm text-muted-foreground py-2">
              No workflows associated with this agent
            </div>
          )}
        </div>

        <Separator />

        {/* MCPs Section */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Settings className="h-4 w-4 text-green-600" />
            <h3 className="text-sm font-medium">Associated MCPs</h3>
            <Badge variant="secondary" className="text-xs">
              {mcpServerIds.length}
            </Badge>
          </div>

          {loadingMCPs ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading MCPs...</span>
            </div>
          ) : mcpError ? (
            <div className="text-sm text-red-600 py-2">{mcpError}</div>
          ) : mcps.length > 0 ? (
            <div className="space-y-2">
              {mcps.map((mcp) => (
                <Card key={mcp.id} className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">{mcp.name}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {mcp.description}
                      </p>
                    </div>
                    <ExternalLink className="h-3 w-3 text-muted-foreground ml-2 flex-shrink-0" />
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-sm text-muted-foreground py-2">
              No MCPs associated with this agent
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
}
