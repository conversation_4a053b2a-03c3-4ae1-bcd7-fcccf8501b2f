/**
 * ConditionalComponentInspector - Specialized inspector for conditional components.
 * 
 * This component provides a specialized UI for configuring conditional components
 * when component-based conditional routing is enabled. It includes:
 * - Component mode indicator
 * - Enhanced condition configuration
 * - Handle management information
 * - Routing decision visualization
 * 
 * Used when CONDITIONAL_ROUTING_MODE is set to 'component'.
 */

import React, { useState } from 'react';
import { Node } from 'reactflow';
import { WorkflowNodeData } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { InputRenderer } from '../InputRenderer';
import { checkInputVisibility } from '@/utils/inputVisibility';
import { isFieldRequired } from '@/lib/validation/fieldValidation';
import {
  CONDITIONAL_COMPONENT_UI_CONFIG,
  shouldUseComponentMode
} from '@/config/conditionalRouting';
import { Settings, Eye, EyeOff } from 'lucide-react';

interface ConditionalComponentInspectorProps {
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
}

export function ConditionalComponentInspector({
  node,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
}: ConditionalComponentInspectorProps) {
  const numConditions = node.data.config?.num_conditions || 2;
  const definition = node.data.definition;
  const [showAdvancedInfo, setShowAdvancedInfo] = useState(false);

  // Helper function to get target transition name for a condition
  const getTargetTransition = (conditionNum: number): string => {
    // This would typically come from edge connections in a real implementation
    const conditionConfig = node.data.config?.[`condition_${conditionNum}_expected_value`];
    if (conditionConfig) {
      return `${conditionConfig} Flow`;
    }
    return `Condition ${conditionNum} Flow`;
  };

  // Helper function to get default transition name
  const getDefaultTransition = (): string => {
    return 'Default Flow';
  };

  // Component mode is now the only supported mode

  return (
    <div className="space-y-6">
      {/* Component Mode Indicator */}
      {CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_MODE_INDICATORS && (
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Component Mode
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedInfo(!showAdvancedInfo)}
                className="h-6 px-2 text-xs"
              >
                {showAdvancedInfo ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                {showAdvancedInfo ? 'Hide' : 'Show'} Details
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-muted-foreground">
              This conditional node will execute as a component in the workflow, providing enhanced routing capabilities.
            </p>
            {showAdvancedInfo && (
              <div className="mt-3 space-y-2 border-t pt-3">
                <div className="text-xs">
                  <p className="font-medium text-blue-800 dark:text-blue-200">Technical Details:</p>
                  <ul className="mt-1 space-y-1 text-muted-foreground">
                    <li>• Executes as separate transition in node-executor-service</li>
                    <li>• Supports {numConditions} condition{numConditions !== 1 ? 's' : ''} with dynamic routing</li>
                    <li>• Provides enhanced error handling and logging</li>
                    <li>• Compatible with global context variables</li>
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Basic Configuration */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Basic Configuration</h3>
        
        {/* Number of Conditions */}
        {definition?.inputs
          ?.filter(inputDef => inputDef.name === 'num_conditions')
          .map(inputDef => (
            <div key={inputDef.name} className="pb-2">
              <Label
                htmlFor={`config-${node?.id}-${inputDef.name}`}
                className="flex items-center justify-between text-sm-custom font-semibold font-[Satoshi]"
              >
                <span>{inputDef.display_name}</span>
                {isFieldRequired(node, inputDef, isInputConnected(inputDef.name)) && (
                  <Badge
                    variant="destructive"
                    className="h-4 px-1 text-[9px]"
                  >
                    Required
                  </Badge>
                )}
              </Label>
              {inputDef.info && (
                <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs-custom font-[Satoshi] font-small">
                  {inputDef.info}
                </p>
              )}
              <InputRenderer
                inputDef={inputDef}
                value={node.data.config?.[inputDef.name]}
                onChange={onConfigChange}
                isDisabled={shouldDisableInput(inputDef.name)}
                isConnected={isInputConnected(inputDef.name)}
                connectionInfo={getConnectionInfo(inputDef.name)}
                nodeId={node.id}
              />
            </div>
          ))}
      </div>

      <Separator />

      {/* Conditions Configuration */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Conditions</h3>
        
        {Array.from({ length: numConditions }, (_, i) => i + 1).map(conditionNum => (
          <Card key={conditionNum} className="border-gray-200 dark:border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Condition {conditionNum}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {definition?.inputs
                ?.filter(inputDef => 
                  inputDef.name.startsWith(`condition_${conditionNum}_`) &&
                  !inputDef.name.includes('input_handle')
                )
                .map(inputDef => {
                  const isVisible = checkInputVisibility(inputDef, node, node.data.config || {});
                  if (!isVisible) return null;

                  return (
                    <div key={inputDef.name} className="pb-2">
                      <Label
                        htmlFor={`config-${node?.id}-${inputDef.name}`}
                        className="flex items-center justify-between text-sm-custom font-semibold font-[Satoshi]"
                      >
                        <span>{inputDef.display_name}</span>
                        {isFieldRequired(node, inputDef, isInputConnected(inputDef.name)) && (
                          <Badge
                            variant="destructive"
                            className="h-4 px-1 text-[9px]"
                          >
                            Required
                          </Badge>
                        )}
                      </Label>
                      {inputDef.info && (
                        <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs-custom font-[Satoshi] font-small">
                          {inputDef.info}
                        </p>
                      )}
                      <InputRenderer
                        inputDef={inputDef}
                        value={node.data.config?.[inputDef.name]}
                        onChange={onConfigChange}
                        isDisabled={shouldDisableInput(inputDef.name)}
                        isConnected={isInputConnected(inputDef.name)}
                        connectionInfo={getConnectionInfo(inputDef.name)}
                        nodeId={node.id}
                      />
                    </div>
                  );
                })}
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator />

      {/* Handle Management Section */}
      {CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_HANDLE_INFO && (
        <>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Component Handles</h3>
            <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <p className="text-xs font-medium text-green-800 dark:text-green-200">
                    Data Flow
                  </p>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    Data flows through the previous component in the workflow. This conditional component 
                    only determines the routing decision.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator />
        </>
      )}

      {/* Routing Decision Section */}
      {CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_ROUTING_VISUALIZATION && (
        <>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Routing Decision</h3>
            <Card className="border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50">
              <CardContent className="pt-4">
                <div className="space-y-3">
                  {Array.from({ length: numConditions }, (_, i) => i + 1).map(conditionNum => {
                    const operator = node.data.config?.[`condition_${conditionNum}_operator`] || 'equals';
                    const expectedValue = node.data.config?.[`condition_${conditionNum}_expected_value`] || '';
                    const source = node.data.config?.[`condition_${conditionNum}_source`] || 'node_output';

                    return (
                      <div key={conditionNum} className="flex items-center justify-between p-2 rounded bg-white/50 dark:bg-gray-900/50">
                        <div className="flex items-center gap-2 text-xs">
                          <Badge variant="outline" className="h-5 px-1.5 text-[10px]">
                            {conditionNum}
                          </Badge>
                          <span className="text-muted-foreground">
                            {source} {operator} "{expectedValue}"
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-xs font-mono">
                          <span className="text-purple-600 dark:text-purple-400">→</span>
                          <span className="text-purple-800 dark:text-purple-200">
                            {getTargetTransition(conditionNum)}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                  <div className="flex items-center justify-between p-2 rounded bg-gray-100/50 dark:bg-gray-800/50">
                    <div className="flex items-center gap-2 text-xs">
                      <Badge variant="secondary" className="h-5 px-1.5 text-[10px]">
                        ∅
                      </Badge>
                      <span className="text-muted-foreground">No conditions match</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs font-mono">
                      <span className="text-gray-600 dark:text-gray-400">→</span>
                      <span className="text-gray-800 dark:text-gray-200">
                        {getDefaultTransition()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator />
        </>
      )}

      {/* Default Transition */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Default Transition</h3>
        <p className="text-xs text-muted-foreground">
          The default transition will be used when no conditions match.
        </p>
      </div>
    </div>
  );
}
