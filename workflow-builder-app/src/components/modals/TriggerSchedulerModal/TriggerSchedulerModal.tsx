import React, { useState } from 'react';
import { SchedulerComponent } from './components/SchedulerComponent';
import { TriggersComponent } from './components/TriggersComponent';

interface TriggerSchedulerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  workflowName?: string;
  workflowId?: string;
}

const TriggerSchedulerModal: React.FC<TriggerSchedulerModalProps> = ({ 
  open, 
  onOpenChange, 
  workflowName,
  workflowId 
}) => {
  const [activeTab, setActiveTab] = useState<'schedule' | 'triggers'>('schedule');

  if (!open) {
    return null;
  }

  return (
    <div 
      className="fixed top-0 left-0 right-0 bottom-0 bg-black/30 flex items-center justify-center"
      style={{ 
        zIndex: 50, 
        backdropFilter: 'blur(4px)', 
        WebkitBackdropFilter: 'blur(4px)',
        position: 'fixed',
        width: '100vw',
        height: '100vh'
      }}
    >
      <div className="relative rounded-xl w-[960px] h-[644px] bg-white dark:bg-[#1E1E1E] shadow-lg p-6 flex flex-col justify-between items-start gap-6">
        
        {/* Header with Title and Close Button */}
        <div className="flex flex-row justify-between items-center p-0 gap-2 w-[912px] h-7">
          <h2 className="font-satoshi font-bold text-xl leading-[140%] tracking-tight text-gray-800 dark:text-white">
            Add triggers and scheduler
          </h2>
          <button
            onClick={() => onOpenChange(false)}
            className="w-6 h-6 flex items-center justify-center bg-transparent border-none cursor-pointer"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="#777E90" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        
        {/* Modal Content */}
        <div className="w-[912px] h-full flex flex-col items-center gap-6">
          
          {/* Tab Navigation */}
          <div className="w-[912px] h-[72px] flex flex-row justify-between items-center p-2.5 gap-4.5 bg-gray-100 dark:bg-[#2E2E2E] rounded-lg">
            <button
              onClick={() => setActiveTab('triggers')}
              className={`w-[434px] h-[52px] flex justify-center items-center py-[15px] px-0 gap-2.5 rounded ${activeTab === 'triggers' ? 'bg-white dark:bg-[#18181B]' : 'bg-transparent'} border-none cursor-pointer font-satoshi font-bold text-base leading-[140%] text-gray-800 dark:text-white`}
            >
              Triggers
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`w-[434px] h-[52px] flex justify-center items-center py-[15px] px-0 gap-2.5 rounded ${activeTab === 'schedule' ? 'bg-white dark:bg-[#18181B]' : 'bg-transparent'} border-none cursor-pointer font-satoshi font-bold text-base leading-[140%] text-gray-800 dark:text-white`}
            >
              Scheduler
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === 'schedule' && (
            <SchedulerComponent workflowName={workflowName} workflowId={workflowId} />
          )}

          {activeTab === 'triggers' && (
            <TriggersComponent workflowName={workflowName} workflowId={workflowId} />
          )}
        </div>
      </div>
    </div>
  );
};

export default TriggerSchedulerModal;