import React from 'react';

interface TriggersComponentProps {
  workflowName?: string;
  workflowId?: string;
}

export const TriggersComponent: React.FC<TriggersComponentProps> = ({ 
  workflowName,
  workflowId 
}) => {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="text-center">
        <p className="text-lg mb-2 text-gray-800 dark:text-white font-semibold">
          Coming Soon
        </p>
        <p className="text-sm text-gray-500 dark:text-[#6F6F6F]">
          Trigger functionality will be available soon
        </p>
      </div>
    </div>
  );
};