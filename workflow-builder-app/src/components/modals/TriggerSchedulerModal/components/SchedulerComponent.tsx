import React, { useState, useEffect } from 'react';
import { schedulerService, SchedulerCreate, Scheduler, SchedulerUpdate } from '@/services/schedulerService';
import { toast } from 'sonner';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { SchedulerList } from '@/components/ui/schedulerTriggersList';

interface SchedulerComponentProps {
  workflowName?: string;
  workflowId?: string;
}

export const SchedulerComponent: React.FC<SchedulerComponentProps> = ({ 
  workflowName,
  workflowId 
}) => {
  const [view, setView] = useState<'list' | 'create' | 'edit' | 'parameters'>('list');
  const [editingScheduler, setEditingScheduler] = useState<Scheduler | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [schedulers, setSchedulers] = useState<Scheduler[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [inputParameters, setInputParameters] = useState<any[]>([]);
  const [parameterValues, setParameterValues] = useState<Record<string, any>>({});
  const [parameterErrors, setParameterErrors] = useState<Record<string, string>>({});
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    schedulerId: string;
    schedulerName: string;
  }>({
    isOpen: false,
    schedulerId: '',
    schedulerName: ''
  });
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoadingParameters, setIsLoadingParameters] = useState(false);
  const [detectedTimezone, setDetectedTimezone] = useState<string | null>(null);
  
  const SUPPORTED_TIMEZONES = [
    "UTC", "America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles",
    "America/Toronto", "America/Vancouver", "America/Sao_Paulo", "Europe/London", "Europe/Berlin",
    "Europe/Madrid", "Europe/Paris", "Europe/Moscow", "Asia/Kolkata", "Asia/Shanghai",
    "Asia/Tokyo", "Asia/Singapore", "Asia/Dubai", "Asia/Seoul", "Australia/Sydney",
    "Australia/Perth", "Pacific/Auckland", "Africa/Johannesburg", "Africa/Cairo"
  ];

  const normalizeTimezone = (timezone: string): string => {
    const legacyMap: Record<string, string> = {
      'US/Eastern': 'America/New_York', 'US/Central': 'America/Chicago',
      'US/Mountain': 'America/Denver', 'US/Pacific': 'America/Los_Angeles',
      'Canada/Eastern': 'America/Toronto', 'Canada/Pacific': 'America/Vancouver',
      'Brazil/East': 'America/Sao_Paulo', 'GB': 'Europe/London', 'CET': 'Europe/Berlin',
      'GMT': 'UTC', 'Asia/Calcutta': 'Asia/Kolkata', 'PRC': 'Asia/Shanghai'
    };
    return legacyMap[timezone] || timezone;
  };
  
  const [formData, setFormData] = useState({
    name: '', frequency: 'daily' as SchedulerCreate['frequency'], time: '09:00',
    timezone: 'UTC', days_of_week: [] as string[], days_of_month: [] as number[],
    cron_expression: '', is_active: true
  });

  const loadWorkflowParameters = async () => {
    if (!workflowId) return;
    setIsLoadingParameters(true);
    try {
      const { fetchWorkflowById } = await import('@/app/(features)/workflows/api');
      const workflowData = await fetchWorkflowById(workflowId);
      
      if (workflowData?.workflow?.start_nodes) {
        const fieldsToUse = workflowData.workflow.start_nodes.map((node: any) => ({
          nodeId: node.transition_id || 'start-node', nodeName: 'Start Node',
          name: node.field, displayName: node.field, inputType: node.type || 'string', required: true
        }));
        
        setInputParameters(fieldsToUse);
        
        const initialValues: Record<string, any> = {};
        fieldsToUse.forEach((field: any) => {
          const fieldId = `${field.nodeId}_${field.name}`;
          let existingValue = null;
          if (editingScheduler?.input_values) {
            const existingInput = editingScheduler.input_values.find(
              (input: any) => input.field_name === field.name
            );
            if (existingInput) existingValue = existingInput.field_value;
          }
          
          if (existingValue !== null) {
            initialValues[fieldId] = existingValue;
          } else {
            switch (field.inputType) {
              case 'boolean': initialValues[fieldId] = false; break;
              case 'number': case 'int': case 'float': initialValues[fieldId] = 0; break;
              case 'object': case 'dict': case 'json': initialValues[fieldId] = {}; break;
              case 'array': case 'list': initialValues[fieldId] = []; break;
              default: initialValues[fieldId] = '';
            }
          }
        });
        setParameterValues(initialValues);
      } else {
        setInputParameters([]);
        setParameterValues({});
      }
    } catch (error) {
      console.error('Error loading workflow parameters:', error);
      setInputParameters([]);
      setParameterValues({});
    } finally {
      setIsLoadingParameters(false);
    }
  };

  useEffect(() => {
    const rawTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const normalizedTimezone = normalizeTimezone(rawTimezone);
    const matchedTimezone = SUPPORTED_TIMEZONES.includes(normalizedTimezone) ? normalizedTimezone : 'UTC';
    setDetectedTimezone(rawTimezone);
    setFormData(prev => ({ ...prev, timezone: matchedTimezone }));
    loadSchedulers();
  }, []);

  const loadSchedulers = async () => {
    setIsLoading(true);
    try {
      const data = await schedulerService.getSchedulers();
      const filteredSchedulers = workflowId 
        ? data.filter(scheduler => scheduler.workflow_id === workflowId)
        : data;
      setSchedulers(filteredSchedulers);
    } catch (error: any) {
      toast.error('Failed to load schedulers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleParameterChange = (fieldId: string, value: any, type: string) => {
    setParameterValues(prev => ({ ...prev, [fieldId]: value }));
    const newErrors = { ...parameterErrors };
    const field = inputParameters.find(p => `${p.nodeId}_${p.name}` === fieldId);
    const isRequired = field?.required !== false;
    
    if (isRequired && (value === '' || value === null || value === undefined)) {
      newErrors[fieldId] = 'This field is required';
    } else {
      delete newErrors[fieldId];
    }
    setParameterErrors(newErrors);
  };

  const handleFormSubmit = () => {
    if (!workflowId || !formData.name.trim()) {
      toast.error(!workflowId ? 'Workflow ID is required' : 'Schedule name is required');
      return;
    }

    if (formData.frequency === 'weekly' && formData.days_of_week.length === 0) {
      toast.error('Please select at least one day of the week');
      return;
    }

    if (formData.frequency === 'monthly' && formData.days_of_month.length === 0) {
      toast.error('Please specify at least one day of the month');
      return;
    }

    if (formData.frequency === 'custom' && !formData.cron_expression.trim()) {
      toast.error('Cron expression is required for custom frequency');
      return;
    }

    loadWorkflowParameters().then(() => setView('parameters'));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      if (!workflowId) {
        toast.error('Workflow ID is required');
        return;
      }

      const schedulerData: SchedulerCreate = {
        name: formData.name, workflow_id: workflowId, frequency: formData.frequency,
        timezone: formData.timezone, is_active: formData.is_active
      };

      if (['daily', 'weekly', 'monthly'].includes(formData.frequency)) {
        schedulerData.time = formData.time;
      }
      
      if (formData.frequency === 'weekly' && formData.days_of_week.length > 0) {
        schedulerData.days_of_week = formData.days_of_week;
      }
      
      if (formData.frequency === 'monthly' && formData.days_of_month.length > 0) {
        schedulerData.days_of_month = formData.days_of_month;
      }
      
      if (formData.frequency === 'custom' && formData.cron_expression) {
        schedulerData.cron_expression = formData.cron_expression;
      }

      if (inputParameters.length > 0) {
        const input_values: any[] = [];
        inputParameters.forEach((field: any) => {
          const fieldId = `${field.nodeId}_${field.name}`;
          const value = parameterValues[fieldId];
          if (value !== undefined) {
            input_values.push({
              field_name: field.name, field_value: value,
              field_type: field.inputType === 'int' || field.inputType === 'float' ? 'number' : 
                         field.inputType === 'bool' ? 'boolean' :
                         field.inputType === 'list' ? 'array' : field.inputType
            });
          }
        });
        if (input_values.length > 0) {
          schedulerData.input_values = input_values;
        }
      }

      await schedulerService.createScheduler(schedulerData);
      toast.success('Scheduler created successfully');
      
      const rawTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const normalizedTimezone = normalizeTimezone(rawTimezone);
      const matchedTimezone = SUPPORTED_TIMEZONES.includes(normalizedTimezone) ? normalizedTimezone : 'UTC';
      setFormData({
        name: '', frequency: 'daily', time: '09:00', timezone: matchedTimezone,
        days_of_week: [], days_of_month: [], cron_expression: '', is_active: true
      });
      setParameterValues({});
      setParameterErrors({});
      setInputParameters([]);
      setView('list');
      await loadSchedulers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to create scheduler');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClick = (scheduler: Scheduler) => {
    setDeleteConfirmation({
      isOpen: true, schedulerId: scheduler.id, schedulerName: scheduler.name
    });
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      await schedulerService.deleteScheduler(deleteConfirmation.schedulerId);
      toast.success('Scheduler deleted successfully');
      await loadSchedulers();
      setDeleteConfirmation({ isOpen: false, schedulerId: '', schedulerName: '' });
    } catch (error: any) {
      toast.error('Failed to delete scheduler');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditClick = (scheduler: Scheduler) => {
    setFormData({
      name: scheduler.name, frequency: scheduler.frequency as SchedulerCreate['frequency'],
      time: scheduler.time || '09:00', timezone: scheduler.timezone,
      days_of_week: scheduler.days_of_week || [], days_of_month: scheduler.days_of_month || [],
      cron_expression: scheduler.cron_expression || '', is_active: scheduler.is_active
    });
    setEditingScheduler(scheduler);
    setView('edit');
  };

  const handleEditFormSubmit = async () => {
    if (!editingScheduler || !formData.name.trim()) {
      toast.error(!editingScheduler ? 'No scheduler selected for editing' : 'Schedule name is required');
      return;
    }

    if (formData.frequency === 'weekly' && formData.days_of_week.length === 0) {
      toast.error('Please select at least one day of the week');
      return;
    }

    if (formData.frequency === 'monthly' && formData.days_of_month.length === 0) {
      toast.error('Please specify at least one day of the month');
      return;
    }

    if (formData.frequency === 'custom' && !formData.cron_expression.trim()) {
      toast.error('Cron expression is required for custom frequency');
      return;
    }

    await loadWorkflowParameters();
    
    if (editingScheduler.input_values) {
      setTimeout(() => {
        const existingValues: Record<string, any> = {};
        editingScheduler.input_values?.forEach((input: any) => {
          const field = inputParameters.find((p: any) => p.name === input.field_name);
          if (field) {
            const fieldId = `${field.nodeId}_${field.name}`;
            existingValues[fieldId] = input.field_value;
          }
        });
        setParameterValues(prev => ({ ...prev, ...existingValues }));
      }, 300);
    }
    
    setView('parameters');
  };

  const handleUpdateSubmit = async () => {
    if (!editingScheduler) {
      toast.error('No scheduler selected for editing');
      return;
    }

    setIsSubmitting(true);
    try {
      const schedulerData: SchedulerUpdate = {
        name: formData.name, frequency: formData.frequency,
        timezone: formData.timezone, is_active: formData.is_active
      };

      if (['daily', 'weekly', 'monthly'].includes(formData.frequency)) {
        schedulerData.time = formData.time;
      }
      
      if (formData.frequency === 'weekly' && formData.days_of_week.length > 0) {
        schedulerData.days_of_week = formData.days_of_week;
      }
      
      if (formData.frequency === 'monthly' && formData.days_of_month.length > 0) {
        schedulerData.days_of_month = formData.days_of_month;
      }
      
      if (formData.frequency === 'custom' && formData.cron_expression) {
        schedulerData.cron_expression = formData.cron_expression;
      }

      if (inputParameters.length > 0) {
        const input_values: any[] = [];
        inputParameters.forEach((field: any) => {
          const fieldId = `${field.nodeId}_${field.name}`;
          const value = parameterValues[fieldId];
          if (value !== undefined) {
            input_values.push({
              field_name: field.name, field_value: value,
              field_type: field.inputType === 'int' || field.inputType === 'float' ? 'number' : 
                         field.inputType === 'bool' ? 'boolean' :
                         field.inputType === 'list' ? 'array' : field.inputType
            });
          }
        });
        if (input_values.length > 0) {
          schedulerData.input_values = input_values;
        }
      }

      await schedulerService.updateScheduler(editingScheduler.id, schedulerData);
      toast.success('Scheduler updated successfully');
      
      const rawTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const normalizedTimezone = normalizeTimezone(rawTimezone);
      const matchedTimezone = SUPPORTED_TIMEZONES.includes(normalizedTimezone) ? normalizedTimezone : 'UTC';
      setFormData({
        name: '', frequency: 'daily', time: '09:00', timezone: matchedTimezone,
        days_of_week: [], days_of_month: [], cron_expression: '', is_active: true
      });
      setParameterValues({});
      setParameterErrors({});
      setInputParameters([]);
      setEditingScheduler(null);
      setView('list');
      await loadSchedulers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update scheduler');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFrequency = (scheduler: Scheduler) => {
    switch (scheduler.frequency) {
      case 'every_minute': return 'Every minute';
      case 'hourly': return 'Hourly';
      case 'daily': return `Daily at ${scheduler.time}`;
      case 'weekly': return `Weekly on ${scheduler.days_of_week?.join(', ')} at ${scheduler.time}`;
      case 'monthly': return `Monthly on day ${scheduler.days_of_month?.join(', ')} at ${scheduler.time}`;
      case 'custom': return `Custom: ${scheduler.cron_expression}`;
      default: return scheduler.frequency;
    }
  };

  const formatLastUpdate = (dateString: string | null) => {
    if (!dateString) return 'never';
    
    const updateDate = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - updateDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    if (diffDays < 30) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    
    return updateDate.toLocaleDateString();
  };

  return (
    <>
      <div className="w-[912px] h-[384px] overflow-y-auto" style={{
        scrollbarWidth: 'thin',
        scrollbarColor: '#CBD5E0 transparent'
      }}>
        <div className="w-[896px] flex flex-col items-start gap-4">
        
        {/* Scheduler List View */}
        {view === 'list' && (
          <>
            {/* Create Scheduler Button */}
            <div className="w-full flex justify-between items-center">
              <h3 className="text-gray-800 dark:text-white text-xl font-bold m-0 leading-[140%] tracking-[-0.2px] font-satoshi">
                Schedulers for {workflowName}
              </h3>
              <button
                onClick={() => setView('create')}
                className="flex items-center gap-1 px-3 py-2 bg-transparent border-none cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <span className="text-[#AE00D0] font-bold text-base font-satoshi">
                  + Create Scheduler
                </span>
              </button>
            </div>
            <SchedulerList
              schedulers={schedulers}
              isLoading={isLoading}
              onEdit={handleEditClick}
              onDelete={handleDeleteClick}
              onCreate={() => setView('create')}
              workflowName={workflowName}
            />
          </>
        )}

        {/* Create/Edit Scheduler Form */}
        {(view === 'create' || view === 'edit') && (
          <>
            {/* Schedule Name */}
            <div className="flex flex-col items-start p-0 gap-2 w-[902px] h-[78px]">
              <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                Schedule Name
              </label>
              <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., Daily Report"
                  className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px]"
                />
              </div>
            </div>

            {/* Frequency */}
            <div className="flex flex-col items-start p-0 gap-2 w-[902px] h-[78px]">
              <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                Frequency
              </label>
              <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                <select
                  value={formData.frequency}
                  onChange={(e) => handleInputChange('frequency', e.target.value)}
                  className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] appearance-none pr-8"
                  style={{
                    backgroundImage: `url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%3E%3Cpath%20d%3D%22M6%209L12%2015L18%209%22%20stroke%3D%22%23${window.matchMedia('(prefers-color-scheme: dark)').matches ? 'FFFFFF' : '000000'}%22%20stroke-width%3D%222%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E")`,
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'right center'
                  }}
                >
                  <option value="every_minute">Every Minute</option>
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="custom">Custom (Cron)</option>
                </select>
              </div>
            </div>

            {/* Time (for daily, weekly, monthly) */}
            {['daily', 'weekly', 'monthly'].includes(formData.frequency) && (
              <div className="flex flex-col items-start p-0 gap-2 w-[902px] h-[78px]">
                <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                  Time
                </label>
                <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                  <input
                    type="time"
                    value={formData.time}
                    onChange={(e) => handleInputChange('time', e.target.value)}
                    className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px]"
                  />
                </div>
              </div>
            )}

            {/* Timezone */}
            <div className="flex flex-col items-start p-0 gap-2 w-[902px]">
              <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                Timezone
              </label>
              {detectedTimezone && view === 'create' && (
                <div className="text-xs text-purple-600 dark:text-purple-400 font-satoshi mb-1">
                  Timezone automatically detected: {detectedTimezone}
                  {normalizeTimezone(detectedTimezone) !== detectedTimezone && ` (normalized to ${normalizeTimezone(detectedTimezone)})`}
                  {!SUPPORTED_TIMEZONES.includes(normalizeTimezone(detectedTimezone)) && " (defaulted to UTC)"}
                </div>
              )}
              <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                <select
                  value={formData.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] appearance-none pr-8"
                  style={{
                    backgroundImage: `url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%3E%3Cpath%20d%3D%22M6%209L12%2015L18%209%22%20stroke%3D%22%23${window.matchMedia('(prefers-color-scheme: dark)').matches ? 'FFFFFF' : '000000'}%22%20stroke-width%3D%222%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E")`,
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'right center'
                  }}
                >
                  {SUPPORTED_TIMEZONES.map(tz => (
                    <option key={tz} value={tz}>{tz}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Days of Week (for weekly) */}
            {formData.frequency === 'weekly' && (
              <div className="flex flex-col items-start p-0 gap-2 w-[902px] h-[78px]">
                <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                  Days of Week
                </label>
                <div className="box-border flex flex-row justify-start items-center py-2 px-3 gap-4 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg flex-wrap overflow-hidden">
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                    <label key={day} className="flex items-center gap-1 text-gray-800 dark:text-white text-sm font-satoshi font-medium cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.days_of_week.includes(day)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('days_of_week', [...formData.days_of_week, day]);
                          } else {
                            handleInputChange('days_of_week', formData.days_of_week.filter(d => d !== day));
                          }
                        }}
                        className="mr-1 accent-[#AE00D0] dark:accent-[#AE00D0]"
                      />
                      {day}
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* Days of Month (for monthly) */}
            {formData.frequency === 'monthly' && (
              <div className="flex flex-col items-start p-0 gap-2 w-[902px]">
                <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                  Days of Month (1-31)
                </label>
                <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                  <input
                    type="text"
                    value={formData.days_of_month.join(', ')}
                    onChange={(e) => {
                      const days = e.target.value
                        .split(',')
                        .map(d => parseInt(d.trim()))
                        .filter(d => !isNaN(d) && d >= 1 && d <= 31);
                      handleInputChange('days_of_month', days);
                    }}
                    placeholder="e.g., 1, 15, 30"
                    className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px]"
                  />
                </div>
                <small className="text-gray-500 dark:text-[#6F6F6F] text-xs font-satoshi font-normal leading-5">
                  Enter comma-separated day numbers (1-31)
                </small>
              </div>
            )}

            {/* Cron Expression (for custom) */}
            {formData.frequency === 'custom' && (
              <div className="flex flex-col items-start p-0 gap-2 w-[902px]">
                <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center text-gray-800 dark:text-white">
                  Cron Expression
                </label>
                <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                  <input
                    type="text"
                    value={formData.cron_expression}
                    onChange={(e) => handleInputChange('cron_expression', e.target.value)}
                    placeholder="e.g., 0 9 * * 1-5 (weekdays at 9 AM)"
                    className="w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px]"
                  />
                </div>
                <small className="text-gray-500 dark:text-[#6F6F6F] text-xs font-satoshi font-normal leading-5">
                  5-part cron format: minute hour day month weekday
                </small>
              </div>
            )}

            {/* Active Status Toggle */}
            <div className="w-[902px] flex flex-col gap-2">
              <label className="font-bold text-sm text-gray-800 dark:text-white">
                Status
              </label>
              <div className="flex items-center gap-3">
                <button
                  type="button"
                  onClick={() => handleInputChange('is_active', !formData.is_active)}
                  className={`w-11 h-6 rounded-full border-none cursor-pointer relative transition-colors duration-200 ${formData.is_active ? 'bg-[#AE00D0] dark:bg-[#AE00D0]' : 'bg-gray-300 dark:bg-[#4B4B4D]'}`}
                >
                  <div
                    className={`w-5 h-5 rounded-full bg-white absolute top-0.5 transition-all duration-200 ${formData.is_active ? 'left-[22px]' : 'left-0.5'}`}
                  />
                </button>
                <span className={`text-sm font-medium font-satoshi leading-[22px] ${formData.is_active ? 'text-gray-800 dark:text-white' : 'text-gray-500 dark:text-[#6F6F6F]'}`}>
                  {formData.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </>
        )}

        {/* Input Parameters Step */}
        {view === 'parameters' && (
          <>
            <div className="w-full mb-4">
              <h3 className="text-gray-800 dark:text-white text-xl font-bold mb-2 leading-[140%] tracking-[-0.2px] font-satoshi">
                Workflow Input Parameters
              </h3>
              <p className="text-gray-500 dark:text-[#6F6F6F] text-sm">
                Configure the input parameters that will be passed to the workflow when the scheduler executes.
              </p>
            </div>

            {inputParameters.length > 0 ? (
              <>
                {inputParameters.map((field: any) => {
                  const fieldId = `${field.nodeId}_${field.name}`;
                  const isRequired = field.required !== false;
                  
                  return (
                    <div key={fieldId} className="flex flex-col items-start p-0 gap-2 w-[902px] h-[78px]">
                      <label className="w-[902px] h-[22px] font-satoshi font-bold text-sm leading-[22px] flex items-center justify-between text-gray-800 dark:text-white">
                        <span>
                          {field.displayName || field.name || 'Unnamed Field'}
                          {isRequired && <span className="text-red-500 ml-1">*</span>}
                        </span>
                        <span className="text-xs font-normal text-gray-500 dark:text-[#6F6F6F]">
                          Type: ({field.inputType})
                        </span>
                      </label>
                      <div className="box-border flex flex-row justify-between items-center py-2 px-3 gap-12 w-[902px] h-12 bg-white dark:bg-[#18181B] border border-gray-200 dark:border-[#4B4B4D] shadow-sm rounded-lg">
                        {field.inputType === 'boolean' ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={parameterValues[fieldId] === true}
                              onChange={(e) => handleParameterChange(fieldId, e.target.checked, field.inputType)}
                              className="h-4 w-4 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-purple-600 focus:ring-purple-500"
                            />
                            <span className="text-sm text-gray-800 dark:text-white font-satoshi font-medium leading-[22px]">
                              {field.displayName || field.name}
                            </span>
                          </div>
                        ) : field.inputType === 'object' || field.inputType === 'dict' || field.inputType === 'json' ? (
                          <textarea
                            value={typeof parameterValues[fieldId] === 'object' ? JSON.stringify(parameterValues[fieldId], null, 2) : parameterValues[fieldId] || '{}'}
                            onChange={(e) => {
                              try {
                                const parsed = JSON.parse(e.target.value);
                                handleParameterChange(fieldId, parsed, field.inputType);
                              } catch {
                                handleParameterChange(fieldId, e.target.value, field.inputType);
                              }
                            }}
                            placeholder="Enter JSON object..."
                            className={`w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] font-mono resize-none ${parameterErrors[fieldId] ? 'text-red-500' : ''}`}
                            rows={1}
                          />
                        ) : field.inputType === 'array' || field.inputType === 'list' ? (
                          <textarea
                            value={Array.isArray(parameterValues[fieldId]) ? JSON.stringify(parameterValues[fieldId], null, 2) : parameterValues[fieldId] || '[]'}
                            onChange={(e) => {
                              try {
                                const parsed = JSON.parse(e.target.value);
                                handleParameterChange(fieldId, parsed, field.inputType);
                              } catch {
                                handleParameterChange(fieldId, e.target.value, field.inputType);
                              }
                            }}
                            placeholder="Enter JSON array..."
                            className={`w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] font-mono resize-none ${parameterErrors[fieldId] ? 'text-red-500' : ''}`}
                            rows={1}
                          />
                        ) : field.inputType === 'number' || field.inputType === 'int' || field.inputType === 'float' ? (
                          <input
                            type="number"
                            value={parameterValues[fieldId] || ''}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                              handleParameterChange(fieldId, value, field.inputType);
                            }}
                            placeholder={`Enter ${field.displayName || field.name}...`}
                            className={`w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] ${parameterErrors[fieldId] ? 'text-red-500' : ''}`}
                          />
                        ) : (
                          <input
                            type="text"
                            value={parameterValues[fieldId] || ''}
                            onChange={(e) => handleParameterChange(fieldId, e.target.value, field.inputType)}
                            placeholder={`Enter ${field.displayName || field.name}...`}
                            className={`w-full h-[30px] bg-transparent border-none outline-none text-gray-800 dark:text-white text-sm font-satoshi font-medium leading-[22px] ${parameterErrors[fieldId] ? 'text-red-500' : ''}`}
                          />
                        )}
                      </div>
                      {parameterErrors[fieldId] && (
                        <p className="text-xs text-red-500 font-satoshi">{parameterErrors[fieldId]}</p>
                      )}
                    </div>
                  );
                })}
              </>
            ) : (
              <div className="w-full h-[200px] flex flex-col items-center justify-center text-gray-500 dark:text-[#6F6F6F] text-center">
                <p className="text-base mb-2">No input parameters required</p>
                <p className="text-sm">This workflow can be scheduled without additional parameters</p>
              </div>
            )}
          </>
        )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="w-[912px] flex flex-row items-center p-0 gap-5 justify-center">
        {/* {view === 'list' && (
          <>
            <button
              onClick={() => {}}
              className="box-border flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 bg-white dark:bg-white border border-gray-200 dark:border-[#EFF0F3] rounded-md cursor-pointer"
            >
              <div className="flex flex-row items-start p-0 px-1 h-5">
                <span className="font-satoshi font-bold text-sm leading-5 text-gray-800 dark:text-[#313131]">
                  Cancel
                </span>
              </div>
            </button>
            <button
              onClick={() => setView('create')}
              className="flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 bg-purple-600 dark:bg-[#AE00D0] rounded-md border-none cursor-pointer"
            >
              <div className="flex flex-row items-start p-0 px-1 h-5">
                <span className="font-satoshi font-bold text-sm leading-5 text-white">
                  Next
                </span>
              </div>
            </button>
          </>
        )} */}
        {(view === 'create' || view === 'edit' || view === 'parameters') && (
          <>
            <button
              onClick={() => {
                if (view === 'parameters') {
                  setView(editingScheduler ? 'edit' : 'create');
                } else {
                  setView('list');
                  setEditingScheduler(null);
                  const rawTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                  const normalizedTimezone = normalizeTimezone(rawTimezone);
                  const matchedTimezone = SUPPORTED_TIMEZONES.includes(normalizedTimezone) ? normalizedTimezone : 'UTC';
                  setFormData({
                    name: '', frequency: 'daily', time: '09:00', timezone: matchedTimezone,
                    days_of_week: [], days_of_month: [], cron_expression: '', is_active: true
                  });
                  setParameterValues({});
                  setParameterErrors({});
                  setInputParameters([]);
                }
              }}
              className="box-border flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 bg-white dark:bg-white border border-gray-200 dark:border-[#EFF0F3] rounded-md cursor-pointer"
            >
              <div className="flex flex-row items-start p-0 px-1 h-5">
                <span className="font-satoshi font-bold text-sm leading-5 text-gray-800 dark:text-[#313131]">
                  {view === 'parameters' ? 'Back' : 'Cancel'}
                </span>
              </div>
            </button>
            <button
              onClick={view === 'edit' ? handleEditFormSubmit : 
                       view === 'parameters' ? (editingScheduler ? handleUpdateSubmit : handleSubmit) : handleFormSubmit}
              disabled={isSubmitting || isLoadingParameters}
              className="flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 bg-[#AE00D0] dark:bg-[#AE00D0] rounded-md border-none cursor-pointer disabled:opacity-70"
            >
              <div className="flex flex-row items-start p-0 px-1 h-5">
                <span className="font-satoshi font-bold text-sm leading-5 text-white">
                  {isLoadingParameters ? 'Loading...' :
                   isSubmitting ? 
                    (editingScheduler ? 'Updating...' : 'Creating...') : 
                    (view === 'edit' ? 'Next' : 
                     view === 'parameters' ? (editingScheduler ? 'Update' : 'Create') : 'Next')}
                </span>
              </div>
            </button>
          </>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteConfirmation.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setDeleteConfirmation({ isOpen: false, schedulerId: '', schedulerName: '' });
          }
        }}
        title="Delete Scheduler"
        description={`Are you sure you want to delete "${deleteConfirmation.schedulerName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        isLoading={isDeleting}
        onConfirm={handleDeleteConfirm}
      />
    </>
  );
};