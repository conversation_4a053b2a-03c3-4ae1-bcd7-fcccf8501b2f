"use client"

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus, X } from "lucide-react";
import { toast } from "sonner";
import { JsonEditorWithPrettify } from "@/components/common/JsonEditorWithPrettify";

// Form schema for validation
const propertySchema = z.object({
  type: z.enum(["string", "number", "boolean", "array"]),
  title: z.string().min(1, "Title is required").max(50, "Title must be at most 50 characters"),
  description: z.string().min(1, "Description is required").max(200, "Description must be at most 200 characters"),
});

const outputSchemaFormSchema = z.object({
  properties: z.array(propertySchema).min(1, "At least one property is required"),
});

type OutputSchemaFormData = z.infer<typeof outputSchemaFormSchema>;

interface AddOutputSchemaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  toolName: string;
  onSubmit: (schema: object) => void;
}

export const AddOutputSchemaDialog: React.FC<AddOutputSchemaDialogProps> = ({
  open,
  onOpenChange,
  toolName,
  onSubmit,
}) => {
  // Tab state
  const [activeTab, setActiveTab] = useState<"properties" | "json">("properties");

  // JSON tab state
  const [schemaString, setSchemaString] = useState("");
  const [isValidJson, setIsValidJson] = useState(true);

  // Property form
  const form = useForm<OutputSchemaFormData>({
    resolver: zodResolver(outputSchemaFormSchema),
    defaultValues: {
      properties: [{ type: "string", title: "", description: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "properties",
  });

  // JSON tab handlers
  const handleSchemaChange = (jsonString: string) => {
    setSchemaString(jsonString);
    try {
      JSON.parse(jsonString);
      setIsValidJson(true);
    } catch (e) {
      setIsValidJson(false);
    }
  };

  // Property form submit handler
  const handleFormSubmit = (data: OutputSchemaFormData) => {
    // Transform the form data into the required JSON schema format
    const schema = {
      properties: data.properties.reduce((acc, property) => {
        acc[property.title] = {
          type: property.type,
          description: property.description,
          title: property.title,
        };
        return acc;
      }, {} as Record<string, any>),
    };

    onSubmit(schema);
    toast.success(`Output schema for '${toolName}' submitted.`);
    onOpenChange(false);
    resetForms();
  };

  // JSON submit handler
  const handleJsonSubmit = () => {
    try {
      const parsedSchema = JSON.parse(schemaString);
      onSubmit(parsedSchema);
      toast.success(`Output schema for '${toolName}' submitted.`);
      onOpenChange(false);
      resetForms();
    } catch (e) {
      toast.error("Invalid JSON. Please correct the schema before submitting.");
      setIsValidJson(false);
    }
  };

  // Combined submit handler
  const handleSubmit = () => {
    if (activeTab === "properties") {
      form.handleSubmit(handleFormSubmit)();
    } else {
      handleJsonSubmit();
    }
  };

  const addProperty = () => {
    append({ type: "string", title: "", description: "" });
  };

  const removeProperty = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  // Handle button clicks to prevent event propagation
  const handleAddPropertyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    addProperty();
  };

  const handleRemovePropertyClick = (index: number) => (e: React.MouseEvent) => {
    e.stopPropagation();
    removeProperty(index);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value as "properties" | "json");
  };

  const resetForms = () => {
    form.reset();
    setSchemaString("");
    setIsValidJson(true);
  };

  const handleClose = () => {
    resetForms();
    onOpenChange(false);
  };

  // Check if current tab is valid for submission
  const isSubmitDisabled = () => {
    if (activeTab === "properties") {
      return !form.formState.isValid;
    } else {
      return !isValidJson || !schemaString.trim();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent 
        className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto"
        onPointerDownOutside={(e) => {
          // Only prevent if it's not a button click
          const target = e.target as Element;
          if (!target.closest('button') && !target.closest('[role="button"]')) {
            e.preventDefault();
          }
        }}
        onInteractOutside={(e) => {
          // Only prevent if it's not a button click
          const target = e.target as Element;
          if (!target.closest('button') && !target.closest('[role="button"]')) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>Add Output Schema for: {toolName}</DialogTitle>
          <DialogDescription>
            Define the output schema for this tool using either property-based form or JSON input.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
          <TabsList className="grid w-auto max-w-[50%] grid-cols-2" onClick={(e) => e.stopPropagation()}>
            <TabsTrigger value="properties">Properties</TabsTrigger>
            <TabsTrigger value="json">JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="properties" className="space-y-4">
            <Form {...form}>
              <div className="py-4" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Properties</h3>
                  <Button type="button" variant="outline" size="sm" onClick={handleAddPropertyClick}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Property
                  </Button>
                </div>

                <div className="space-y-4">
                  {fields.map((field, index) => (
                    <div key={field.id} className="border rounded-lg p-4 space-y-4" onClick={(e) => e.stopPropagation()}>
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">Property {index + 1}</h4>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={handleRemovePropertyClick(index)}
                            className="text-destructive hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name={`properties.${index}.type`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Type</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="string">String</SelectItem>
                                  <SelectItem value="number">Number</SelectItem>
                                  <SelectItem value="boolean">Boolean</SelectItem>
                                  <SelectItem value="array">Array</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`properties.${index}.title`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Title</FormLabel>
                              <FormControl>
                                <Input placeholder="Property title" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`properties.${index}.description`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Input placeholder="Property description" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Form>
          </TabsContent>

          <TabsContent value="json" className="space-y-4">
            <div className="py-4" onClick={(e) => e.stopPropagation()}>
              <h3 className="text-lg font-medium mb-4">JSON Schema</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Enter your JSON schema directly. Use the editor below to paste or write your schema.
              </p>
              <JsonEditorWithPrettify
                initialJsonString={schemaString}
                onJsonChange={handleSchemaChange}
                height="300px"
              />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter onClick={(e) => e.stopPropagation()}>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitDisabled()}>
            Save Schema
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
