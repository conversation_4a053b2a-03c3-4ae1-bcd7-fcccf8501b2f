"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ExternalLink, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { OAuthDetails } from "@/types";
import { initiateOAuthFlow, checkOAuthCredentials, API_BASE_URL } from "@/lib/api";
import { useUserStore } from "@/store/userStore";
import { checkOAuthSuccess, clearOAuthSuccess, openOAuthInNewTab } from "@/lib/oauthUtils";
import { getClientAccessToken } from "@/lib/clientCookies";
import Image from "next/image";

interface OAuthConnectionStatusProps {
  oauthDetails: OAuthDetails;
  toolName: string;
  nodeId: string;
  onConnectionChange: (isConnected: boolean) => void;
  className?: string;
  displayMode?: "compact" | "inline"; // New prop for display mode
  serviceLogo?: string; // Logo URL passed from parent component
  initialConnectionState?: {
    isConnected: boolean;
    provider?: string;
    connectedAt?: string;
    expiresAt?: string;
  };
}

export const OAuthConnectionStatus: React.FC<OAuthConnectionStatusProps> = ({
  oauthDetails,
  toolName,
  nodeId,
  onConnectionChange,
  className = "",
  displayMode = "compact",
  serviceLogo,
  initialConnectionState,
}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    is_connected: boolean;
    expires_in?: number;
  }>({
    is_connected: initialConnectionState?.isConnected ?? oauthDetails.is_connected,
    expires_in: undefined,
  });

  // Get current user from store
  const user = useUserStore((state) => state.user);

  // Add debouncing for OAuth status checks
  const lastCheckRef = useRef<number>(0);
  const CHECK_DEBOUNCE_TIME = 1000; // 1 second

  // Check connection status with debouncing
  const checkConnectionStatus = async () => {
    try {
      // Debounce OAuth status checks
      const now = Date.now();
      if (now - lastCheckRef.current < CHECK_DEBOUNCE_TIME) {
        return; // Skip if called too recently
      }
      lastCheckRef.current = now;

      if (process.env.NODE_ENV === 'development') {
        console.log(`Checking OAuth status for ${oauthDetails.tool_name} (${oauthDetails.provider})`);
      }

      const result = await checkOAuthCredentials({
        tool_name: oauthDetails.tool_name,
        provider: oauthDetails.provider,
      });

      if (process.env.NODE_ENV === 'development') {
        console.log(`OAuth status result:`, result);
      }

      if (result.success) {
        const newStatus = {
          is_connected: result.is_connected || false,
          expires_in: result.expires_in,
        };

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updating connection status:`, newStatus);
        }
        setConnectionStatus(newStatus);
        onConnectionChange(newStatus.is_connected);

        // Also update the original oauth_details object
        if (oauthDetails) {
          oauthDetails.is_connected = newStatus.is_connected;
        }
      }
    } catch (error) {
      console.error("Error checking OAuth status:", error);
    }
  };

  // Listen for OAuth callback success (both sessionStorage and postMessage)
  useEffect(() => {
    const handleOAuthSuccess = () => {
      const successData = checkOAuthSuccess(oauthDetails.tool_name, oauthDetails.provider);
      if (successData) {
        // Clear the success flag
        clearOAuthSuccess();
        // Refresh connection status
        checkConnectionStatus();
        // Show success toast
        toast.success(`Successfully connected to ${successData.provider} for ${successData.toolName}`);
      }
    };

    // Listen for postMessage from OAuth popup
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) return;

      // Filter out unrelated messages (like MetaMask)
      if (!event.data || typeof event.data !== 'object') return;

      // Only process OAuth-related messages
      if (event.data.type !== 'OAUTH_SUCCESS' && event.data.type !== 'OAUTH_ERROR') {
        // Ignore non-OAuth messages (like MetaMask messages)
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('Received OAuth postMessage:', event.data);
        console.log('Current tool details:', { toolName: oauthDetails.tool_name, provider: oauthDetails.provider });
      }

      if (event.data.type === 'OAUTH_SUCCESS') {
        // Check if this success is for our tool (match by provider since tool_name might not be passed correctly)
        if (event.data.provider === oauthDetails.provider) {
          if (process.env.NODE_ENV === 'development') {
            console.log('OAuth success matched for provider:', event.data.provider);
          }
          // Refresh connection status
          setTimeout(() => {
            checkConnectionStatus();
          }, 500);
          // Show success toast
          toast.success(`Successfully connected to ${event.data.provider}!`);
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('OAuth success did not match. Expected provider:', oauthDetails.provider, 'Received:', event.data.provider);
          }
        }
      }
    };

    // Check immediately when component mounts
    handleOAuthSuccess();

    // Set up interval to check for success (fallback for sessionStorage method)
    const interval = setInterval(handleOAuthSuccess, 1000);

    // Add message listener for new tab OAuth flow
    window.addEventListener('message', handleMessage);

    return () => {
      clearInterval(interval);
      window.removeEventListener('message', handleMessage);
    };
  }, [oauthDetails.tool_name, oauthDetails.provider]);

  // Also listen for focus events to refresh status when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      // Refresh connection status when window gains focus
      checkConnectionStatus();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  // Check connection status on mount
  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const handleConnect = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering inspector panel
    setIsConnecting(true);
    try {
      // Get current user ID from auth context
      const userId = user?.id || user?.email || "anonymous_user";

      // Build OAuth URL with parameters
      const queryParams = new URLSearchParams({
        provider: oauthDetails.provider,
        tool_name: oauthDetails.tool_name,
        user_id: userId,
        redirect_url: window.location.origin + "/oauth/callback",
      });

      if (oauthDetails.scopes) {
        queryParams.append("scopes", oauthDetails.scopes.join(","));
      }

      const oauthUrl = `${API_BASE_URL}/oauth/authorize?${queryParams.toString()}`;

      console.log('Opening OAuth URL:', oauthUrl);
      console.log('OAuth details:', oauthDetails);

      // Open OAuth in new tab
      openOAuthInNewTab(
        oauthUrl,
        () => {
          // Success callback
          toast.success(`Successfully connected to ${oauthDetails.provider}!`);

          // Force refresh connection status multiple times to ensure it updates
          setTimeout(() => checkConnectionStatus(), 100);
          setTimeout(() => checkConnectionStatus(), 500);
          setTimeout(() => checkConnectionStatus(), 1000);

          setIsConnecting(false);
        },
        (error) => {
          // Error callback
          toast.error(`OAuth connection failed: ${error}`);
          setIsConnecting(false);
        }
      );

      toast.info(`Opening ${oauthDetails.provider} authorization in new tab...`);
    } catch (error) {
      console.error("Error initiating OAuth flow:", error);
      toast.error("Failed to start OAuth connection");
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering inspector panel
    setIsConnecting(true);
    try {
      // Get access token for authentication using the proper client cookie method
      const accessToken = getClientAccessToken();

      if (!accessToken) {
        throw new Error('No authentication token found. Please log in again.');
      }

      // Build query parameters for the DELETE request
      const queryParams = new URLSearchParams({
        tool_name: oauthDetails.tool_name,
        provider: oauthDetails.provider,
      });

      const response = await fetch(`${API_BASE_URL}/oauth/credentials?${queryParams.toString()}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const result = await response.json();

      if (response.ok && result.success !== false) {
        // Successfully disconnected
        toast.success(`Successfully disconnected from ${oauthDetails.provider}!`);

        // Update connection status
        setConnectionStatus({
          is_connected: false,
          expires_in: undefined,
        });

        // Notify parent component
        onConnectionChange(false);
      } else {
        throw new Error(result.message || 'Failed to disconnect');
      }
    } catch (error) {
      console.error("Error disconnecting OAuth:", error);
      toast.error(`Failed to disconnect from ${oauthDetails.provider}: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const getExpirationText = () => {
    if (!connectionStatus.expires_in) return null;
    
    const hours = Math.floor(connectionStatus.expires_in / 3600);
    const minutes = Math.floor((connectionStatus.expires_in % 3600) / 60);
    
    if (hours > 0) {
      return `Expires in ${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `Expires in ${minutes}m`;
    } else {
      return "Expires soon";
    }
  };

  if (connectionStatus.is_connected) {
    // Inline display mode for connected state - show disconnect button
    if (displayMode === "inline") {
      const providerName = oauthDetails.provider;

      return (
        <div className="flex flex-col items-start p-3 gap-3 w-full rounded-lg">
          {/* Header Section */}
          

          {/* Disconnect Button - Frame 20 */}
          <button
            onClick={handleDisconnect}
            disabled={isConnecting}
            className="flex flex-row items-center transition-colors duration-200 disabled:opacity-50"
            style={{
              boxSizing: "border-box",
              padding: "5px 7px",
              gap: "4px",
              width: "89px",
              height: "26px",
              background: "var(--container-Background, #FFFFFF)",
              border: "1px solid #22C55E",
              borderRadius: "4px",
              flex: "none",
              order: 2,
              flexGrow: 0,
            }}
          >
            {/* Icon Container - Group 19 */}
            <div
              style={{
                width: "12px",
                height: "12px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                position: "relative",
              }}
            >
              {isConnecting ? (
                <Loader2 className="h-3 w-3 animate-spin text-white" />
              ) : serviceLogo ? (
                <Image
                  src={serviceLogo}
                  alt={`${providerName} logo`}
                  width={12}
                  height={12}
                  style={{
                    objectFit: "cover",
                  }}
                />
              ) : (
                <div
                  style={{
                    width: "12px",
                    height: "12px",
                    background: "#FFFFFF",
                    borderRadius: "2px",
                  }}
                />
              )}
            </div>

            {/* Button Text */}
            <span
              style={{
                width: "59px",
                height: "16px",
                fontFamily: "'Satoshi', 'Geist', system-ui, sans-serif",
                fontStyle: "normal",
                fontWeight: 400,
                fontSize: "10px",
                lineHeight: "16px",
                textAlign: "center",
                color: "var(--White, #000000)",
                flex: "none",
                order: 1,
                flexGrow: 0,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {isConnecting ? "..." : "Connected"}
            </span>
          </button>
        </div>
      );
    }

    // For compact mode when connected, show a simple connected status
    return (
      <div className="flex items-center gap-2 p-2 w-full bg-green-50 border border-green-200 rounded-lg dark:bg-green-950/30 dark:border-green-700">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <span className="text-xs text-green-800 dark:text-green-300">
          Connected to {oauthDetails.provider}
        </span>
      </div>
    );
  }

  // Inline display mode for prominent connect button (similar to OAuthConnectNode)
  if (displayMode === "inline") {
    const providerName = oauthDetails.provider;

    return (
      <div className="flex flex-col items-start p-3 gap-3 w-full rounded-lg">
        {/* Header Section */}
        

        {/* Connect Button - Frame 20 */}
        <button
          onClick={handleConnect}
          disabled={isConnecting}
          className="flex flex-row items-center transition-colors duration-200 disabled:opacity-50"
          style={{
            boxSizing: "border-box",
            padding: "5px 7px",
            gap: "4px",
            width: "155px",
            height: "26px",
            background: "var(--container-Background, #FFFFFF)",
            border: "1px solid #4B4B4D",
            borderRadius: "4px",
            flex: "none",
            order: 2,
            flexGrow: 0,
          }}
        >
          {/* Icon Container - Group 19 */}
          <div
            style={{
              width: "12px",
              height: "12px",
              flex: "none",
              order: 0,
              flexGrow: 0,
              position: "relative",
            }}
          >
            {isConnecting ? (
              <Loader2 className="h-3 w-3 animate-spin text-white" />
            ) : serviceLogo ? (
              <Image
                src={serviceLogo}
                alt={`${providerName} logo`}
                width={12}
                height={12}
                style={{
                  objectFit: "cover",
                }}
              />
            ) : (
              <div
                style={{
                  width: "12px",
                  height: "12px",
                  background: "#FFFFFF",
                  borderRadius: "2px",
                }}
              />
            )}
          </div>

          {/* Button Text */}
          <span
            style={{
              width: "125px",
              height: "16px",
              fontFamily: "'Satoshi', 'Geist', system-ui, sans-serif",
              fontStyle: "normal",
              fontWeight: 400,
              fontSize: "10px",
              lineHeight: "16px",
              color: "var(--White, #000000)",
              flex: "none",
              order: 1,
              flexGrow: 0,
            }}
          >
            {isConnecting ? "Connecting..." : `Sign in with ${providerName.toLowerCase()}`}
          </span>
        </button>
      </div>
    );
  }

  // Compact display mode (original behavior)
  return (
    <TooltipProvider>
      <div className={`relative rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50 p-1 shadow-sm dark:border-orange-800 dark:from-orange-950/30 dark:to-amber-950/30 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-orange-500 text-white shadow-sm">
              <AlertCircle className="h-3.5 w-3.5" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs font-small text-orange-800 dark:text-orange-300">
                OAuth Required
              </span>
              <span className="text-[10px] text-orange-600 dark:text-orange-400">
                {oauthDetails.provider}
              </span>
            </div>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={handleConnect}
                disabled={isConnecting}
                className="h-7 px-1 text-xs font-medium border-orange-300 bg-white text-orange-700 hover:bg-orange-100 hover:border-orange-400 disabled:opacity-50 dark:border-orange-700 dark:bg-orange-950/50 dark:text-orange-300 dark:hover:bg-orange-900/50"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1.5 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-3 w-3 mr-1.5" />
                    Connect
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="p-2 text-xs">
              <div className="font-medium">Connect to {oauthDetails.provider}</div>
              <div className="text-muted-foreground text-[10px]">
                Required to use this tool in your workflow
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
};
