// Debug script for MCP Tools component
// Add this to your browser console when inspecting the component

// Function to log the current state of the MCP Tools component
function debugMCPTools() {
  // Find the MCP Tools component in the inspector panel
  const inspectorPanel = document.querySelector("[data-inspector-panel]");
  if (!inspectorPanel) {
    console.error("Inspector panel not found");
    return;
  }

  // Get all inputs in the inspector panel
  const inputs = inspectorPanel.querySelectorAll("input, select, button");
  console.log("Inputs in inspector panel:", inputs);

  // Find the mode dropdown
  const modeDropdown = Array.from(inputs).find((input) => input.id && input.id.includes("mode"));
  console.log("Mode dropdown:", modeDropdown);

  if (modeDropdown) {
    console.log("Current mode:", modeDropdown.value);
  }

  // Find the command input
  const commandInput = Array.from(inputs).find((input) => input.id && input.id.includes("command"));
  console.log("Command input:", commandInput);
  console.log(
    "Command input visibility:",
    commandInput ? getComputedStyle(commandInput.closest(".space-y-2")).display : "N/A",
  );

  // Find the SSE URL input
  const sseUrlInput = Array.from(inputs).find((input) => input.id && input.id.includes("sse_url"));
  console.log("SSE URL input:", sseUrlInput);
  console.log(
    "SSE URL input visibility:",
    sseUrlInput ? getComputedStyle(sseUrlInput.closest(".space-y-2")).display : "N/A",
  );

  // Get the component's config from React DevTools
  console.log(
    "To get the component config, use React DevTools to inspect the InspectorPanel component",
  );
  console.log("Look for selectedNode.data.config to see the current configuration");
}

// Run the debug function
debugMCPTools();
