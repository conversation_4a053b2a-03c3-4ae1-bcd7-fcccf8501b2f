import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
import enum

from pydantic import BaseModel, Field

# --- Pydantic Enums (mirroring gRPC enums) ---
class ActivityType(str, enum.Enum):
    WORKFLOW = "WORKFLOW"
    AGENT = "AGENT"

class ActivityStatus(str, enum.Enum):
    STARTED = "ACTIVITY_STATUS_STARTED"
    IN_PROGRESS = "ACTIVITY_STATUS_IN_PROGRESS"
    COMPLETED = "ACTIVITY_STATUS_COMPLETED"
    FAILED = "ACTIVITY_STATUS_FAILED"
    PAUSED = "ACTIVITY_STATUS_PAUSED"
    CANCELLED = "ACTIVITY_STATUS_CANCELLED"

class LogType(str, enum.Enum):
    TIME_LOG = "TIME_LOG"
    RESULT_LOG = "RESULT_LOG"

class LogStatus(str, enum.Enum):
    SUCCESS = "LOG_STATUS_SUCCESS"
    FAILURE = "LOG_STATUS_FAILURE"
    PAUSED = "LOG_STATUS_PAUSED"
    APPROVED = "LOG_STATUS_APPROVED"
    CANCELLED = "LOG_STATUS_CANCELLED"

# --- Pydantic Schemas for API requests and responses ---

class ActivitySchema(BaseModel):
    id: str
    resource_id: str
    type: ActivityType
    user_id: str
    status: ActivityStatus
    user_metadata: Optional[Dict[str, Any]] = None
    resource_name: Optional[str] = None
    logs: List['ActivityLogSchema'] = []
    events: List['ActivityEventSchema'] = []
    created_at: datetime
    updated_at: datetime
    correlation_id: Optional[str] = None

class ActivityLogSchema(BaseModel):
    id: str
    activity_id: str
    log_type: LogType
    log_status: LogStatus
    log_details: Optional[Dict[str, Any]] = None
    created_at: datetime

class ActivityEventSchema(BaseModel):
    id: str
    activity_id: str
    event_name: str
    event_details: Optional[Dict[str, Any]] = None
    created_at: datetime

class CreateActivityRequestSchema(BaseModel):
    resource_id: str = Field(..., example="workflow-123")
    user_id: str = Field(..., example="user-123")
    type: ActivityType = Field(..., example=ActivityType.WORKFLOW)
    status: ActivityStatus = Field(..., example=ActivityStatus.STARTED)
    user_metadata: Optional[Dict[str, Any]] = None
    correlation_id: Optional[str] = Field(None, example="workflow-exec-123")

class CreateActivityResponseSchema(BaseModel):
    success: bool
    message: str
    activity: ActivitySchema

class CreateActivityLogRequestSchema(BaseModel):
    activity_id: str = Field(..., example="activity-789")
    user_id: str = Field(..., example="user-123")
    log_type: LogType = Field(..., example=LogType.TIME_LOG)
    log_status: LogStatus = Field(..., example=LogStatus.SUCCESS)
    log_details: Optional[Dict[str, Any]] = None
    activity_status: Optional[ActivityStatus] = Field(None, example=ActivityStatus.IN_PROGRESS)

class CreateActivityEventRequestSchema(BaseModel):
    resource_id: str = Field(..., example="workflow-123")
    user_id: str = Field(..., example="user-123")
    type: ActivityType = Field(..., example=ActivityType.WORKFLOW)
    status: ActivityStatus = Field(..., example=ActivityStatus.STARTED)
    user_metadata: Optional[Dict[str, Any]] = None
    event_name: str = Field(..., example="workflow.started")
    event_details: Optional[Dict[str, Any]] = None
    correlation_id: Optional[str] = Field(None, example="workflow-exec-123")

class CreateActivityEventResponseSchema(BaseModel):
    success: bool
    message: str
    event: ActivityEventSchema

class UpdateActivityRequestSchema(BaseModel):
    user_id: str = Field(..., example="user-123")
    status: Optional[ActivityStatus] = Field(None, example=ActivityStatus.COMPLETED)
    user_metadata: Optional[Dict[str, Any]] = None

class UpdateActivityResponseSchema(BaseModel):
    success: bool
    message: str
    activity: Optional[ActivitySchema] = None

class DeleteActivityRequestSchema(BaseModel):
    id: str = Field(..., example="activity-789")

class DeleteActivityResponseSchema(BaseModel):
    success: bool
    message: str

class GetActivityResponseSchema(BaseModel):
    activity: ActivitySchema
    logs: List[ActivityLogSchema]
    events: List[ActivityEventSchema]

class PaginationMetadata(BaseModel):
    total: int
    total_pages: int
    current_page: int
    page_size: int
    has_next_page: bool
    has_previous_page: bool

class ListActivitiesResponseSchema(BaseModel):
    success: bool
    message: str
    activities: List[ActivitySchema]
    metadata: PaginationMetadata

class ListActivityLogsRequestSchema(BaseModel):
    activity_id: Optional[str] = Field(None, example="activity-789")
    log_type: Optional[LogType] = Field(None, example=LogType.TIME_LOG)
    log_status: Optional[LogStatus] = Field(None, example=LogStatus.SUCCESS)
    page_size: Optional[int] = Field(10, example=10)
    offset: Optional[int] = Field(0, example=0)

class ListActivityLogsResponseSchema(BaseModel):
    success: bool
    message: str
    data: List[ActivityLogSchema]
    metadata: PaginationMetadata

class ListActivityEventsRequestSchema(BaseModel):
    activity_id: Optional[str] = Field(None, example="activity-789")
    event_name: Optional[str] = Field(None, example="workflow.started")
    page_size: Optional[int] = Field(10, example=10)
    offset: Optional[int] = Field(0, example=0)

class ListActivityEventsResponseSchema(BaseModel):
    success: bool
    message: str
    data: List[ActivityEventSchema]
    metadata: PaginationMetadata

# Enable forward references for ActivitySchema
ActivitySchema.model_rebuild()