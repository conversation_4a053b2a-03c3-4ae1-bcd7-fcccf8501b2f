"""
Activity Service Client

This module provides a client for communicating with the Activity Service via gRPC.
It handles all activity management operations including activities, logs, and events.
"""

import logging
from typing import Optional, List, Dict, Any
import grpc
from grpc import RpcError
from datetime import datetime, timezone

from app.core.config import settings
from app.grpc_ import analytics_pb2, analytics_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from google.protobuf.struct_pb2 import Struct
from google.protobuf import json_format

logger = logging.getLogger(__name__)


class ActivityServiceClient:
    """Client for Activity Service gRPC communication."""

    def __init__(self):
        """Initialize the Activity Service client."""
        self.channel = None
        self.activity_stub = None
        self._connect()

    def _connect(self):
        """Establish gRPC connection to Activity Service."""
        try:
            analytics_address = (
                f"{settings.ANALYTICS_SERVICE_HOST}:{settings.ANALYTICS_SERVICE_PORT}"
            )
            self.channel = grpc.insecure_channel(analytics_address)
            self.activity_stub = analytics_pb2_grpc.ActivityServiceStub(self.channel)
            logger.info(f"Connected to Activity Service at {analytics_address}")
        except Exception as e:
            logger.error(f"Failed to connect to Activity Service: {e}")
            raise

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()

    # ===== HELPER FUNCTIONS =====

    def _datetime_to_grpc_timestamp(self, dt: Optional[datetime]) -> Optional[Timestamp]:
        if dt is None:
            return None
        ts = Timestamp()
        aware_dt = dt.astimezone(timezone.utc) if dt.tzinfo else dt.replace(tzinfo=timezone.utc)
        ts.FromDatetime(aware_dt)
        return ts

    def _grpc_timestamp_to_datetime(self, ts: Optional[Timestamp]) -> Optional[datetime]:
        if ts is None or (ts.seconds == 0 and ts.nanos == 0):
            return None
        return ts.ToDatetime().replace(tzinfo=timezone.utc)

    def _dict_to_grpc_struct(self, data: Optional[Dict[str, Any]]) -> Optional[Struct]:
        if data is None:
            return None
        s = Struct()
        json_format.ParseDict(data, s)
        return s

    def _grpc_struct_to_dict(self, s: Optional[Struct]) -> Optional[Dict[str, Any]]:
        if s is None:
            return None
        return json_format.MessageToDict(s)

    def _activity_proto_to_dict(self, activity: analytics_pb2.Activity) -> Dict[str, Any]:
        return {
            "id": activity.id,
            "resource_id": activity.resource_id,
            "correlation_id": activity.correlation_id,
            "type": analytics_pb2.ActivityType.Name(activity.type),
            "user_id": activity.user_id,
            "status": analytics_pb2.ActivityStatus.Name(activity.status),
            "user_metadata": self._grpc_struct_to_dict(activity.user_metadata),
            "logs": [self._log_proto_to_dict(log) for log in activity.logs],
            "events": [self._event_proto_to_dict(event) for event in activity.events],
            "created_at": self._grpc_timestamp_to_datetime(activity.created_at),
            "updated_at": self._grpc_timestamp_to_datetime(activity.updated_at),
        }

    def _log_proto_to_dict(self, log: analytics_pb2.ActivityLog) -> Dict[str, Any]:
        return {
            "id": log.id,
            "activity_id": log.activity_id,
            "log_type": analytics_pb2.LogType.Name(log.log_type),
            "log_status": analytics_pb2.LogStatus.Name(log.log_status),
            "log_details": self._grpc_struct_to_dict(log.log_details),
            "created_at": self._grpc_timestamp_to_datetime(log.created_at),
        }

    def _event_proto_to_dict(self, event: analytics_pb2.ActivityEvent) -> Dict[str, Any]:
        return {
            "id": event.id,
            "activity_id": event.activity_id,
            "event_name": event.event_name,
            "event_details": self._grpc_struct_to_dict(event.event_details),
            "created_at": self._grpc_timestamp_to_datetime(event.created_at),
        }

    # ===== ACTIVITY MANAGEMENT =====

    async def create_activity(self, activity_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            request = analytics_pb2.CreateActivityRequest(
                resource_id=activity_data["resource_id"],
                type=analytics_pb2.ActivityType.Value(activity_data["type"]),
                user_id=activity_data["user_id"],
                status=analytics_pb2.ActivityStatus.Value(activity_data["status"]),
                user_metadata=self._dict_to_grpc_struct(activity_data.get("user_metadata")),
                correlation_id=activity_data.get("correlation_id", ""),
            )
            response = self.activity_stub.CreateActivity(request)
            return {
                "success": response.success,
                "message": response.message,
                "activity": self._activity_proto_to_dict(response.activity)
            }
        except RpcError as e:
            logger.error(f"gRPC error creating activity: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "activity": None
            }
        except Exception as e:
            logger.error(f"Error creating activity: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "activity": None
            }

    async def create_activity_log(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            request = analytics_pb2.CreateActivityLogRequest(
                activity_id=log_data["activity_id"],
                log_type=analytics_pb2.LogType.Value(log_data["log_type"]),
                log_status=analytics_pb2.LogStatus.Value(log_data["log_status"]),
                log_details=self._dict_to_grpc_struct(log_data.get("log_details")),
                activity_status=analytics_pb2.ActivityStatus.Value(log_data["activity_status"]) if log_data.get("activity_status") else analytics_pb2.ACTIVITY_STATUS_UNSPECIFIED,
            )
            response = self.activity_stub.CreateActivityLog(request)
            return self._log_proto_to_dict(response)
        except RpcError as e:
            logger.error(f"gRPC error creating activity log: {e}")
            raise
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")
            raise

    async def create_activity_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            request = analytics_pb2.CreateActivityEventRequest(
                resource_id=event_data["resource_id"],
                type=analytics_pb2.ActivityType.Value(event_data["type"]),
                user_id=event_data["user_id"],
                status=analytics_pb2.ActivityStatus.Value(event_data["status"]),
                user_metadata=self._dict_to_grpc_struct(event_data.get("user_metadata")),
                event_name=event_data["event_name"],
                event_details=self._dict_to_grpc_struct(event_data.get("event_details")),
                correlation_id=event_data.get("correlation_id", ""),
            )
            response = self.activity_stub.CreateActivityEvent(request)
            return {
                "success": response.success,
                "message": response.message,
                "event": self._event_proto_to_dict(response.event)
            }
        except RpcError as e:
            logger.error(f"gRPC error creating activity event: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "event": None
            }
        except Exception as e:
            logger.error(f"Error creating activity event: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "event": None
            }

    async def update_activity(self, activity_id: str, user_id: str, activity_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            request_args = {
                "id": activity_id,
                "user_id": user_id,
            }

            if "status" in activity_data and activity_data["status"] is not None:
                request_args["status"] = analytics_pb2.ActivityStatus.Value(activity_data["status"])
            
            if "user_metadata" in activity_data and activity_data["user_metadata"] is not None:
                request_args["user_metadata"] = self._dict_to_grpc_struct(activity_data.get("user_metadata"))

            request = analytics_pb2.UpdateActivityRequest(**request_args)
            
            response = self.activity_stub.UpdateActivity(request)
            return {
                "success": response.success,
                "message": response.message,
                "activity": self._activity_proto_to_dict(response.activity) if response.activity.id else None
            }
        except RpcError as e:
            logger.error(f"gRPC error updating activity: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "activity": None
            }
        except Exception as e:
            logger.error(f"Error updating activity: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "activity": None
            }

    async def delete_activity(self, activity_id: str, user_id: str) -> Dict[str, Any]:
        try:
            request = analytics_pb2.DeleteActivityRequest(
                id=activity_id,
                user_id=user_id,
            )
            response = self.activity_stub.DeleteActivity(request)
            return {
                "success": response.success,
                "message": response.message
            }
        except RpcError as e:
            logger.error(f"gRPC error deleting activity: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Error deleting activity: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}"
            }

    async def get_activity(self, correlation_id: str) -> Dict[str, Any]:
        try:
            request = analytics_pb2.GetActivityRequest(correlation_id=correlation_id)
            response = self.activity_stub.GetActivity(request)
            return {
                "activity": self._activity_proto_to_dict(response.activity),
                "logs": [self._log_proto_to_dict(log) for log in response.logs],
                "events": [self._event_proto_to_dict(event) for event in response.events],
            }
        except RpcError as e:
            logger.error(f"gRPC error getting activity: {e}")
            raise
        except Exception as e:
            logger.error(f"Error getting activity: {e}")
            raise

    async def list_activities(
        self,
        user_id: Optional[str] = None,
        type: Optional[str] = None,
        resource_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ) -> Dict[str, Any]:
        try:
            request = analytics_pb2.ListActivitiesRequest(
                user_id=user_id or "",
                type=analytics_pb2.ActivityType.Value(type) if type else analytics_pb2.ACTIVITY_TYPE_UNSPECIFIED,
                resource_id=resource_id or "",
                page=page,
                page_size=page_size,
            )
            response = self.activity_stub.ListActivities(request)
            return {
                "success": response.success,
                "message": response.message,
                "activities": [self._activity_proto_to_dict(activity) for activity in response.activities],
                "metadata": {
                    "total": response.metadata.total,
                    "total_pages": response.metadata.total_pages,
                    "current_page": response.metadata.current_page,
                    "page_size": response.metadata.page_size,
                    "has_next_page": response.metadata.has_next_page,
                    "has_previous_page": response.metadata.has_previous_page,
                }
            }
        except RpcError as e:
            logger.error(f"gRPC error listing activities: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "activities": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }
        except Exception as e:
            logger.error(f"Error listing activities: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "activities": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }

    async def list_activity_logs(
        self,
        activity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        log_type: Optional[str] = None,
        log_status: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ) -> Dict[str, Any]:
        """List activity logs with pagination and filtering."""
        try:
            request = analytics_pb2.ListActivityLogsRequest(
                activity_id=activity_id or "",
                user_id=user_id or "",
                log_type=analytics_pb2.LogType.Value(log_type) if log_type else analytics_pb2.LOG_TYPE_UNSPECIFIED,
                log_status=analytics_pb2.LogStatus.Value(log_status) if log_status else analytics_pb2.LOG_STATUS_UNSPECIFIED,
                page=page,
                page_size=page_size,
            )
            response = self.activity_stub.ListActivityLogs(request)
            return {
                "success": response.success,
                "message": response.message,
                "data": [self._log_proto_to_dict(log) for log in response.data],
                "metadata": {
                    "total": response.metadata.total,
                    "total_pages": response.metadata.total_pages,
                    "current_page": response.metadata.current_page,
                    "page_size": response.metadata.page_size,
                    "has_next_page": response.metadata.has_next_page,
                    "has_previous_page": response.metadata.has_previous_page,
                }
            }
        except RpcError as e:
            logger.error(f"gRPC error listing activity logs: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "data": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }
        except Exception as e:
            logger.error(f"Error listing activity logs: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "data": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }

    async def list_activity_events(
        self,
        activity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        event_name: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ) -> Dict[str, Any]:
        """List activity events with pagination and filtering."""
        try:
            request = analytics_pb2.ListActivityEventsRequest(
                activity_id=activity_id or "",
                user_id=user_id or "",
                event_name=event_name or "",
                page=page,
                page_size=page_size,
            )
            response = self.activity_stub.ListActivityEvents(request)
            return {
                "success": response.success,
                "message": response.message,
                "data": [self._event_proto_to_dict(event) for event in response.data],
                "metadata": {
                    "total": response.metadata.total,
                    "total_pages": response.metadata.total_pages,
                    "current_page": response.metadata.current_page,
                    "page_size": response.metadata.page_size,
                    "has_next_page": response.metadata.has_next_page,
                    "has_previous_page": response.metadata.has_previous_page,
                }
            }
        except RpcError as e:
            logger.error(f"gRPC error listing activity events: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {str(e)}",
                "data": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }
        except Exception as e:
            logger.error(f"Error listing activity events: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "data": [],
                "metadata": {
                    "total": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": page_size,
                    "has_next_page": False,
                    "has_previous_page": False,
                }
            }

    async def list_activity_logs_by_correlation_id(self, correlation_id: str) -> Dict[str, Any]:
        """List activity logs by correlation ID."""
        try:
            request = analytics_pb2.ListActivityLogsByCorrelationIdRequest(correlation_id=correlation_id)
            response = self.activity_stub.ListActivityLogsByCorrelationId(request)
            return {
                "success": response.success,
                "message": response.message,
                "data": [self._log_proto_to_dict(log) for log in response.data],
                "metadata": {
                    "total": response.metadata.total,
                    "total_pages": response.metadata.total_pages,
                    "current_page": response.metadata.current_page,
                    "page_size": response.metadata.page_size,
                    "has_next_page": response.metadata.has_next_page,
                    "has_previous_page": response.metadata.has_previous_page,
                }
            }
        except RpcError as e:
            logger.error(f"gRPC error listing activity logs by correlation_id: {e}")
            if e.code() == grpc.StatusCode.NOT_FOUND:
                return {
                    "success": False,
                    "message": e.details(),
                    "data": [],
                    "metadata": {}
                }
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "data": [],
                "metadata": {}
            }
        except Exception as e:
            logger.error(f"Error listing activity logs by correlation_id: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "data": [],
                "metadata": {}
            }

# Create a global instance
activity_service = ActivityServiceClient()