"""
Test suite for marketplace authentication routes in API Gateway
Following TDD approach: Write tests first, then implement
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status
import json

# Import the app and dependencies we'll implement
from app.main import app
from app.services.workflow_service import WorkflowServiceClient
from app.services.user_service import UserServiceClient


class TestMarketplaceAuthRoutes:
    """Test cases for marketplace authentication routes"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_workflow_with_auth(self):
        """Mock workflow that requires authentication"""
        return {
            "id": "workflow-123",
            "name": "AI Content Pipeline",
            "env_credential_status": "pending_input",
            "credential_summary": {
                "total_requirements": 2,
                "by_provider": {
                    "openai": {
                        "count": 15,
                        "types": ["api_key"],
                        "required": True,
                        "fields": ["openai_api_key"]
                    }
                },
                "estimated_setup_time": 15,
                "analysis_version": 2,
                "last_analyzed": "2025-01-28T10:00:00Z",
                "credential_requirements": [
                    {
                        "credential_type": "api_key",
                        "field_name": "openai_api_key",
                        "is_required": True,
                        "component_count": 15,
                        "provider_name": "openai"
                    }
                ]
            }
        }
    
    @pytest.fixture
    def mock_workflow_no_auth(self):
        """Mock workflow that doesn't require authentication"""
        return {
            "id": "workflow-456",
            "name": "Simple Text Processor",
            "env_credential_status": "not_required",
            "credential_summary": None
        }

    @pytest.fixture
    def mock_user_coverage(self):
        """Mock user credential coverage"""
        return {
            "total_requirements": 2,
            "available_credentials": 1,
            "missing_credentials": 1,
            "coverage_percentage": 50.0,
            "detailed_status": [
                {
                    "field_name": "openai_api_key",
                    "status": "available",
                    "credential_id": "cred_123",
                    "credential_name": "My OpenAI Key"
                },
                {
                    "field_name": "github_token",
                    "status": "missing",
                    "credential_id": None,
                    "credential_name": None
                }
            ]
        }

    @patch('app.services.workflow_service.WorkflowServiceClient')
    @patch('app.services.user_service.UserServiceClient')
    def test_import_workflow_with_auth_analyze_action(
        self, 
        mock_user_service_class,
        mock_workflow_service_class,
        client, 
        mock_workflow_with_auth,
        mock_user_coverage
    ):
        """Test POST /marketplace/workflows/{id}/import-with-auth with analyze action"""
        # Arrange
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=mock_workflow_with_auth)
        mock_workflow_service_class.return_value = mock_workflow_service
        
        mock_user_service = Mock()
        mock_user_service.batch_check_user_coverage = AsyncMock(return_value=mock_user_coverage)
        mock_user_service_class.return_value = mock_user_service
        
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["action"] == "analyzed"
        assert "credential_summary" in data
        assert "user_coverage" in data
        assert data["user_coverage"]["coverage_percentage"] == 50.0

    @patch('app.services.workflow_service.WorkflowServiceClient')
    @patch('app.services.user_service.UserServiceClient')
    def test_import_workflow_with_auth_import_action(
        self,
        mock_user_service_class,
        mock_workflow_service_class,
        client,
        mock_workflow_with_auth,
        mock_user_coverage
    ):
        """Test POST /marketplace/workflows/{id}/import-with-auth with import action"""
        # Arrange
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=mock_workflow_with_auth)
        mock_workflow_service.import_with_credentials = AsyncMock(return_value={
            "workflow_id": "imported-workflow-789",
            "status": "success"
        })
        mock_workflow_service_class.return_value = mock_workflow_service
        
        mock_user_service = Mock()
        mock_user_service.batch_check_user_coverage = AsyncMock(return_value=mock_user_coverage)
        mock_user_service_class.return_value = mock_user_service
        
        request_data = {
            "action": "import",
            "credential_mapping": {
                "openai_api_key": {
                    "credential_id": "cred_123",
                    "credential_name": "My OpenAI Key"
                }
            }
        }
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["action"] == "imported"
        assert data["workflow_id"] == "imported-workflow-789"
        assert "credential_summary" in data
        assert "user_coverage" in data

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_import_workflow_with_auth_workflow_not_found(
        self, 
        mock_workflow_service_class,
        client
    ):
        """Test POST /marketplace/workflows/{id}/import-with-auth for non-existent workflow"""
        # Arrange
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=None)
        mock_workflow_service_class.return_value = mock_workflow_service
        
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/non-existent/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_import_workflow_with_auth_no_credential_summary(
        self, 
        mock_workflow_service_class,
        client
    ):
        """Test POST /marketplace/workflows/{id}/import-with-auth for workflow without credential analysis"""
        # Arrange
        workflow_without_analysis = {
            "id": "workflow-123",
            "env_credential_status": "not_required",
            "credential_summary": None
        }
        
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=workflow_without_analysis)
        mock_workflow_service_class.return_value = mock_workflow_service
        
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "credential analysis not available" in data["detail"].lower()

    def test_import_workflow_with_auth_invalid_action(self, client):
        """Test POST /marketplace/workflows/{id}/import-with-auth with invalid action"""
        # Arrange
        request_data = {"action": "invalid_action"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_import_workflow_with_auth_missing_auth_header(self, client):
        """Test POST /marketplace/workflows/{id}/import-with-auth without authentication"""
        # Arrange
        request_data = {"action": "analyze"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('app.services.workflow_service.WorkflowServiceClient')
    @patch('app.services.user_service.UserServiceClient')
    def test_import_workflow_with_auth_import_failure(
        self,
        mock_user_service_class,
        mock_workflow_service_class,
        client,
        mock_workflow_with_auth
    ):
        """Test POST /marketplace/workflows/{id}/import-with-auth when import fails"""
        # Arrange
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=mock_workflow_with_auth)
        mock_workflow_service.import_with_credentials = AsyncMock(side_effect=Exception("Import failed"))
        mock_workflow_service_class.return_value = mock_workflow_service
        
        mock_user_service = Mock()
        mock_user_service.batch_check_user_coverage = AsyncMock(return_value={})
        mock_user_service_class.return_value = mock_user_service
        
        request_data = {
            "action": "import",
            "credential_mapping": {}
        }
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-123/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch('app.services.workflow_service.WorkflowServiceClient')
    @patch('app.services.user_service.UserServiceClient')
    def test_import_workflow_no_auth_required(
        self,
        mock_user_service_class,
        mock_workflow_service_class,
        client,
        mock_workflow_no_auth
    ):
        """Test importing workflow that doesn't require authentication"""
        # Arrange
        mock_workflow_service = Mock()
        mock_workflow_service.get_workflow_with_auth_summary = AsyncMock(return_value=mock_workflow_no_auth)
        mock_workflow_service.import_with_credentials = AsyncMock(return_value={
            "workflow_id": "imported-workflow-456",
            "status": "success"
        })
        mock_workflow_service_class.return_value = mock_workflow_service
        
        mock_user_service = Mock()
        mock_user_service.batch_check_user_coverage = AsyncMock(return_value={
            "total_requirements": 0,
            "available_credentials": 0,
            "missing_credentials": 0,
            "coverage_percentage": 100.0,
            "detailed_status": []
        })
        mock_user_service_class.return_value = mock_user_service
        
        request_data = {"action": "import"}
        
        # Act
        response = client.post(
            "/api/v1/marketplace/workflows/workflow-456/import-with-auth",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["action"] == "imported"
        assert data["workflow_id"] == "imported-workflow-456"
        assert data["user_coverage"]["coverage_percentage"] == 100.0
