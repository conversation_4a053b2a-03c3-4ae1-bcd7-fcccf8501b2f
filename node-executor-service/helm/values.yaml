# Application name, set by the pipeline
name: my-app

# Set to null if HPA is enabled
replicaCount: null

image:
  name: nginx:latest  # Overridden by pipeline
  pullPolicy: IfNotPresent

serviceAccount:
  create: true

revisionHistoryLimit: 2

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 25%
    maxSurge: 25%

resources:
  requests:
    memory: "640Mi"
    cpu: "250m"
  limits:
    memory: "1Gi"
    cpu: "500m"

livenessProbe:
  enabled: false
  type: http  # Set to 'grpc' if using gRPC
  http:
    path: /
  # grpc:
  #   port: 9090
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: false
  type: http  # Set to 'grpc' if using gRPC
  http:
    path: /
  # grpc:
  #   port: 9090
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 10
  failureThreshold: 3

nodeSelector:
  enabled: false
  values:
    dedicated: livekit

tolerations:
  enabled: false
  values:
    - effect: NoSchedule
      key: dedicated
      operator: Equal
      value: livekit

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 60
  # targetMemoryUtilizationPercentage: 70

service:
  type: ClusterIP
  port: 80
  targetPort: 50052
  sessionAffinity:
    enabled: false
    timeoutSeconds: 10800
    
ingress:
  enabled: false
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
  hosts:
    - host: my-app.example.com
      paths:
        - path: /
          pathType: Prefix
  tls: []

externalSecret:
  enabled: true
  refreshInterval: "1m"  # Overridden by pipeline
  gcpStore: gcp-store
  gcpSecretName: ""  # Overridden by pipeline