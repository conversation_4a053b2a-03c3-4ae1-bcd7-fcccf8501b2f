2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "correlation_id": "34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "correlation_id": "34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message:713] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Executing tool CombineTextComponent for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:94] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Executing tool for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:97] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "correlation_id": "34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "correlation_id": "34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:111] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Tool name: CombineTextComponent for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:144] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Processing payload with component CombineTextComponent for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'input_1': 10, 'main_input': 'ran', 'num_additional_inputs': 1, 'separator': ' ', 'request_id': '8cee39cd-ed63-4a7b-8f71-64356486b72d'}
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id 8cee39cd-ed63-4a7b-8f71-64356486b72d. Separator: ' ', Num additional inputs: 1
2025-07-30 13:49:06 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id 8cee39cd-ed63-4a7b-8f71-64356486b72d. Result length: 6
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:148] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Component CombineTextComponent processed payload successfully for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool:154] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] ToolExecutor returning raw component result for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message:717] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Tool CombineTextComponent executed successfully for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result:1007] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Preparing to send result for component ApiRequestNode, RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result:1105] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Sending Kafka response: RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, Response={
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863546.077533,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 10",
  "error": null
}
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d, Response={
  "request_id": "8cee39cd-ed63-4a7b-8f71-64356486b72d",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863546.077533,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 10",
  "error": null
}
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result:1121] [ReqID:bd009c39-9e7d-4ef0-9e77-612012abcb42] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Sent result for component ApiRequestNode to topic node_results for RequestID=bd009c39-9e7d-4ef0-9e77-612012abcb42
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=bd009c39-9e7d-4ef0-9e77-612012abcb42
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result:1121] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Sent result for component ApiRequestNode to topic node_results for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=8cee39cd-ed63-4a7b-8f71-64356486b72d
2025-07-30 13:49:06 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:bd009c39-9e7d-4ef0-9e77-612012abcb42] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Successfully committed offset 2028 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2027-1753863546.06948
2025-07-30 13:49:06 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2028 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2027-1753863546.06948
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message:936] [ReqID:bd009c39-9e7d-4ef0-9e77-612012abcb42] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2027, TaskID=ApiRequestNode-node-execution-request-0-2027-1753863546.06948
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2027, TaskID=ApiRequestNode-node-execution-request-0-2027-1753863546.06948
2025-07-30 13:49:06 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Successfully committed offset 2029 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2029 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message:936] [ReqID:8cee39cd-ed63-4a7b-8f71-64356486b72d] [CorrID:34eeacd7-adce-4192-8193-6d11a47ff8bd-1753863488378354] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2028, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:49:06 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2028, TaskID=ApiRequestNode-node-execution-request-0-2028-1753863546.069531
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2029, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2029, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824, Payload={
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "a": "9"
    },
    "data_type": "Auto-Detect",
    "search_mode": "Exact Path",
    "field_matching_mode": "Auto-detect",
    "selector": "a"
  },
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data"
}
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824, Payload={
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "a": "9"
    },
    "data_type": "Auto-Detect",
    "search_mode": "Exact Path",
    "field_matching_mode": "Auto-detect",
    "selector": "a"
  },
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data"
}
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message:713] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool SelectDataComponent for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message] Executing tool SelectDataComponent for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:94] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:97] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor received payload: {
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "a": "9"
    },
    "data_type": "Auto-Detect",
    "search_mode": "Exact Path",
    "field_matching_mode": "Auto-detect",
    "selector": "a"
  },
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data"
}
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "a": "9"
    },
    "data_type": "Auto-Detect",
    "search_mode": "Exact Path",
    "field_matching_mode": "Auto-detect",
    "selector": "a"
  },
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "node_label": "Select Data"
}
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:111] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool name: SelectDataComponent for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] Tool name: SelectDataComponent for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:144] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing payload with component SelectDataComponent for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] Processing payload with component SelectDataComponent for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process:345] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing select data request for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process] Processing select data request for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process:359] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process:423] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Data selected successfully for request_id 8d3a2976-47d2-4ec9-9cab-990b7e089b46. Result type: str
2025-07-30 13:50:43 - SelectDataComponent - INFO - [process] Data selected successfully for request_id 8d3a2976-47d2-4ec9-9cab-990b7e089b46. Result type: str
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:148] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Component SelectDataComponent processed payload successfully for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] Component SelectDataComponent processed payload successfully for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool:154] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor returning raw component result for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message:717] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool SelectDataComponent executed successfully for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message] Tool SelectDataComponent executed successfully for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result:1007] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Preparing to send result for component ApiRequestNode, RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result:1105] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sending Kafka response: RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, Response={
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863643.1330209,
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "result": {
    "output_data": "9"
  },
  "error": null
}
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46, Response={
  "request_id": "8d3a2976-47d2-4ec9-9cab-990b7e089b46",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863643.1330209,
  "transition_id": "transition-SelectDataComponent-1753852841462",
  "result": {
    "output_data": "9"
  },
  "error": null
}
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result:1121] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sent result for component ApiRequestNode to topic node_results for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=8d3a2976-47d2-4ec9-9cab-990b7e089b46
2025-07-30 13:50:43 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Successfully committed offset 2030 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2030 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message:936] [ReqID:8d3a2976-47d2-4ec9-9cab-990b7e089b46] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2029, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:43 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2029, TaskID=ApiRequestNode-node-execution-request-0-2029-1753863643.124824
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2030, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2030, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 7,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 7,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool CombineTextComponent for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 7,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 7,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:111] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool name: CombineTextComponent for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:144] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing payload with component CombineTextComponent for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'input_1': 7, 'main_input': 'ran', 'num_additional_inputs': 1, 'separator': ' ', 'request_id': '98e7fc22-5746-4778-a25a-5fc54173456d'}
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id 98e7fc22-5746-4778-a25a-5fc54173456d. Separator: ' ', Num additional inputs: 1
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id 98e7fc22-5746-4778-a25a-5fc54173456d. Result length: 5
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:148] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Component CombineTextComponent processed payload successfully for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:154] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor returning raw component result for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool CombineTextComponent executed successfully for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1007] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Preparing to send result for component ApiRequestNode, RequestID=98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1105] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sending Kafka response: RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, Response={
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.6211,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 7",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=98e7fc22-5746-4778-a25a-5fc54173456d, Response={
  "request_id": "98e7fc22-5746-4778-a25a-5fc54173456d",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.6211,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 7",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2031, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2031, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 8,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 8,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool CombineTextComponent for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 8,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 8,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:111] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool name: CombineTextComponent for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:144] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing payload with component CombineTextComponent for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'input_1': 8, 'main_input': 'ran', 'num_additional_inputs': 1, 'separator': ' ', 'request_id': 'a23f6e2b-01fe-4ee1-ac88-c535156b2d48'}
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id a23f6e2b-01fe-4ee1-ac88-c535156b2d48. Separator: ' ', Num additional inputs: 1
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id a23f6e2b-01fe-4ee1-ac88-c535156b2d48. Result length: 5
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:148] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Component CombineTextComponent processed payload successfully for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:154] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor returning raw component result for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool CombineTextComponent executed successfully for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1007] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Preparing to send result for component ApiRequestNode, RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1105] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sending Kafka response: RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, Response={
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.62242,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 8",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48, Response={
  "request_id": "a23f6e2b-01fe-4ee1-ac88-c535156b2d48",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.62242,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 8",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2032, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2032, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 9,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 9,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool CombineTextComponent for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 9,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 9,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:111] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool name: CombineTextComponent for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:144] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing payload with component CombineTextComponent for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'input_1': 9, 'main_input': 'ran', 'num_additional_inputs': 1, 'separator': ' ', 'request_id': 'bccc27dd-18cd-4152-b722-ce88a66992ca'}
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id bccc27dd-18cd-4152-b722-ce88a66992ca. Separator: ' ', Num additional inputs: 1
2025-07-30 13:50:49 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id bccc27dd-18cd-4152-b722-ce88a66992ca. Result length: 5
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:148] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Component CombineTextComponent processed payload successfully for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool:154] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor returning raw component result for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool CombineTextComponent executed successfully for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1007] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Preparing to send result for component ApiRequestNode, RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1105] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sending Kafka response: RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, Response={
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.623667,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 9",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca, Response={
  "request_id": "bccc27dd-18cd-4152-b722-ce88a66992ca",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863649.623667,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 9",
  "error": null
}
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1121] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sent result for component ApiRequestNode to topic node_results for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=98e7fc22-5746-4778-a25a-5fc54173456d
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1121] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sent result for component ApiRequestNode to topic node_results for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=a23f6e2b-01fe-4ee1-ac88-c535156b2d48
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result:1121] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sent result for component ApiRequestNode to topic node_results for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:49 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=bccc27dd-18cd-4152-b722-ce88a66992ca
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Successfully committed offset 2031 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2031 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message:936] [ReqID:98e7fc22-5746-4778-a25a-5fc54173456d] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2030, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2030, TaskID=ApiRequestNode-node-execution-request-0-2030-1753863649.619779
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Successfully committed offset 2032 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2032 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message:936] [ReqID:a23f6e2b-01fe-4ee1-ac88-c535156b2d48] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2031, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2031, TaskID=ApiRequestNode-node-execution-request-0-2031-1753863649.6198049
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Successfully committed offset 2033 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:50 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2033 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message:936] [ReqID:bccc27dd-18cd-4152-b722-ce88a66992ca] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2032, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:50 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2032, TaskID=ApiRequestNode-node-execution-request-0-2032-1753863649.6198149
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2033, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2033, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message:713] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool CombineTextComponent for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:94] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Executing tool for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:97] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "input_1": 10,
    "main_input": "ran",
    "num_additional_inputs": 1,
    "separator": " "
  },
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "correlation_id": "79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867",
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "node_label": "Combine Text"
}
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:111] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool name: CombineTextComponent for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:144] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Processing payload with component CombineTextComponent for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'input_1': 10, 'main_input': 'ran', 'num_additional_inputs': 1, 'separator': ' ', 'request_id': 'cbc78c21-f3dd-46ce-8924-12343aa8b076'}
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['input_1', 'main_input', 'num_additional_inputs', 'separator', 'request_id']
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id cbc78c21-f3dd-46ce-8924-12343aa8b076. Separator: ' ', Num additional inputs: 1
2025-07-30 13:50:56 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id cbc78c21-f3dd-46ce-8924-12343aa8b076. Result length: 6
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:148] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Component CombineTextComponent processed payload successfully for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool:154] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] ToolExecutor returning raw component result for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message:717] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Tool CombineTextComponent executed successfully for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result:1007] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Preparing to send result for component ApiRequestNode, RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result:1105] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sending Kafka response: RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, Response={
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863656.020992,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 10",
  "error": null
}
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076, Response={
  "request_id": "cbc78c21-f3dd-46ce-8924-12343aa8b076",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1753863656.020992,
  "transition_id": "transition-CombineTextComponent-1753852886677",
  "result": "ran 10",
  "error": null
}
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result:1121] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Sent result for component ApiRequestNode to topic node_results for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=cbc78c21-f3dd-46ce-8924-12343aa8b076
2025-07-30 13:50:56 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Successfully committed offset 2034 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 2034 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message:936] [ReqID:cbc78c21-f3dd-46ce-8924-12343aa8b076] [CorrID:79dc4de1-8f50-4961-9652-e0f92f173ad8-1753863638589867] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2033, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
2025-07-30 13:50:56 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=2033, TaskID=ApiRequestNode-node-execution-request-0-2033-1753863656.018555
